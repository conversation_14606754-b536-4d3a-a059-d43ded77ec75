# 足球竞彩-手机端

#### Description

{**When you're done, you can delete the content in this README and update the file with details for others getting started with your repository**}

#### Software Architecture

Software architecture description

#### Installation

1.  xxxx
2.  xxxx
3.  xxxx

#### Instructions

1.  xxxx
2.  xxxx
3.  xxxx

#### Contribution

1.  Fork the repository
2.  Create Feat_xxx branch
3.  Commit your code
4.  Create Pull Request

#### Gitee Feature

1.  You can use Readme_XXX.md to support different languages, such as Readme_en.md, Readme_zh.md
2.  Gitee blog [blog.gitee.com](https://blog.gitee.com)
3.  Explore open source project [https://gitee.com/explore](https://gitee.com/explore)
4.  The most valuable open source project [GVP](https://gitee.com/gvp)
5.  The manual of Gitee [https://gitee.com/help](https://gitee.com/help)
6.  The most popular members [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)

#### git提交规范

- feat, fix, perf, style, docs, test, refactor, build, ci, chore, revert, wip, workflow, types, release
