<script setup lang="ts">
import { onLaunch } from '@dcloudio/uni-app'
import { getCurrentInstance } from 'vue'
import { useUserStore } from './store'
import { wxLogin, miniLogin } from './service/login'
import { getUserInfo } from './service/userService'
import { storeToRefs } from 'pinia'
import { initWxJsSdk } from './utils/wxJsSdk'
import { authCheck } from '@/utils/authCheck'

const {
  // @ts-ignore
  proxy: { $isResolve },
} = getCurrentInstance()
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const code = ref<null | string>(null)
const isLogined = computed(() => userStore.isLogined)
const params = ref()

async function login() {
  if (code.value && !isLogined.value) {
    // 判断当前页面是否是个人中心页面 /pages/myInfo/index 如果是就不进行登录操作
    const pathname = window.location.pathname
    if (pathname === '/pages/myInfo/index') {
      return
    }
    const loginResult = await wxLogin(params.value)
    // console.log('登录结果', loginService.value.accessToken)
    userStore.setToken(
      loginResult.accessToken.accessToken,
      loginResult.accessToken.refreshToken,
      null,
    )

    const userInfoResult = await getUserInfo()
    userStore.setUserInfo(userInfoResult)

    // 登录的时候根据用户author字段，先设置用户的默认角色(作者或读者)
    console.log('author', userInfoResult.author)
    userStore.changeRole(!!userInfoResult.author)

    $isResolve()
    if (userInfo.value.showPartner) uni.navigateTo({ url: '/pages/partner/invitation' })
  }
}

onMounted(() => {
  if (!userStore.userInfo) {
    userStore.clearUserInfo()
  }

  // #ifdef H5
  const pathname = location.pathname
  if (pathname !== '/pages/error/404') {
    setTimeout(() => {
      initWxJsSdk()
    }, 500)
  }
  // #endif
})

onShow(async (e) => {
  const { query } = e

  // 查看权限校验
  // await authCheck(query)
})

// 登录拦截 从微信认证跳转回来
onLaunch(async (e) => {
  const { query } = e

  // 查看权限校验
  await authCheck(query)

  if (query.code) {
    code.value = query.code
    params.value = query
    login()
  } else {
    $isResolve()
  }
})
</script>

<style lang="scss">
/* stylelint-disable selector-type-no-unknown */
uni-page-body,
html,
body {
  height: 100% !important;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.wot-theme-light {
  height: 100%;
}

button::after {
  border: none;
}

:root,
page {
  height: 100vh;
  --wot-button-normal-bg: #d1302e;
  --wot-button-primary-bg-color: #d1302e;
  --wot-calendar-active-color: #d1302e;
  --wot-color-theme: #d1302e;
  font-family: PingFang SC;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.wd-fab {
  z-index: 2 !important;
}

.sy-segmented {
  :deep(.wd-segmented__item.is-active .wd-segmented__item-label) {
    color: #d1302e;
  }
}
</style>
