import { GAME_PLAY_TYPE, HOME_ARTICLE_PLAY, MATCH_STATUS, WIN_TYPE } from '@/utils/enum'
import { http } from '@/utils/http'
import { ConfigType } from 'dayjs'
import { IOdds } from './match'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'

/* 文章列表 */
export interface IArticle {
  id: number
  title: string
  intro: string
  createTime: number
  win: number
  winName: string
  top: 0 | 1 // 0 否 1 是
  isNew: 0 | 1
  refundType: 0 | 1
  price: number
  conclusion: string
}

export interface PageResult<T> {
  list: T[]
  total: number
}

export interface IArticleRank {
  articleId: number
  title: string
  count: number
  amount: number
  createTime: string
}
export const ARTICLE_DEFAULT_PAGE = 5

/*
 * 方案列表
 * type 0 最新(在售) 1 历史
 */
export function getSchemeListByAuthorId(
  authorId: number,
  pageNo: number,
  type = 0,
  pageSize = DEFAULT_PAGE_SIZE,
): Promise<{ list: IArticle[]; total: number }> {
  return http.get('/app-api/home/<USER>', { pageNo, pageSize, authorId, type })
}

interface IAuthorPackageTag {
  id: number
  type: number // 0 包天 1 包次 3 套餐包
  days: number
  price: number
}

export interface IAuthorPackage {
  id: number
  privilegeName: string
  children: IAuthorPackageTag[] | null
  totalArticleCount: number
  date: number | null // 每日更新文章数量
  price: number
  type: number
}

/*
 * 根据作者id获取作者套餐列表(带分页)
 */
export function getPackageListByAuthorId(authorId: number): Promise<IPackageDetail[]> {
  return http.get('/app-api/home/<USER>', { authorId })
}

export interface IPackageDetail extends IAuthorPackage {
  authorId: number
  authorName: string
  content: string
  hasBuy: number
  avatarUrl: string
  totalArticle: { id: number; title: string; intro: string }[]
}

export function getPackageDetailById(id: number): Promise<IPackageDetail> {
  return http.get(`/app-api/member/privilege/get/${id}`)
}

/* 热门文章列表(在售) */
export function getHotArticleList(
  pageNo: number,
  pageSize = ARTICLE_DEFAULT_PAGE,
): Promise<{ list: IArticle[]; total: number }> {
  return http.get('/app-api/home/<USER>', { pageNo, pageSize })
}

export interface IPackage {
  privilegeName: string
  price: number
  id: number
}

/* 热门文章列表(套餐) */
export function getAuthorPrivilege(authorId: number, type: number): Promise<IPackage[]> {
  return http.get('/app-api/home/<USER>', { authorId, type })
}

interface IArticleRecord {
  id: number
  title: string
  intro: string
  freeContents: string
  contents: string
  authorName: string
  authorAvatar: string
  authorId: number
  isAttention: number // 是否关注作者(0 未关注 1 已关注)
  createTime: ConfigType
  buyNum: number
  price: number
  refundType: number // 不中退款(0 是 1 否)
  win: number // 是否中奖(0 未知 1 是 2 否)
  winName: string
  winExc: number
  isBuy: number // 是否购买(0 未购买 1 已购买)
  privilegeStatus: number
  privilegeEndDate: number
  privilegeNum: number // 剩余补单次数
  privilegeType: number //特权类型：0：包时套餐 1：次数套餐 2:免费补单
  conclusion: string
  showQQ?: boolean // 是否显示更新qq
  competitionName: string
  matchTime: number
  homeTeamLogo: string
  homeTeamName: string
  awayTeamLogo: string
  awayTeamName: string
  betScore: string
  betType: number
  matchStatus: number
  aggScore: string
  matchId: number
  attentionMatch: number
}

export interface IArticleMatchInfo {
  matchId: number
  statusId: MATCH_STATUS
  homeTeamLogo: string
  awayTeamLogo: string
  competitionName: string // 联赛名
  matchTime: number
  homeScore: number
  awayScore: number
  homeHalfScore: number
  awayHalfScore: number
  homeCorner: number
  awayCorner: number
  issueNum: string | null
  sellOutTime: string | null
}

/* 文章详情 */
export interface IArticleDetail extends IArticleRecord {
  accomplishment: number
  authorAvatar: string // 作者头像
  schemePlay: number
  recommend: IArticleRecord
  autoReplacement: 0 | 1 // 自动补单
  matchScheme: string // IMatchPlayItem类型的字符串数组
  canEdit: number // 是否可以编辑(0 不能 1 可以)
  endTime: Date
  shareQrCode: string
  status: number // 文章状态(0 下架 1 发布)
  consumeStatus: -1 | 0 | 1 // -1 否 0 全部 1 是
  consumeMinNum: number | null // 消费最低次数
  consumeMaxNum: number | null // 消费最高次数
  consumeMinAmount: number | null // 最低消费金额
  consumeMaxAmount: number | null // 最高消费金额
  top: number // 是否置顶(0 否 1 是)
  addFreeContent: string
  addContent: string
  articleAppendFreeList: Array<any>
  articleAppendList: Array<any>
  currentResults: Array<number> // 近期战绩
  currentResultsName: Array<string> // 近期战绩名称
  issue: string // 期数(14场与任九)
  articleMatchInfo: IArticleMatchInfo[]
  [key: string]: any
  accomplishmentShowPic: string // 战绩图片
  topBg: string
}

export interface IArticlePushConfig {
  articleId: number
  consumeStatus: -1 | 0 | 1
  consumeMinNum: number | null
  consumeMaxNum: number | null
  consumeMinAmount: number | null
  consumeMaxAmount: number | null
  templateId: number | null
  pushTimeList: ConfigType[]
}

export interface IMatchResult {
  playId: number
  type: number // 0 主玩玩法 1 附赠玩法
  result: any // 比赛结果
  resultType: GAME_PLAY_TYPE
  opinion: number
  articleMatchInfo?: IArticleMatchInfo
}

export interface IMatchSchem {
  matchId: number
  homeName: string
  awayName: string
  odds: IOdds
  opinion: number // 0 还未判别 1 命中 2 未命中
  matchTime: number
  matchPlays: IMatchResult[]
}

export interface IMatchSchemItem extends Omit<IMatchSchem, 'odds'> {
  dataType: number
  roundName: string
  round: string
}

export function getArticleDetail(id: number): Promise<IArticleDetail> {
  return http.get(`/app-api/article/get/${id}`)
}

export function checkArticleStatus(id: number): Promise<any> {
  return http.get(`/app-api/article/check-article-status`, { id })
}

export function updateArticleShareInfo(data): Promise<boolean> {
  return http.post(`/app-api/author/updateSharePic`, data)
}

export function getArticleListById(id: number): Promise<any> {
  return http.get(`/app-api/article/list`, { id })
}

/* 关注作者 */
export function addAuthorAttention(authorId: number): Promise<boolean> {
  return http.get('/app-api/member/attention/add', { authorId })
}

/* 取消关注作者 */
// /app-api/member/attention/cancel
export function removeAutionAttention(authorId: number): Promise<boolean> {
  return http.get('/app-api/member/attention/cancel', { authorId })
}

/* 是否显示接受推送 */
export function canUserShowPush(authorId: number): Promise<boolean> {
  return http.get('/app-api/article/showPush', { authorId })
}

/* 是否显示接受推送 */
export function showAttention(authorId: number): Promise<boolean> {
  return http.get('/app-api/article/showAttention', { authorId })
}

export function changeTopStatus(data): Promise<boolean> {
  return http.post('/app-api/author/top-status', data)
}

export function changeArticleStatus(data): Promise<boolean> {
  return http.post('/app-api/author/article-status', data)
}

export function getExtraArticleList(id): Promise<Array<any>> {
  return http.get('/app-api/article/getExtraArticleList', { id })
}

export function pushExtraArticle(data): Promise<boolean> {
  return http.post('/app-api/article/pushExtraArticle', data)
}

export function getArticlePushConfig(id: number): Promise<IArticlePushConfig[]> {
  return http.get('/app-api/author/getArticlePushConfig', { id })
}

/* 设置公开 */

interface IArticleConsumeParams extends Partial<IArticlePushConfig> {
  articleId: number
  consumeStatus: -1 | 0 | 1
}
export function updateArticleConsumeInfo(data: IArticleConsumeParams) {
  return http.post('/app-api/author/updateConsumeInfo', data)
}

/* 设置推送 */
interface IArticlePushParams extends Partial<IArticlePushConfig> {
  articleId: number
  consumeStatus: -1 | 0 | 1
  pushTimeList: ConfigType[]
}
export function updateArticlePushConfig(data: IArticlePushParams[]) {
  return http.post('/app-api/author/updateArticlePushConfigs', data)
}

export function getArticleRankPage(params): Promise<PageResult<IArticleRank>> {
  return http.get('/app-api/article/getArticleRank', params)
}

export interface IAuthorArticleParams {
  title: string
  dateTime: string
  type: 0 | 1 // 0 未判定 1 已判定
  pageNo: number
  pageSize: number
}

export interface IAuthorArticle {
  id: number
  matchIds: string
  title: string
  createTime: number // 发布时间
  win: WIN_TYPE // 红黑 0：未结束 1：红 2:黑
  winName: string
  winExc: number
  top: number // 置顶 0：否 1：是
  isNew: number // 新 0：否 1：是
  price: number // 文章价格
  conclusion: string // 结语
  refundType: number // 是否不同击退： 0：否 1：是
  viewCount: number // 浏览量
  buyCount: number // 购买量
  status: number
  homeName: string // 主队名称
  awayName: string // 客队名称
  authorDivide: number // 收款总额
}

/* 作者文章列表 */
export function getAuthorArticles(
  params: Partial<IAuthorArticleParams>,
): Promise<{ list: IAuthorArticle[]; total: number }> {
  return http.get('/app-api/author/article-page', params)
}

export interface IArticleScheme {
  id: number
  avatarUrl: string // 用户头像
  authorName: string // 用户昵称
  recentWinCount: number // 进几中几
  hitRate: number // 命中率
  winCount: number // 连红
  title: string // 标题
  createTime: number // 时间
  buyCount: number // 购买人数
  price: number // 价格
}

/* 根据比赛id获取文章列表 */
export function getArticlesByMatchId(
  matchId: number | string,
  pageNo: number,
  pageSize: number,
): Promise<{ list: IArticleScheme[]; total: number }> {
  return http.get('/app-api/author/getArticleListByMatchId', { matchId, pageNo, pageSize })
}

export interface IHomeArticleItem {
  id: number
  authorId: number
  authorName: string
  avatarUrl: string
  winCount: number // 连中次数
  recentWinCount: string // 最近连中数
  hitRate: number // 命中率(转换为百分比需要乘以100)
  title: string // 文章标题
  createTime: number // 发布时间
  schemePlay: number // 方案玩法
  matchScheme: string // IMatchResult 字符串
  buyCount: number // 购买量
  price: number // 价格
}

/* 获取首页文章列表 */
export function getHomeArticleList(
  type,
  pageNo: number,
  pageSize = DEFAULT_PAGE_SIZE,
  playType?: HOME_ARTICLE_PLAY,
): Promise<{ list: IHomeArticleItem[]; total: number }> {
  return http.get('/app-api/home/<USER>', { type, playType, pageNo, pageSize })
}

export function complaintCreate(data): Promise<any> {
  return http.post(`/app-api/article/complaint/create`, data)
}

export function getComplaintList(): Promise<any> {
  return http.get(`/app-api/article/complaint/getComplaintList`)
}

export function getOrcInfo(
  ocrContent: string,
): Promise<{ matchScheme: string; schemePlay: number; [key: string]: any }> {
  return http.post('/app-api/article/getOcrInfo', { ocrContent })
}
