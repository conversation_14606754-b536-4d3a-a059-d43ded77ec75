import { FOCUSED_AUTHOR_TYPE, SCHEME_TYPE } from '@/utils/enum'
import { http } from '@/utils/http'

interface shareInfo {
  title?: string
  username: string
  price?: string
  qrcode: string
  avatar: string
}

interface IArticlePublishData {
  draft: 0 | 1
  id?: number // 发布文章id，新增或首次保存为草稿不需要
  title: string | null // 文章标题
  intro: string | null // 文章简介
  freeContents: string | null // 免费内容
  contents: string | null // 付费内容
  refundType: 0 | 1 // 不中即退 1 否 2 是
  autoReplacement: 0 | 1 // 是否可补单 1 是 0 否
}

export function publishArticle(data: Partial<IArticlePublishData>): Promise<IResData<number>> {
  return http.post('/app-api/author/publish', data)
}

export function appendArticle(data): Promise<IResData<number>> {
  return http.post('/app-api/author/articleAppend', data)
}

export function getArticleShareInfo(id): Promise<IResData<shareInfo>> {
  return http.get('/app-api/author/getArticleShareInfo', { id })
}

export function getAuthorShareInfo(id): Promise<IResData<shareInfo>> {
  return http.get('/app-api/author/getAuthorShareInfo', { id })
}

export function getArticleShareImg(id): Promise<IResData<string>> {
  return http.get('/app-api/article/share', { id })
}

export function getArticleInfo(id): Promise<IResData<any>> {
  return http.get('/app-api/author/info', { id })
}

export function getMatchList(): Promise<any> {
  return http.get('/app-api/author/matchList')
}

export function deletedArticle(id): Promise<IResData<any>> {
  return http.post(`/app-api/author/deleteArticle/${id}`)
}

export function getAuthorPrivilegeDetail(authorId): Promise<IResData<any>> {
  return http.get('/app-api/author/privilegeDetail/', { authorId })
}

export function getUserPrivilegeList(params): Promise<IResData<any>> {
  return http.get('/app-api/author/getUserPrivilegeList', params)
}

export function getUserPrivilegeArticleList(params): Promise<IResData<any>> {
  return http.get('/app-api/author/getUserPrivilegeArticleList', params)
}

export function getExOrderAuthorDetail(params): Promise<IResData<any>> {
  return http.get('/app-api/scheme-order/exOrderAuthorDetail', params)
}

export interface IAuthorStatistic {
  todayIncome: number // 今日收益
  totalIncome: number // 总收入
  todayConsumer: number // 今日购买人数
  totalConsumer: number // 总消费人数
  todayPrivilegeIncome: number // 今日发布方案收入
  todayArticleIncome: number // 总发布方案收入
}

/* 作者首页统计信息 */
export function getAuthorStatistic(): Promise<IAuthorStatistic> {
  return http.get('/app-api/author/getIncomeCount')
}

export function getEditorType(): Promise<number> {
  return http.get('/app-api/author/getEditorType')
}

export function getAuthorPrivilegeUser(params): Promise<IResData<any>> {
  return http.get('/app-api/member/auth/getAuthorPrivilegeUser', params)
}

export function getExOrderRecords(params): Promise<IResData<any>> {
  return http.get('/app-api/scheme-order/exOrderRecords', params)
}

export function getExOrderDetail(params): Promise<IResData<any>> {
  return http.get('/app-api/scheme-order/exOrderDetail', params)
}

export function getPrivilegeQrCode(id): Promise<string> {
  return http.get('/app-api/author/getPrivilegeQrCode', { privilegeId: id })
}

export interface IScheme {
  id: number
  name: string
  dataType: number
  multiple: 0 | 1
  session: null | number
  play: string
  bundled: 0 | 1 // 附赠玩法 1为有附赠玩法
}

/* 获取所有玩法 */
export function getGameplay(): Promise<IScheme[]> {
  return http.get('/app-api/author/getSchemePlayList')
}

export interface IGamePlayItem {
  id: number
  name: string
  label: string
  resultType: number
  resultNum: number
  value: number
}

export type IGamePlayRecord = Omit<IGamePlayItem, 'label' | 'value'>

/* 根据玩法id查询方案玩法 */
export function getPlayMethodById(id: string): Promise<IGamePlayRecord[]> {
  return http.get('/app-api/author/getPlayMethodById', { schemeId: id })
}

export function getAllPlayMethod(): Promise<Omit<IGamePlayItem, 'label' | 'value'>[]> {
  return http.get('/app-api/author/getPlayMethodById?schemeId')
}

export interface ISchemeRecord {
  matchPlay: number
  match?: { id: number; homeName: string; awayName: string; result?: string }[]
}

export interface IMatchPlayItem {
  playId: number
  opinion: number
  type: number // 0 主玩法 1 附赠玩法
  result: string // '1,2,3'
  resultType: number // 玩法类型
}

export interface IArticleDraft {
  id: number
  draft: 0 | 1
  price: number | null
  schemePlay: number
  title: string | null
  intro: string | null
  freeContents: string | null
  contents: string | null
  autoReplacement: 0 | 1 // 自动补单
  refundType: 0 | 1 // 不中即退
  matchScheme: string // IMatchPlayItem类型的字符串数组
}

export function getArticleDraft(): Promise<IArticleDraft | null> {
  return http.get('/app-api/article/getDraftArticle')
}

export interface IRecommendAuthor {
  authorId: number
  authorName: string
  avatarUrl: string
  winCount: number
  recentWinCount: string
  hitRate: number
}

/* 推荐专家 */
export function getRecommendAuthor(): Promise<IRecommendAuthor[]> {
  return http.get('/app-api/home/<USER>')
}

export interface IFocusedAuthor extends IRecommendAuthor {
  sellCount: number
  hitRate: number
}

/* 关注作者 */
export function getFocusedAuthor(type = FOCUSED_AUTHOR_TYPE.FOCUSED): Promise<IFocusedAuthor[]> {
  return http.get('/app-api/home/<USER>', { type })
}

export interface IIdentifyCard {
  success: boolean
  address: string
  birth: string
  name: string
  num: string
}

// 作者信息接口返回数据类型
export interface IAuthorInfo {
  authorId: number
  authorName: string
  authorAvatar: string
  fans: number
  attention: number
  registerCount: number
  attentionStatus: number
  privilegeSetStatus: number // 是否设置包时特权 0：否 1：是
  winCount: number // 连中次数
  recentWinCount: string // 最近连中数
  hitRate: number
  intro: string // 简介
}

/* 获取作者信息 */
export function getAuthorInfo(id: number): Promise<IAuthorInfo> {
  return http.get('/app-api/author/info', { id })
}

/* 身份证正面识别 */
export function identifyIDCard(image: string): Promise<IIdentifyCard> {
  return http.post('/app-api/id-ocr/id-card-front', { image })
}

/* 身份证反面识别 */
export function identifyIDCardBack(image: string) {
  return http.post('/app-api/id-ocr/id-card-back', { image })
}

export interface IAuthorAuditParams {
  name: string
  idCard: string
  imgUrl: string
  nickname: string
  phone: string
  desc: string
  idCardFront: string
  idCardBack: string
  bankNo: string
  bankName: string
  bankRealName: string
  proDesc?: string
  proImgUrls: string
}

/* 提交作者信息 */
// /app-api/member/author-audit/submit-author-audit
export function submitAuthor(data: IAuthorAuditParams) {
  return http.post('/app-api/member/author-audit/submit-author-audit', data)
}

export function getMemberPrivilegeLogs(params): Promise<IResData<any>> {
  return http.get('/app-api/author/getMemberPrivilegeLogs', params)
}

/* 关注作者 */
export function followAuthor(authorId: number): Promise<any> {
  return http.post('/app-api/author/follow', { authorId })
}

/* 取消关注作者 */
export function unfollowAuthor(authorId: number): Promise<any> {
  return http.post('/app-api/author/unfollow', { authorId })
}

// 作者专辑文章信息
export interface IAuthorArticle {
  id: number
  title: string
  price: number
  publishTime: string
  purchaseCount: number
  schemePlay: number // 文章玩法
  tags?: string[] // 标签列表
  matches?: {
    id: number
    matchTime: string
    leagueName: string
    homeName: string
    awayName: string
  }[] // 比赛列表
}

// 作者专辑信息
export interface IAuthorPackage {
  articleCount: number // 文章总数
  dailyUpdateCount: number // 每日更新数量
  articleList: IAuthorArticle[] // 文章列表
}

/* 获取作者专辑文章列表 */
export function getAuthorPackageArticles(
  packageId: number,
  page: number = 1,
  size: number = 5,
): Promise<IAuthorPackage> {
  return http.get('/app-api/author/package/articles', { packageId, page, size })
}

// 文章中的比赛信息
export interface IArticleMatchInfo {
  matchId: number
  matchTime: number
  homeTeamName: string
  awayTeamName: string
  homeTeamLogo: string
  awayTeamLogo: string
  competitionName: string
  homeScoreStr: string
  awayScoreStr: string
  homeScore: number
  awayScore: number
  homeHalfScore: number
  awayHalfScore: number
  homeCorner: number
  awayCorner: number
  issueNum: string
}

// 套餐文章信息
export interface IPrivilegeArticleInfo {
  id: number // 文章ID
  title: string // 文章标题
  intro: string // 文章简介
  price: number
  count: number // 购买人数
  createTime: string
  initBuyCount: number // 初始购买数
  matchIds: string
  schemePlay: number
  issue: string
  articleMatchInfo: IArticleMatchInfo[] // 比赛信息
}

// 套餐文章列表响应
export interface IPrivilegeArticleListResponse {
  list: IPrivilegeArticleInfo[]
  total: number
}

/* 获取套餐文章列表 */
export function getPrivilegeArticleList(params: {
  pageNo: number
  pageSize: number
  privilegeId: number
  authorId?: number
}): Promise<IPrivilegeArticleListResponse> {
  return http.get('/app-api/member/privilege/get-privilege-article-list', params)
}

// 套餐文章信息
export interface IPrivilegeArticle {
  id: number
  title: string
  intro: string
}

// 子套餐信息
export interface IPrivilegeChild {
  id: number
  type: number
  days: number
  price: number
}

// 套餐详情
export interface IPrivilegeInfo {
  privilegeName: string
  price: number
  content: string
  type: number
  id: number
  totalArticleCount: number
  totalArticle: IPrivilegeArticle[]
  date: number // 每几天
  num: number // 发布几篇文章
  children: IPrivilegeChild[]
  authorName: string
  avatarUrl: string
  qrcode: string
  isBuy: number // 是否售卖 0 售卖 1 不售卖
  hasBuy: number // 是否已购买 0 未购买 1 已购买
  authorId: number
  top: number
  articlePrice?: number // 文章单价，可能不存在
  showPicUrl?: string // 套餐图片
}

/* 获取套餐详情 */
export function getPrivilegeInfo(id: number): Promise<IPrivilegeInfo> {
  return http.get(`/app-api/member/privilege/get/${id}`)
}

export interface IOtherArticleItem {
  id: number
  schemePlay: SCHEME_TYPE | null
  title: string
  matchTime: number
  createTime: number
  competitionName: string
  homeTeamName: string
  awayTeamName: string
  buyCount: number
  price: number
}

/* 作者其他文章 */
export function getOtherAuthorArticles(articleId: number): Promise<IOtherArticleItem[]> {
  return http.get('/app-api/article/getOtherArticles', { articleId })
}
