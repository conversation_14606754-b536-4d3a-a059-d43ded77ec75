import { http } from '@/utils/http'

interface authorAudit {
  name: string
  idCard: string
  imgUrl: string
  status: number
}

export function submitAuthorAudit(data): Promise<IResData<boolean>> {
  return http.post('/app-api/member/author-audit/submit-author-audit', data)
}

export function getAuthorAuditList(): Promise<IResData<authorAudit>> {
  return http.get('/app-api/member/author-audit/get-author-audit-info')
}
