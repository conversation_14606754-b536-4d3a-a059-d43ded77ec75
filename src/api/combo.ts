/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2025-05-15 15:36:30
 * @LastEditTime: 2025-05-16 14:57:55
 * @LastEditors: Ethan
 * @FilePath: /football-lottery-uniapp/src/api/combo.ts
 */
import { GAME_PLAY_TYPE, HOME_ARTICLE_PLAY, WIN_TYPE } from '@/utils/enum'
import { http } from '@/utils/http'
import { ConfigType } from 'dayjs'
import { IOdds } from './match'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'

// 套餐详情
export function getMemberPrivilege(id: number) {
  return http.get(`/app-api/member/privilege/get/${id}`)
}

// 获取套餐推送配置
export function getPrivilegePushConfig(id: number) {
  return http.get(`/app-api/author/getPrivilegePushConfig?id=${id}`)
}

// 更新套餐推送配置
export function updatePrivilegePushConfigs(data) {
  return http.post(`/app-api/author/updatePrivilegePushConfigs`, data)
}

export function privilegeArticleList(id: number, date?: string) {
  return http.get(`/app-api/member/privilege/privilege-article-list`, { id, date })
}

// 添加套餐可选文章
export function addPrivilegeArticle(data) {
  return http.post(`/app-api/member/privilege/add-privilege-article`, data)
}

// 修改套餐状态
export function changePrivilegeStatus(id: number) {
  return http.post(`/app-api/member/privilege/change-status/${id}`)
}

// 删除套餐配置
export function delPrivilegeSet(id: number) {
  return http.get(`/app-api/author/delPrivilegeSet?id=${id}`)
}

// 折扣二维码
export function privilegeSetCreate(data) {
  return http.post(`/app-api/author/privilegeSet/create`, data)
}

// 分享图片
export function getPrivilegeShareImg(id): Promise<IResData<string>> {
  return http.get('/app-api/member/privilege/share', { id })
}

// 设置公开
export function privilegeUpdateConsumeInfo(data) {
  return http.post('/app-api/member/privilege/updateConsumeInfo', data)
}

// 退款
export function refundPrivilegeOrder(id: number) {
  return http.get(`/app-api/privilege-order/refund`, { id })
}

// 部分退款
export function refundPrivilegeOrderPart(id: number, amount: number) {
  return http.post(`/app-api/privilege-order//part-refund`, { id, amount })
}

// 获取退款订单列表
export function getRefundOrderList(params: any) {
  params.date = params.startDate
  return http.get('/app-api/scheme-order/getRefundOrderPage', params)
}

// 获取套餐历史记录
export function getDiscountPrivilegeList(id: number) {
  return http.get('/app-api/author/getDiscountPrivilegeList', { id })
}
