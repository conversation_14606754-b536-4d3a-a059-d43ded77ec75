import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
import { DATA_TYPE, LIVE_INFO_TYPE } from '@/utils/enum'
import { http } from '@/utils/http'
import { IMatchResult } from './article'
import { IGamePlayItem, IMatchPlayItem } from './author'

export interface IMatchItem {
  id: number // 比赛场次id
  easonId: number // 赛季id
  competitionId: number // 赛事id
  categoryId: number // 赛事分类id
  categoryName: string // 赛事分类名称
  homeTeamId: number // 主队id
  homeTeamName: string // 主队名称
  homeTeamLogo: string //主队logo
  awayTeamId: number // 客队id
  awayTeamName: string // 客队名称
  awayTeamLogo: string // 客队logo
  statusId: number // 比赛状态
  statusDesc: string // 比赛状态描述
  matchTime: number // 比赛时间
  neutral: number // 是否中立场
  homeScores: string // 主队比分数据
  homeScore: number // 主队比分
  homeHalfScore: number // 主队半场比分
  homeRedCard: number // 主队红牌
  homeYellowCard: number // 主队黄牌
  homeCornerKick: number // 主队角球
  awayScores: string // 客队比分数据
  awayScore: number // 客队比分
  awayHalfScore: number // 客队半场比分
  awayRedCard: number // 客队红牌
  awayYellowCard: number // 客队黄牌
  awayCornerKick: number // 客队角球
  homePosition: string // 主队排名
  awayPosition: string // 客队排名
  aggScore: string // 双回合常规时间
  relatedId: number // 双回合另一回合比赛id
  attention: number // 关注状态 0-未关注，1-已关注
  liveInfo: string //
  date: string
  time: string
  roundName: string
  homeShootIn: number // 主队射正数
  awayShootIn: number // 客队射正数
  homeShoot: number // 主队射门数
  awayShoot: number // 客队射门数
  homeCoachName: string // 主队教练
  homeCoachLogo: string // 主队教练头像
  awayCoachName: string // 客队教练
  awayCoachLogo: string // 客队教练头像
  homeFormation: string // 客队阵型
  awayFormation: string // 客队阵型
  refereeName: string // 裁判
  competitionName: string
  hasFormation: boolean // 是否有阵容信息
  hasArticle: boolean // 是否有文章
}

export interface ILiveRecord {
  type: number // 事件类型
  position: 0 | 1 | 2 // 事件法生一方(0 无 1 主队 2 客队)
  data: string // 事件详情
  time: string // 时间
}

export interface IMatchDetailItem {
  type: number
  position: number // 事件发生方，1 主队； 2 客队
  data: string
}

/* 获取比赛场次信息(包括，我关注的和热门推荐) */
// export function getMatchList(): Promise<{
//   recommendList: IMatchItem[]
//   attentionList: IMatchItem[]
// }> {
//   return http.get('/app-api/server/match-list/list')
// }

/* 我关注的比赛列表 */
export function getAttentionList(
  pageNo = 1,
  pageSize = DEFAULT_PAGE_SIZE,
): Promise<{ list: IMatchItem[] }> {
  return http.get('/app-api/server/match-list/getAttentionList', { pageNo, pageSize })
}

/* 获取推荐比赛列表 */
// /app-api/server/match-list/getRecommendList
export function getRecommendList(): Promise<IMatchItem[]> {
  return http.get('/app-api/server/match-list/getRecommendList')
}

/* 获取指定日期的比赛场次信息 */
export function getMatchListByDate(
  date: string,
  pageNo = 1,
  pageSize = DEFAULT_PAGE_SIZE,
  competitionId?: number,
): Promise<{ list: IMatchItem[]; total: number }> {
  return http.get('/app-api/server/match-list/getListByDate', {
    date,
    competitionId,
    pageNo,
    pageSize,
  })
}

/* 获取比赛信息 */
export function getMatchList(params: {
  competitionId?: number
  pageNo: number
  pageSize: number
  name?: string
}): Promise<{ list: IMatchItem[]; total: number }> {
  return http.get('/app-api/server/match-list/getListByDate', params)
}

/* 获取历史赛事信息 */
export function getHistoryMatchList(params: {
  competitionId?: number
  pageNo: number
  pageSize: number
  name?: string
}): Promise<{ list: IMatchItem[]; total: number }> {
  return http.get('/app-api/server/match-list/getOldMatchList', params)
}

export interface IMatchCategory {
  id: number
  name: string
}

export interface ICascadeMatchCategory {
  id: number
  name: string
  logo: string
  children?: ICascadeMatchCategory[]
}

/* 获取比赛分类信息 */
export function getCategoryNum(date?: string): Promise<IMatchCategory[]> {
  return http.get('/app-api/server/match-category/getCategoryGame', { date })
}

export function getFocusedCategoriesInfo(
  ids: string,
): Promise<{ id: number; shortName: string }[]> {
  return http.get('/app-api/server/match-category/getCompetitionByIds', { ids })
}

/* 获取赛事分类(一级，州) */
export function getCategoryLevel1(): Promise<IMatchCategory[]> {
  return http.get('/app-api/server/match-category/list')
}

/* 获取赛事分类(二级，国家) */
export function getCategoryLevel2(
  id: number,
): Promise<{ id: number; name: string; logo: string }[]> {
  return http.get('/app-api/server/match-category/getCountryList', { categoryId: id })
}

/* 获取国际赛事分类(也是二级) */
export function getCategoryLevel2International(): Promise<
  { id: number; shortName: string; logo: string }[]
> {
  return http.get('/app-api/server/match-category/getInternationalMatch')
}

/* 获取赛事列表(第三级，叶子节点) */
export function getCategoryLevel3(
  id: number,
): Promise<{ id: number; shortName: string; logo: string }[]> {
  return http.get('/app-api/server/match-category/getCompetitionList', { countryId: id })
}

/* 关注比赛 */
export function addMatchAttention(id: number): Promise<boolean> {
  return http.post(`/app-api/server/match-list/addAttention?matchId=${id}`)
}

/* 取消关注比赛 */
export function cancelMatchAttention(id: number): Promise<boolean> {
  return http.post(`/app-api/server/match-list/cancelAttention?matchId=${id}`)
}

/////////////////////////////////// 比赛详情 ///////////////////////////////

/* 获取比赛详情 */
export function getMatchDetailById(matchId: number | string): Promise<IMatchItem> {
  return http.get('/app-api/server/match-list/getMatchInfo', { matchId })
}

export interface ILineupItem {
  id: number
  name: string
  lineupType: 1 | 2 // 主队 1 客队
  first: 0 | 1 // 是否首发
  logo: string // 头像
  shirtNumber: number // 队服号码
  positionName: string // 位置
  incidents: string // json字符串
  captain: 0 | 1 // 是否是队长
  x: number
  y: number
}

interface ISubstitution extends IIncident {
  in_player: { id: number; name: string }
  out_player: { id: number; name: string }
}

export interface IIncident {
  type: LIVE_INFO_TYPE // 事件类型
  reason_type: number // 事件原因 见INCIDENT_TYPE
  time: string // 数字字符串
  belong: 0 | 1 | 2 // 事件发生方 0 中立 1 主队 2 客队
  home_score: number // 主队比分
  away_score: number // 客队比分
  in_player?: { id: number; name: string }
  out_player?: { id: number; name: string }
}

export interface IIncidentData {
  cards: IIncident[]
  scores: IIncident[]
  substitution?: ISubstitution[]
}

interface ILineupRecord extends Omit<ILineupItem, 'incidents'> {
  incidents: IIncidentData | null
}

export interface ILineup {
  first: { home: ILineupRecord[]; away: ILineupRecord[] }
  substitute: { home: ILineupRecord[]; away: ILineupRecord[] }
}

export interface ILineupData {
  starter: { home: ILineupItem[]; away: ILineupItem[]; len: number }
  bench: { home: ILineupItem[]; away: ILineupItem[]; len: number }
}

/* 获取比赛阵容信息 */
export function getMatchLineUpDetailByMatchId(matchId: number | string): Promise<ILineupItem[]> {
  return http.get('/app-api/server/match-lineup-detail/getMatchLineupDetail', { matchId })
}

/* 获取用户已投票的球队 */
export function getVoteTeam(matchId: number): Promise<number | null> {
  return http.get('/app-api/member-vote/getMemberVoteTeam', { matchId })
}

/* 获取比赛投票信息 */
export function getMatchVoteInfo(matchId: number): Promise<{ teamId: number; num: number }[]> {
  return http.get('/app-api/member-vote/getMatchVoteInfo', { matchId })
}

/* 比赛投票 */
export function matchVote(teamId: number, matchId: number) {
  return http.post('/app-api/member-vote/matchVote', { teamId, matchId })
}

export interface IChatItem {
  userId: number
  msg: string
  nickname: string
  id: number
}

/* 比赛聊天记录 */
export function getMatchChatList(matchId: number, id?: number): Promise<IChatItem[]> {
  return http.get('/app-api/member-chat/getMatchChatList', id ? { matchId, id } : { matchId })
}

/* 发送比赛聊天记录 */
export function sendMatchChat(matchId: number, msg: string) {
  return http.post('/app-api/member-chat/sendMatchChat', { matchId, msg })
}

/* 获取所有联赛类型 */
export function getMatchCategory() {
  return http.get('/app-api/server/match-category/list')
}

/* 更新用户关注赛事 */
export function updateCompetitionIds(competitionId: string) {
  return http.get('/app-api/member/user/updateCompetitionIds', { competitionId })
}

/* 获取比赛阵容 */
export function getLineupById(matchId: number): Promise<ILineupItem[]> {
  return http.get('/app-api/server/match-lineup-detail/get-by-match-id', { matchId })
}

export interface IMatchInfoItem {
  matchId: number // 赛事id
  homeName: string // 主队名称
  awayName: string // 客队名称
  comp: string // 赛事名称
  roundName: string // 赛事名称与伦次
  matchTime: number // 比赛日期
  issueNum: string | null // 期
  issue: string | null // 14场或任九的期数
}

/* 获取比赛信息列表 */
export function getMatchListInfo(dataType: number | string): Promise<IMatchInfoItem[]> {
  return http.get('/app-api/server/match-list/getMatchListInfos', { dataType })
}

export interface IMatchInfoRecord extends IMatchInfoItem {
  shortHomeName: string
  shortAwayName: string
  shortComp: string
  matchPlayOdds: IOdds
}

export interface IMatchInfoData extends IMatchInfoRecord {
  week: string
}

export function get24HoursMatchList({
  dataType,
  pageNo,
  pageSize,
  name,
}: {
  dataType: DATA_TYPE
  pageNo: number
  pageSize: number
  name?: string
}): Promise<{ list: IMatchInfoRecord[]; total: number }> {
  return http.get('/app-api/server/match-list/get24HoursMatchList', {
    dataType,
    name,
    pageNo,
    pageSize,
  })
}

export interface IOdds {
  matchId: number // 比赛id
  spf: string // 胜负平
  bf: string // 比分
  rq: string // 让球胜负平
  bqc: string // 半全场
  jq: string // 进球
  dxq: string // 大小球 string字符串，逗号隔开，中间是进球数，第一个是买大的赔率，最后一个是买小的赔率
  asiaHomeodds: number // (外围)让胜平负 主胜
  asiaDrawodds: number // (外围)让胜平负 让比分多少
  asiaAwayodds: number // (外围)让胜平负 客胜
  euHomeodds: number // (外围)胜平负 主胜
  euDrawodds: number // (外围)胜平负 和
  euAwayodds: number // (外围)胜平负 客胜
  bsHomeodds: number // (大小球)大赔率
  bsDrawodds: number // (大小球)进球数
  bsAwayodds: number // (大小球)小赔率
}

/* 获取赛事对应玩法的赔率 */
export function getMatchPlayOdds(matchId: string | number, dataType: number): Promise<IOdds[]> {
  return http.get('/app-api/server/match-list/getMatchPlayOdds', { dataType, matchId })
}

export interface IMatchSchemeItem {
  matchId: number
  opinion: number | string
  homeName: string
  awayName: string
  roundName: string
  matchTime: number
  issueNum: string | null
  odds: IOdds
  main: any | any[]
  bonus?: any | any[]
  gamePlayOptions: IGamePlayItem[]
  bonusGamePlayOptions: IGamePlayItem[]
  mmatchPlays: IMatchResult[]
  bmatchPlays?: IMatchResult[]
  [key: string]: any
}

export interface IHomeMatchItem extends IMatchItem {
  competitionName: string
}

export function getHomeMatchList(): Promise<IHomeMatchItem[]> {
  return http.get('/app-api/server/match-list/getHomeMatchList')
}

/*
 * 获取第三方获取比赛列表
 * type 0: 5大联赛 1 一级赛事
 * status 0 进行中 1 即将开赛
 */
export function getThirdMatchList(type = 1, status = 0): Promise<{ list: IMatchItem[] }> {
  return http.get('/app-api/server/match-list/third/getMatchList', { type, status })
}

export interface IBroomerResultItem {
  id: number
  matchId: number
  dataType: number
  matchName: string
  matchTime: number
  contents: string
  matchScheme: string
}

export interface IBroomerItem extends Omit<IBroomerResultItem, 'matchScheme'> {
  matchScheme: {
    matchId: number
    opinion: number
    homeName: string
    awayName: string
    matchTime: number
    roundName: string
    round: string
    competitionName: string
    matchPlays: IMatchPlayItem[]
  }[]
}

export interface ISelectedMatchScheme extends IBroomerItem {
  main: ({ label: string; value: number; type: number; disabled: boolean } & IMatchPlayItem)[]
  bonus: ({ label: string; value: number; type: number; disabled: boolean } & IMatchPlayItem)[]
  mmatchPlays: number[]
  bmatchPlays: number[]
}

/* 获取扫盘分页数据 */
export function getBroomerMatchSchemeByPage(
  pageNo = 1,
  params?: Partial<{ startDate: string; endDate: string; teamName: string }>,
): Promise<{ list: IBroomerResultItem[]; total: number }> {
  return http.get('/app-api/server/broomer-match-scheme/page', {
    ...params,
    pageSize: DEFAULT_PAGE_SIZE,
    pageNo,
  })
}

export interface IMatchPlay {
  type: number
  playId: number
  opinion: number
  resultType: number
  result: string
}

export interface IMatchScheme {
  matchId: number
  homeName: string
  awayName: string
  roundName: string
  matchTime: number
  opinion: number
  week?: string
  comp?: string
  shortHomeName?: string
  shortAwayName?: string
  shortComp?: string
  issueNum?: string
  matchPlays: IMatchPlay[]
}
