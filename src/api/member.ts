import { GAME_PLAY_TYPE, HOME_ARTICLE_PLAY, WIN_TYPE } from '@/utils/enum'
import { http } from '@/utils/http'
import { ConfigType } from 'dayjs'
import { IOdds } from './match'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
export const ARTICLE_DEFAULT_PAGE = 5

// 获取套餐
export function getPublishPrivilegeList(id?: number): Promise<{ id: number; name: string }[]> {
  return http.get(`/app-api/member/privilege/get-publish-privilege-list`)
}

// 获取套餐PV记录
export function getPrivilegePvLog(
  privilegeId: number,
  pageNo: number,
  pageSize = ARTICLE_DEFAULT_PAGE,
  isBuy?: number,
): Promise<{ list; total: number }> {
  return http.get('/app-api/member/privilege/getPrivilegePvLog', {
    privilegeId,
    pageNo,
    pageSize,
    isBuy,
  })
}

// 获取套餐PV记录合计
export function getPrivilegeStatistics(
  privilegeId: number,
  pageNo: number,
  pageSize = ARTICLE_DEFAULT_PAGE,
  isBuy: number,
): Promise<{ list; total: number }> {
  return http.get('/app-api/member/privilege/getPrivilegeStatistics', {
    privilegeId,
    pageNo,
    pageSize,
    isBuy,
  })
}

// 套餐置顶
export function privilegeTopStatus(data) {
  return http.post(`/app-api/member/privilege/top-status`, data)
}
