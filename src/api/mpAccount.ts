/*
 * @Description: 
 * @Author: <PERSON>
 * @Date: 2025-05-15 15:36:30
 * @LastEditTime: 2025-05-20 20:54:29
 * @LastEditors: Ethan
 * @FilePath: /football-lottery-uniapp/src/api/mpAccount.ts
 */
import { http } from '@/utils/http'

export function getUserPushWx(): Promise<any> {
  return http.get('/app-api/member/user/getUserPushWx')
}

export function switchWx(data): Promise<any> {
  return http.post('/app-api/member/user/switchWx', data)
}

export function getPushTemplate(accountId): Promise<any[]> {
  return http.get('/app-api/author/getPushTemplates', { accountId })
}

export function articleDelete(id: number) {
  return http.post(`/app-api/article/delete/${id}`)
}