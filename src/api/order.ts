import { PAY_TYPE } from '@/utils/enum'
import { http } from '@/utils/http'

/* 生成方案订单 */
interface IArticleOrder {
  articleId: number
  orderNo: number
  payUrl: string
  qrCode: string
  status: number // 支付状态
  wxJsapiParams: any
}
export function generateOrder(
  articleId: number,
  payType: PAY_TYPE,
  redirectUrl: string,
  shareType,
  privilegeId: number,
): Promise<IArticleOrder> {
  return http.post('/app-api/scheme-order/generateSchemeOrder', {
    articleId,
    payType,
    redirectUrl,
    privilegeId,
  })
}

export function refundOrder(id): Promise<any> {
  return http.get('/app-api/scheme-order/refund', { id })
}
