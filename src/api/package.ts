import { http } from '@/utils/http'

export interface IAuthorPackage {
  id: number
  name: string
  price: number
  isBuy: number
  pvCount: number
  buyCount: number
  amount: number
  type: number
  top: number
}

export interface IAuthorPackageParams {
  pageNo: number
  pageSize: number
  authorId?: number
}

export enum PackageType {
  TIME = 0, //包时套餐
  TIMES = 1, //次数套餐
  PACKAGE = 3, //套餐包
}

/**
 * 添加套餐的参数接口
 */
export interface IAddPackageParams {
  type: number // 特权类型：0：包时套餐 1：次数套餐 3:套餐包
  id?: string // ID
  price: string | number // 价格
  days: string | number // 天数
  matchType: number // 比赛类型
  privilegePrices: Array<{
    oldPrivilegeId?: number
    day: string | number
    price: string | number
  }> // 售价策略列表
  date: string | number // 每几天发布文章数
  num: string | number // 文章数量
  name: string // 套餐名称
  content: string // 套餐介绍
  icon?: string // 图标
  articlePrice?: number,
  showPicUrl?: string
}

/**
 * 获取作者套餐列表
 * @param params 查询参数
 * @returns Promise
 */
export function getAuthorPackages(params: IAuthorPackageParams) {
  return http.get<{
    list: IAuthorPackage[]
    total: number
  }>('/app-api/member/privilege/author/page', params)
}

/**
 * 编辑作者套餐
 * @param data 套餐数据
 * @returns Promise
 */
export function editAuthorPackage(data: Partial<IAuthorPackage>) {
  return http.post('/app-api/member/privilege/author/update', data)
}

/**
 * 删除作者套餐
 * @param id 套餐ID
 * @returns Promise
 */
export function deleteAuthorPackage(id: number) {
  return http<any>({
    url: `/app-api/author/delPrivilegeSet`,
    method: 'GET',
    query: { id },
  })
}

/**
 * 修改套餐状态（上下架）
 * @param id 套餐ID
 * @returns Promise
 */
export function changePackageStatus(id: number) {
  return http.post<boolean>(`/app-api/member/privilege/change-status/${id}`)
}

/**
 * 添加作者套餐
 * @param data 套餐数据
 * @returns Promise
 */
export function addAuthorPackage(data: IAddPackageParams) {
  return http.post<boolean>('/app-api/author/savePrivilegeSet', data)
}

/**
 * 获取作者特权套餐详情
 * @param id 套餐ID
 * @returns Promise
 */
export interface PrivilegePriceVO {
  oldPrivilegeId?: number
  day: number
  price: number
}

export interface AppAuthorPrivilegeSetRespVo {
  id: number
  price: number
  days: number
  type: number // 特权类型：0：包时套餐 1：次数套餐
  matchType: number
  date: number
  num: number
  name?: string // 套餐名称
  content?: string // 套餐介绍
  icon?: string // 图标
  privilegePrices: PrivilegePriceVO[]
  articlePrice: number,
  showPicUrl?: string
}

export function getAuthorPrivilegeDetail(id: number) {
  return http.get<AppAuthorPrivilegeSetRespVo>(`/app-api/author/getPrivilegeSet/${id}`)
}
