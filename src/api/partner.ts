import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
import { http } from '@/utils/http'

export interface IPartnerAuditInfo {
  status: 0 | 1 | -1 // 0 申请中 1 申请成功 -1 失败
  createTime: number
}

/*
 *  获得合作伙伴审核信息 返回值为null则表示没有申请
 */
export function getPartnerAuditInfo(): Promise<null | IPartnerAuditInfo> {
  return http.get('/app-api/partner-audit/get-partner-audit-info')
}

export interface IPartnerAuditParams {
  name: string
  phone: string
  idCard: string
  imgUrlFront: string
  imgUrlBack: string
  headshot: string
}

/* 提交合作伙伴审核信息 */
export function submitPartnerAudit(data: IPartnerAuditParams) {
  return http.post('/app-api/partner-audit/submit-partner-audit', data)
}

export interface IPartnerInfo {
  id: number
  avatarUrl: string // 头像
  nickname: string // 用户昵称
  exCode: string // UID
  totalAmount: number // 累计佣金
  totalCount: number // 推广总数
  canWithdrawAmount: number // 可提现余额
  earnings: number // 待入账金额
  withdrawingAmount: number // 体现中金额
  withdrawedAmount: number // 已提现金额
  publicUser: null | number // null 0 未公开 1 已公开
}

/* 获取合作伙伴信息 */
export function getPartnerInfo(): Promise<IPartnerInfo> {
  return http.get('/app-api/member/partner/get')
}

/* 更新合作伙伴公开用户信息 */
export function updatePublicUser(status: 0 | 1) {
  return http.post('/app-api/member/partner/updatePublicUser', { status })
}

export interface IPartnerPromotionItem {
  id: number
  nickname: string
  avatarUrl: string
  createTime: number
}

/* 推广明细 */
export function getPromotionPage(
  pageNo: number,
  pageSize = DEFAULT_PAGE_SIZE,
  date?: string,
): Promise<{ list: IPartnerPromotionItem[]; total: number }> {
  return http.get('/app-api/member/partner/promotion-page', { pageNo, pageSize, date })
}

export interface IPartnerCommissionItem {
  id: number
  title: string
  userName: string
  rewardAmount: number
  createTime: number
}

/* 佣金明细 */
export function getPromotionRewardPage(
  pageNo: number,
  pageSize = DEFAULT_PAGE_SIZE,
  date?: string,
): Promise<{ list: IPartnerCommissionItem[]; total: number }> {
  return http.get('/app-api/member/partner/promotion-reward-page', { pageNo, pageSize, date })
}

export interface IPartnerDetailInfo {
  id: number
  name: string
  idCard: string
  phone: string
  aliAccount: string
  bankAccount: string
}

/* 合作方个人信息 */
export function getPartnerDetail(): Promise<IPartnerDetailInfo> {
  return http.get('/app-api/member/partner/get-partner-info')
}

/* 获取分享信息(我的UID) */
export function getPartnerShareInfo(): Promise<{ id: number; uid: string; qrcode: string }> {
  return http.get('/app-api/member/partner/getShareInfo')
}

export interface IPartnerSettlement {
  id: number | null
  commissionRate: number // 手续费
  account: string | null // 提现账号
  name: string | null
  idNo: string | null // 身份证号
  bankId: number // 开户银行
}

/* 获取合作伙伴提现信息 */
export function getPartnerSettlement(type: string): Promise<IPartnerSettlement> {
  return http.get('/app-api/member/partner/get-partner-settlement-info', { type })
}

export interface IPartnerSettlementParams {
  settleType: number // 账户类型 1 支付宝 2 银行卡
  account: number | string
  name: string
  idNo: string
  bankId?: number | null
}

/* 更新合作伙伴提现信息(修改支付宝账号或银行卡号) */
export function updatePartnerSettlementInfo(data) {
  return http.post('/app-api/member/partner/update-partner-settlement-info', data)
}

/* 修改合伙人状态(填写合作方邀请码) */
export function submitPartnerInviteCode(code: string) {
  return http.post('/app-api/member/user/updatePartner', { code })
}

/* 获取合伙人提现记录 */
export function getPartnerWithdrawLog(params: {
  pageNo: number
  pageSize: number
  type: number
}): Promise<{ total: number; list: any[] }> {
  return http.get('/app-api/withdraw/partner/logs', params)
}

export interface IPartnerPayItem {
  settileId: number
  payType: 1 | 2
  payTypeName: string
  payAccount: string
}

export interface IPartnerWithdrawConfig {
  min: number // 最低可提现金额
  max: number // 最高可提现金额
  rate: number // 分成比率(费率需要拿100减去此值)
  commission: number // 费率
  canWithdraw: number // 可提现金额
  payInfos: IPartnerPayItem[]
}

/* 获取合伙人提现配置 */
export function getPartnerWithdrawConfig(): Promise<IPartnerWithdrawConfig> {
  return http.get('/app-api/withdraw/partner/get-config')
}

/* 合伙人提现 */
// /app-api/withdraw/partner/submit-withdraw
export function partnerWithdraw(amount: number, settleId: number) {
  return http.post('/app-api/withdraw/partner/submit-withdraw', { amount, settleId })
}

export function checkPartnerStatus(): Promise<boolean> {
  return http.get('/app-api/member/partner/check-status')
}

/* 更换手机获取验证码 */
export function getPartnerPhoneVerifyCode(mobile: string) {
  return http.post('/app-api/partner-info/send-sms-code', { mobile, scene: 2 })
}

/* 更换手机号码(code 验证码) */
export function updatePartnerPhone(data: { mobile: string; code: string }) {
  return http.post('/app-api/partner-info/update-partner-sms-code', { ...data, scene: 2 })
}

/* 记录 */
export function officialLog(type: 0 | 1) {
  return http.post('/app-api/mp/clicklog/click-log', { type })
}

interface IPartnerMatchStatus {
  fiveMatchings: boolean // 是否有五大联赛正在进行的比赛
  fiveMatchLater: boolean // 是否有五大联赛即将开始的比赛
  otherMatchings: boolean // 是否有一级赛事正在进行的比赛
  otherMatchLater: boolean // 是否有一级赛事即将开始的额比赛
}
/* 获取比赛状态 */
export function getMatchStatus(): Promise<IPartnerMatchStatus> {
  return http.get('/app-api/server/match-list/third/getMatchStatus')
}
