import { http } from '@/utils/http'

/* 我关注的作者列表 */
export interface IAuthor {
  authorId: number
  nickname: string
  avatar: string
  articleNum: number
  qq: string
  wxQrcodeUrl?: string
}

export interface IChildAccount {
  id: number
  nickname: string
  avatar: string
  status: number
  fans: number
  articleNum: number
}

export interface IBanner {
  id?: number
  title?: string
  pic: string
  toUrl?: string
}
export function getMyAuthorListToFollow(): Promise<IAuthor[]> {
  return http.get('/app-api/home/<USER>')
}

/* 热门作者列表 */
export function getHotAuthorList(): Promise<IAuthor[]> {
  return http.get('/app-api/home/<USER>')
}

/* 轮播广告 */
export function getBannerList(): Promise<IBanner[]> {
  return http.get('/app-api/banner/list')
}

/* 作者子账号列表 */
export function getChildAccountList(): Promise<IChildAccount[]> {
  return http.get('/app-api/member/user/childAccountList')
}

/* 账号密码登录 */
export function loginByUsername(data): Promise<any> {
  return http.post('/app-api/member/user/loginByUsername', data)
}

/* 添加作者子账号 */
export function addChildAccount(data): Promise<boolean> {
  return http.post('/app-api/member/user/addChildAccount', data)
}

/* 添加作者子账号 */
export function changeToChildAccount(data): Promise<any> {
  return http.post('/app-api/member/user/changeToChildAccount', data)
}

export function updateUserQQMail(data): Promise<boolean> {
  return http.post('/app-api/member/user/updateUserQQMail', data)
}

export function updateUserQQ(data): Promise<boolean> {
  return http.post('/app-api/member/user/updateUserQQ', data)
}
