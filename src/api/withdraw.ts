import { http } from '@/utils/http'

export interface AppWithdrawConfigRespVO {
  min: number // 最小提现金额
  max: number // 最大提现金额
  rate: number // 佣金比例
  commission: number // 手续费
  canWithdraw: number // 可提现金额
  payType: number // 支付方式
  payTypeName: string // 支付方式名称
  payAccount: string // 支付账号
}

export function getWithdrawConfig(): Promise<AppWithdrawConfigRespVO> {
  return http.get('/app-api/withdraw/get-config')
}

export function submitWithdraw(data): Promise<boolean> {
  return http.post('/app-api/withdraw/submit-withdraw', data)
}

export function getWithdrawLog(params): Promise<any> {
  return http.get('/app-api/withdraw/logs', params)
}

export function canWithdraw(): Promise<boolean> {
  return http.get('/app-api/withdraw/canWithdraw')
}

export function getWithdrawInfo(): Promise<any> {
  return http.get('/app-api/withdraw/getWithdrawInfo')
}
