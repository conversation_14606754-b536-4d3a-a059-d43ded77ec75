import { http } from '@/utils/http'

// 微信JSSDK配置接口
export interface WxJsConfig {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
  url: string
}

/**
 * 获取微信JS-SDK配置信息
 * @param url 当前网页的URL，不包含#及其后面部分
 * @returns 微信JSSDK配置信息
 */
export function getWxJsConfig(url: string): Promise<WxJsConfig> {
  return http.post('/app-api/wechat/other/create-weixin-jsapi-signature', {
    url,
  })
}
