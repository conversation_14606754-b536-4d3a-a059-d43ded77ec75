import { http } from '@/utils/http'
import { IGamePlayItem, IScheme } from './author'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'

export interface IZhanjiRankingParams {
  startDate: string // 开始日期 YYYY-MM-DD
  endDate: string // 结束日期 YYYY-MM-DD
  countryId: number // 国家ID
  competitionId: number // 联赛ID
  schemePlay: number // 方案玩法
  playId: number // 玩法
  timeType: number // 时间类型 0 早场 1 晚场
  oddsType: number // 冷门高倍类型 0 3X 1 5X 2 10X
}

interface IAccomplishmentReqVO {
  startDate: string | null
  endDate: string | null
  dateType: number | null // 日期类型 0：今日 1：本周 2：本月
}

interface IZhanjiRecord {
  articleId: number
  isWin: boolean
  winName: string
  createTime: number
  title: string
  sp: number
  income: number
}

export interface IZhanjiItem {
  authorName: string
  keyword: string
  hitRate: number
  alias: string
  accomplishmentReqVO: IAccomplishmentReqVO
  dataList: IZhanjiRecord[]
  showPic: string
}

/*
 * 获取战绩排行榜
 * 1、 dateType 必传 日期类型 0：今日 1：本周 2：本月
 */
export function getZhanjiRanking(
  dateType: number,
  params?: Partial<IZhanjiRankingParams>,
): Promise<IZhanjiItem[]> {
  return http.post('/app-api/accomplishment/getInfos', { ...params, dateType })
}

/* 获取国列表 */
export function getCountryList(): Promise<{ id: number; name: string }[]> {
  return http.get('/app-api/accomplishment/getCountryList')
}

/* 获取联赛列表 */
export function getCompetitionList(): Promise<{ id: number; name: string }[]> {
  return http.get('/app-api/accomplishment/getCompetitionList')
}

/* 获取方案类型列表 */
export function getSchemePlayList(): Promise<IScheme[]> {
  return http.get('/app-api/accomplishment/getSchemePlayList')
}

/* 获取所有玩法类型 */
export function getPlayMethods(play: string): Promise<Omit<IGamePlayItem, 'value'>[]> {
  return http.get('/app-api/accomplishment/getPlayingMethodList', { play })
}

/* 生成战绩 */
export function generateZhanji(e: IZhanjiItem): Promise<number> {
  return http.post('/app-api/accomplishment/generateAccomplishment', e)
}

/* 上传战绩图片 */
export function uploadZhanjiImg(data: { id: string; picUrl: string }) {
  return http.post('/app-api/accomplishment/updateShowPic', data)
}

/* 获取战绩 */
export function getZhanjiDetail(id: number | string): Promise<IZhanjiItem> {
  return http.get('/app-api/accomplishment/getAccomplishmentDetail', { id })
}

export interface IZhanji {
  id: number
  createTime: number // 创建时间
  data: {
    keyword: string
    accomplishmentReqVO: { startDate: string; endDate: string }
  }
}

/* 获取战绩列表(分页) */
export function getAccomplishmentPage(
  pageNo = 1,
  params?: { date: string; endDate: string },
): Promise<{ list: IZhanji[]; total: number }> {
  return http.get('/app-api/accomplishment/getAccomplishmentPage', {
    ...params,
    pageNo,
    pageSize: DEFAULT_PAGE_SIZE,
  })
}
