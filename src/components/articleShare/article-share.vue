<template>
  <wd-popup
    v-model="showArticleQrcode"
    @close="closeQrcode"
    custom-style="background: none;text-align: center;"
  >
    <wd-loading color="#d1302e" v-if="loading" />
    <!-- <view style="height: 0; overflow: hidden"> -->
    <view id="scoreimg" style="position: absolute; top: 0; left: 0; z-index: -1">
      <view
        class="combo-share-wrap"
        :style="{ backgroundImage: `url(${article.topBg || defaultTopBg})` }"
      >
        <text class="article-title">{{ article.title }}</text>
        <text class="article-content">{{ article.intro }}</text>
        <image :src="article.shareQrCode" class="qrcode" />

        <view class="flex justify-center text-white items-center">
          <image :src="article.authorAvatar" class="pic" />
          <text class="txt">{{ article.authorName }}</text>
          <image src="https://sacdn.850g.com/football/static/gold2.svg" class="pic" />
          <text class="txt">{{ article.price || '免费' }}</text>
        </view>
      </view>
    </view>
    <view>
      <img style="width: 710rpx; height: 731rpx" v-show="base64Data" :src="base64Data" />
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import { IArticleDetail, updateArticleShareInfo } from '@/api/article'
import { getArticleShareInfo, getArticleShareImg } from '@/api/author'
import html2canvas from 'html2canvas'
const showArticleQrcode = ref(false)
const base64Data = ref('')
const loading = ref(false)
const emit = defineEmits(['close'])
const init = ref(false)
const qrcodeRef = ref()
const article = ref<IArticleDetail>({} as IArticleDetail)
const defaultTopBg = ref('https://sacdn.850g.com/football/static/scheme/top_bg.png')

const closeQrcode = () => {
  showArticleQrcode.value = false
  emit('close')
}

const initImage = async () => {
  console.log('保存图片')
  const scoreimg = document.getElementById('scoreimg')
  // 使用 html2canvas 将 DOM 元素转换为图片
  const canvas = await html2canvas(scoreimg, {
    scale: window.devicePixelRatio || 2,
    useCORS: true, // 处理跨域问题
    allowTaint: true,
    logging: false,
    backgroundColor: null,
  })
  // 将 canvas 转换为 Base64 数据
  base64Data.value = canvas.toDataURL('image/png')
  loading.value = false
}

const showDialog = async (a: IArticleDetail) => {
  base64Data.value = ''
  showArticleQrcode.value = true
  loading.value = true
  defaultTopBg.value = a.value?.topBg || defaultTopBg.value
  uni.showToast({
    title: '正在生成图片...',
    duration: 1000,
    icon: 'loading',
  })
  article.value = a

  setTimeout(() => {
    initImage()
  }, 500)
}

const uploadPic = (id) => {
  let timeId = setInterval(async () => {
    if (init.value) {
      clearInterval(timeId)
      const data = { id, base64Data: base64Data.value }
      await updateArticleShareInfo(data)
    }
  }, 500)
}

defineExpose({
  showDialog,
  uploadPic,
})
</script>
<style lang="scss">
$zone-multiplier: 1;

.combo-share-canvas {
  width: 710rpx;
  height: 731rpx;
  position: relative;
  z-index: 9;
}

.combo-share-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 710rpx;
  height: 731rpx;
  position: relative;
  // background-image: url('https://sacdn.850g.com/football/static/scheme/top_bg.png');
  background-size: 100% 100%;

  .article-title {
    margin-top: $zone-multiplier * 50rpx;
    margin-bottom: $zone-multiplier * 30rpx;
    font-size: $zone-multiplier * 40rpx;
    color: #fff;
  }

  .article-content {
    display: -webkit-box;
    width: 100%;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    overflow: hidden;
    text-align: center;
    font-size: $zone-multiplier * 32rpx;
    color: #fff;
  }

  .qrcode {
    width: $zone-multiplier * 300rpx;
    height: $zone-multiplier * 300rpx;
    border-radius: $zone-multiplier * 12rpx;
    margin: $zone-multiplier * 50rpx 0;
  }

  .pic {
    width: $zone-multiplier * 60rpx;
    height: $zone-multiplier * 60rpx;
    margin-left: $zone-multiplier * 50rpx;
    border-radius: 50%;
  }

  .txt {
    margin-left: $zone-multiplier * 20rpx;
    font-size: $zone-multiplier * 32rpx;
  }
}

.combo-share-cont {
  width: calc(100% - 48rpx);
  height: calc(100% - 128rpx);
  background: #fff;
  position: absolute;
  left: 24rpx;
  top: 60rpx;
  z-index: 1;
  border-radius: 24rpx;
  overflow: hidden;

  .title {
    font-size: 34rpx;
    color: #cf302c;
    text-align: center;
    padding: 24rpx;
  }

  .cont-after {
    padding: 0 90rpx;
    box-sizing: border-box;
  }

  .user-info {
    background: #fbeaeb;
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    border-radius: 6rpx;

    .rounded-full {
      width: 90rpx;
      height: 90rpx;
      margin-right: 20rpx;
      /* 添加右边距 */
    }

    .user-text {
      max-width: calc(100% - 110rpx);
      display: flex;
      flex-direction: column;
    }

    .username {
      font-size: 30rpx;
      color: #333;
    }

    .intro {
      font-size: 26rpx;
      color: #999;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .price {
    font-size: 30rpx;
    text-align: center;
    padding: 24rpx 0;
    display: flex;
    justify-content: space-between;

    .price-yuanjia {
      color: #999;

      .shanchu {
        position: relative;

        &:after {
          content: '';
          width: 100%;
          height: 1px;
          background: #999;
          position: absolute;
          left: 0;
          top: 50%;
          z-index: 1;
        }
      }
    }

    .price-xianjia {
      color: #cf302c;
    }
  }

  // .qrcode {
  //   width: 200rpx;
  //   height: 200rpx;
  //   border: dashed 1rpx #999;
  //   margin: 0 auto;

  //   image {
  //     width: 100%;
  //     height: 100%;
  //   }
  // }
}

.image-container {
  width: 710rpx;
  height: 731rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;

  image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 24rpx;
  }
}

.f-text {
  text-align: center;
  color: #999;
  padding-top: 10rpx;
}

.combo-share-btn {
  margin: 16rpx auto;
  background: #fff;
  border-radius: 50rpx;
  text-align: center;
  line-height: 100rpx;
  width: 80%;
}
</style>
