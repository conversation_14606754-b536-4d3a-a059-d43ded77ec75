<template>
  <wd-fab :expandable="false" position="right-bottom" class="mt-[-150rpx]" v-if="isMiniApp">
    <template #trigger>
      <view class="revice" @click="goBack">
        <image src="https://sacdn.850g.com/football/static/button/back.png" class="w-full h-full" />
      </view>
    </template>
  </wd-fab>
</template>
<script lang="ts" setup>
import { withDefaults } from 'vue'
const isMiniApp = uni.getSystemInfoSync().uniPlatform !== 'mp-weixin'

const props = withDefaults(defineProps<{ authorId: number }>(), { authorId: 0 })
function goBack() {
  // 如果页面栈中只有一个页面，跳转到首页
  uni.reLaunch({
    url: '/pages/author/info/index?authorId=' + props.authorId + `&ts=${Date.now()}`, // 替换为你的首页路径
  })
}
</script>
<style lang="scss" scoped>
.revice {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  padding: 10rpx;
  font-size: 26rpx;
  text-align: center;
}
</style>
