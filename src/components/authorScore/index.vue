<template>
  <wd-popup
    v-model="myScoreDialogVisible"
    closable
    :close-on-click-modal="false"
    custom-style="padding: 30rpx;width: 70%;margin-top: -50rpx;"
  >
    <view class="title">请选择战绩图生产规则</view>
    <view class="text-center">
      <wd-radio-group v-model="myScoreType" shape="dot" inline @change="channgeType">
        <wd-radio :value="1" class="pr-[50rpx]">默认</wd-radio>
        <wd-radio :value="2" class="pr-[50rpx]">全红</wd-radio>
        <wd-radio :value="3">自定义</wd-radio>
      </wd-radio-group>
    </view>
    <view class="pt-[30rpx] font-size-[28rpx]" style="color: rgba(0, 0, 0, 0.5)">
      {{ getDescMsg() }}
    </view>
    <view v-if="myScoreType == 3">
      <wd-search
        v-model="searchText"
        @change="searchData"
        @clear="searchData"
        @search="searchData"
        @blur="searchData"
        hide-cancel
        placeholder-left
      />
    </view>
    <view class="h-[600rpx] py-[20rpx] overflow-y-auto" v-if="myScoreType == 3">
      <view v-if="customArticleList?.length == 0" class="text-center text-[#999] font-size-[28rpx]">
        未查询近15天到已判定红黑的方案
      </view>

      <wd-checkbox-group v-model="articleIds" :max="10" v-else>
        <wd-checkbox
          :modelValue="item.id"
          v-for="item in customArticleList"
          class="mb-[26rpx]"
          v-show="item.show"
          checked-color="#d1302e"
          :key="item.id"
        >
          <view class="flex justify-between">
            <view class="w-[370rpx] font-size-[28rpx] overflow-ellipsis ellipsis whitespace-nowrap">
              {{ item.title }}
            </view>
            <view v-if="[1, 4, 5, 6].includes(item.win)" class="win_red">红</view>
            <view v-if="[2, 7].includes(item.win)" class="win_black">黑</view>
            <view v-if="item.win === 3" class="win_blue">走</view>
          </view>
        </wd-checkbox>
      </wd-checkbox-group>
    </view>
    <view class="mt-[10rpx] h-[600rpx] py-[20rpx]" v-else>
      <view v-if="customArticleList?.length == 0" class="text-center text-[#999] font-size-[28rpx]">
        未查询到符合条件的方案
      </view>
      <template v-else>
        <view
          class="flex justify-between mb-[26rpx]"
          v-for="item in customArticleList"
          :key="item.id"
        >
          <view class="font-size-[28rpx] flex-1 overflow-ellipsis ellipsis whitespace-nowrap">
            {{ item.title }}
          </view>
          <view v-if="[1, 4, 5, 6].includes(item.win)" class="win_red">红</view>
          <view v-if="[2, 7].includes(item.win)" class="win_black">黑</view>
          <view v-if="item.win === 3" class="win_blue">走</view>
        </view>
      </template>
    </view>
    <view class="pt-[50rpx] text-center">
      <wd-button custom-class="cmbutton" @click="showMyScoreList">生成战绩图</wd-button>
    </view>
  </wd-popup>
  <wd-popup
    v-model="myScoreShowVisible"
    :safe-area-inset-bottom="true"
    custom-style="width: 90%;background: none;text-align: center;"
    @close="closeMyScoreList"
  >
    <wd-loading color="#d1302e" v-if="loading" />
    <view style="height: 0; overflow: hidden">
      <view v-show="!base64Data">
        <view class="scoreimg" id="scoreimg">
          <view class="authorinfo">
            <image
              class="authoravatar"
              :src="userInfo.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg'"
            />
            <view>
              <view class="nickname">{{ userInfo.nickname }}</view>
              <view class="umsg">扫描二维码关注我</view>
            </view>
            <image class="qrcode" :src="authorQrcode" />
          </view>
          <view class="tips">
            <text>近期战绩公示</text>
          </view>
          <view class="scorelist">
            <view :class="getItemClass(item.win)" v-for="item in showArticleList" :key="item.id">
              <view class="flex flex-1">
                <view class="scoreitem-dt">
                  {{ dayjs(item.createTime).format('MM-DD') }}
                </view>
                <view class="scoreitem-title">{{ item.title }}</view>
              </view>
              <view class="scoreitem-right-red" v-if="[1, 4, 5, 6].includes(item.win)">红</view>
              <view class="scoreitem-right-black" v-if="[2, 7].includes(item.win)">黑</view>
              <view class="scoreitem-right-blue" v-if="item.win === 3">走</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-show="base64Data" class="text-center">
      <img :src="base64Data" width="90%" />
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import { getMyScoreList, getAuthorQrcode } from '@/service/userService'
import html2canvas from 'html2canvas'
import dayjs from 'dayjs'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const myScoreDialogVisible = ref(false)
const myScoreType = ref(1)
const myScoreShowVisible = ref(false)
const articleIds = ref([])
const base64Data = ref('')
const showArticleList = ref([])
const customArticleList = ref([])
const authorQrcode = ref()
const searchText = ref('')
const loading = ref(false)

const showDialog = async () => {
  articleIds.value = []
  searchText.value = ''
  myScoreType.value = 1
  base64Data.value = ''
  channgeType()
}

const channgeType = async () => {
  myScoreDialogVisible.value = true
  const data = await getMyScoreList({ type: myScoreType.value })
  customArticleList.value = data
  getAuthorQrcode().then((res) => {
    authorQrcode.value = res
  })
}

const searchData = () => {
  customArticleList.value.forEach((item) => {
    item.show = item.title.indexOf(searchText.value) !== -1
  })
}
const getItemClass = (win) => {
  switch (win) {
    case 1:
    case 4:
    case 5:
    case 6:
      return 'scoreitem-red'
    case 2:
    case 7:
      return 'scoreitem-black'
    case 3:
      return 'scoreitem-blue'
    default:
      return ''
  }
}
const saveImage = async () => {
  const scoreimg = document.getElementById('scoreimg')
  // 使用 html2canvas 将 DOM 元素转换为图片
  const canvas = await html2canvas(scoreimg, {
    scale: window.devicePixelRatio || 2,
    useCORS: true, // 处理跨域问题
    allowTaint: true,
    logging: false,
    backgroundColor: null,
  })
  // 将 canvas 转换为 Base64 数据
  base64Data.value = canvas.toDataURL('image/png')
  loading.value = false
}

const showMyScoreList = async () => {
  showArticleList.value = []
  base64Data.value = ''
  if (myScoreType.value !== 3) {
    showArticleList.value = customArticleList.value
  } else {
    showArticleList.value = customArticleList.value.filter((item) => {
      if (articleIds.value.includes(item.id)) {
        return item
      }
    })
  }
  myScoreShowVisible.value = true
  loading.value = true
  setTimeout(() => {
    saveImage()
  }, 500)
}

const closeMyScoreList = () => {
  myScoreShowVisible.value = false
}
const getDescMsg = () => {
  switch (myScoreType.value) {
    case 1:
      return '规则说明：最近10条已经判定红黑的方案'
    case 2:
      return '规则说明：最近10条全红方案'
    case 3:
      return '规则说明：请选择方案生成分享图'
  }
}

defineExpose({
  showDialog,
})
</script>
<style lang="scss" scoped>
$zone-multiplier: 3;

:deep(.wd-search) {
  padding: 20rpx 0;
}

.title {
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 32rpx;
  font-style: normal;
  line-height: 48rpx;
  letter-spacing: 2rpx;
  margin-bottom: 26rpx;
}

.save-btn {
  margin: 0 $zone-multiplier * 20rpx;
}

.win_red {
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  padding: 2rpx 10rpx;
  margin-left: 20rpx;
  font-size: 26rpx;
  border-radius: 8rpx;
  background: #d1302e;
}

.win_blue {
  padding: 2rpx 10rpx;
  margin-left: 20rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #70b603;
  border-radius: 8rpx;
}

.win_black {
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  padding: 2rpx 10rpx;
  margin-left: 20rpx;
  font-size: 26rpx;
  border-radius: 8rpx;
  background: rgba(0, 0, 0, 0.9);
}

.scoreimg {
  background-image: url('https://sacdn.850g.com/football/static/score/bg-score.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: $zone-multiplier * 20rpx;
  width: $zone-multiplier * 580rpx;

  .authorinfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $zone-multiplier * 20rpx $zone-multiplier * 30rpx;
    border-radius: $zone-multiplier * 12rpx;
    background: #fff;

    .nickname {
      font-size: $zone-multiplier * 30rpx;
      text-align: left;
    }

    .umsg {
      font-size: $zone-multiplier * 26rpx;
      color: rgba(0, 0, 0, 0.5);
      margin-top: $zone-multiplier * 10rpx;
      text-align: left;
    }
  }

  .authoravatar {
    width: $zone-multiplier * 100rpx;
    height: $zone-multiplier * 100rpx;
    border-radius: 50%;
  }

  .qrcode {
    width: $zone-multiplier * 120rpx;
    height: $zone-multiplier * 120rpx;
  }

  .tips {
    margin: $zone-multiplier * 20rpx 0;
    text-align: center;
    font-size: $zone-multiplier * 34rpx;
    color: #fff;
  }

  .scorelist {
    margin-bottom: $zone-multiplier * 20rpx;
    padding: $zone-multiplier * 10rpx $zone-multiplier * 20rpx;
    border-radius: $zone-multiplier * 12rpx;
    background: #fff;

    .scoreitem-red {
      color: #d1302e;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: $zone-multiplier * 16rpx;
    }

    .scoreitem-black {
      color: black;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: $zone-multiplier * 16rpx;
    }

    .scoreitem-blue {
      color: #70b603;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: $zone-multiplier * 16rpx;
    }

    .scoreitem-dt {
      text-align: justify;
      font-size: $zone-multiplier * 28rpx;
      width: $zone-multiplier * 80rpx;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .scoreitem-title {
      font-size: $zone-multiplier * 28rpx;
      margin-left: $zone-multiplier * 20rpx;
      width: $zone-multiplier * 350rpx;
      text-align: left;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .scoreitem-right-red {
      width: $zone-multiplier * 50rpx;
      line-height: $zone-multiplier * 50rpx;
      background-color: #d1302e;
      height: $zone-multiplier * 50rpx;
      text-align: center;
      font-size: $zone-multiplier * 28rpx;
      font-weight: 600;
      border-radius: $zone-multiplier * 8rpx;
      color: #fff;
    }

    .scoreitem-right-black {
      width: $zone-multiplier * 50rpx;
      background-color: black;
      height: $zone-multiplier * 50rpx;
      line-height: $zone-multiplier * 50rpx;
      text-align: center;
      font-size: $zone-multiplier * 28rpx;
      font-weight: 600;
      border-radius: $zone-multiplier * 8rpx;
      color: #fff;
    }

    .scoreitem-right-blue {
      width: $zone-multiplier * 50rpx;
      background-color: #70b603;
      height: $zone-multiplier * 50rpx;
      line-height: $zone-multiplier * 50rpx;
      text-align: center;
      font-size: $zone-multiplier * 28rpx;
      font-weight: 600;
      border-radius: $zone-multiplier * 8rpx;
      color: #fff;
    }
  }
}
</style>
