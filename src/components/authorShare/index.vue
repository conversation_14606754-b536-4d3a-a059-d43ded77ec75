<template>
  <wd-popup
    v-model="showArticleQrcode"
    @close="closeQrcode"
    custom-style="background: none;text-align: center;"
  >
    <wd-loading color="#d1302e" v-if="loading" />
    <view style="height: 0; overflow: hidden">
      <view id="author-qrcode" v-show="!base64Data">
        <image :src="showData.avatar" class="avatar" />
        <text class="author-name">{{ showData.username }}</text>
        <text class="focus">快来关注我吧，我有很多方案！</text>

        <view class="prompt">
          <text class="prompt-item">长按识别下方二维码</text>
          <text class="prompt-item">跳转后识别关注即可接收我的新料推送~</text>
        </view>
        <image :src="showData.qrcode" class="qrcode" />
        <!-- <view class="content">
          <view>
            <image class="avatar" :src="showData.avatar" />
            <text class="title">{{ showData.username }}</text>
          </view>
          <image class="qrcode" :src="showData.qrcode" />
        </view>
        <view class="bottom items-center flex justify-center text-center">
          <text>长按识别二维码查看详情</text>
        </view> -->
      </view>
    </view>
    <view>
      <img style="height: 800rpx" v-show="base64Data" :src="base64Data" />
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import { getAuthorShareInfo } from '@/api/author'
import html2canvas from 'html2canvas'
const showArticleQrcode = ref(false)
const base64Data = ref('')
const loading = ref(false)
const showData = ref({
  username: '这里显示用户昵称',
  qrcode: '',
  avatar: '',
})
const emit = defineEmits(['close'])
const init = ref(false)

const closeQrcode = () => {
  showArticleQrcode.value = false
  console.log('closeQrcode')
  emit('close')
}

const initImage = async () => {
  const scoreimg = document.getElementById('author-qrcode')
  // 使用 html2canvas 将 DOM 元素转换为图片
  const canvas = await html2canvas(scoreimg, {
    scale: window.devicePixelRatio || 2,
    useCORS: true, // 处理跨域问题
    allowTaint: true,
    logging: false,
    backgroundColor: null,
  })
  // 将 canvas 转换为 Base64 数据
  base64Data.value = canvas.toDataURL('image/svg')
  loading.value = false
  init.value = true
}

// const getData = async (id) => {
//   console.log('获取文章数据', id)
//   const data = await getArticleShareImg(id)
//   imageSrc.value = data
// }

const getData = async (id) => {
  const data = await getAuthorShareInfo(id)
  showData.value = data
  loading.value = true
  setTimeout(() => {
    initImage()
  }, 500)
}

const showDialog = async (id) => {
  base64Data.value = ''
  await getData(id)
  showArticleQrcode.value = true
}

defineExpose({
  showDialog,
})
</script>
<style lang="scss" scoped>
$zone-multiplier: 1;

#author-qrcode {
  // width: $zone-multiplier * 580rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: $zone-multiplier * 600rpx;
  height: $zone-multiplier * 800rpx;
  background-image: url('https://sacdn.850g.com/football/static/share_bg.png?123');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  text-align: center;
  padding-top: $zone-multiplier * 40rpx;

  .avatar {
    width: $zone-multiplier * 100rpx;
    height: $zone-multiplier * 100rpx;
    border-radius: 50%;
  }

  .author-name {
    margin: $zone-multiplier * 12rpx 0 $zone-multiplier * 9rpx;
    color: #fff;
    font-size: $zone-multiplier * 30rpx;
    line-height: $zone-multiplier * 40rpx;
    text-align: center;
  }

  .focus {
    color: #fff;
    font-size: $zone-multiplier * 26rpx;
  }

  .prompt {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: $zone-multiplier * 490rpx;
    height: $zone-multiplier * 115rpx;
    padding: $zone-multiplier * 14rpx 0 $zone-multiplier * 15rpx;
    margin: $zone-multiplier * 28rpx 0 $zone-multiplier * 74rpx;
    border-radius: $zone-multiplier * 12rpx;
    background: rgba(255, 255, 255, 0.14);
    font-size: $zone-multiplier * 26rpx;
    color: white;
    line-height: $zone-multiplier * 40rpx;

    &-item {
      &:last-child {
        margin-top: $zone-multiplier * 6rpx;
      }
    }
  }

  .qrcode {
    width: $zone-multiplier * 273rpx;
    height: $zone-multiplier * 273rpx;
    border-radius: $zone-multiplier * 12rpx;
  }

  // .tip {
  //   width: $zone-multiplier * 414rpx;
  //   height: $zone-multiplier * 100rpx;
  //   text-align: center;
  //   margin: $zone-multiplier * 35rpx auto 0;
  // }

  // .content {
  //   background-color: #fff;
  //   border-radius: $zone-multiplier * 12rpx;
  //   margin: $zone-multiplier * 30rpx auto;
  //   padding: $zone-multiplier * 60rpx $zone-multiplier * 30rpx;
  //   width: $zone-multiplier * 440rpx;

  //   .title {
  //     color: rgba(0, 0, 0, 0.9);
  //     text-align: center;
  //     font-size: $zone-multiplier * 32rpx;
  //     font-weight: 600;
  //   }

  //   .qrcode {
  //     width: $zone-multiplier * 350rpx;
  //     height: $zone-multiplier * 350rpx;
  //   }

  //   .avatar {
  //     width: $zone-multiplier * 60rpx;
  //     height: $zone-multiplier * 60rpx;
  //     border-radius: 50%;
  //     margin-right: $zone-multiplier * 10rpx;
  //   }
  // }

  // .bottom {
  //   color: #fff;
  //   border-radius: $zone-multiplier * 42rpx;
  //   background: #ab110f;
  //   text-align: center;
  //   font-size: $zone-multiplier * 26rpx;
  //   margin: auto;
  //   width: $zone-multiplier * 550rpx;
  //   height: $zone-multiplier * 84rpx;
  // }
}
</style>
