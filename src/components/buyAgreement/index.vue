<template>
  <wd-popup v-model="show" @close="closeQrcode"
    custom-style="background-color: #fff;overflow: none;border-radius: 10px;padding-bottom: 20px;">
    <scroll-view scroll-y class="content" :show-scrollbar="false">
      <view class="info">
        <view class="text-center mb-[20rpx]">
          <text class="font-weight-bold">用户购买协议</text>
        </view>
        <view>尊敬的神鱼体育平台用户，请您在购买付费阅读服务前，务必谨慎阅读以下条款:</view>
        <view>
          1、[服务提示]长沙神鱼体育网络科技有限公司
          (以下称“神鱼体育平台服务提供者”)系提供信息存储空间的网络服务提供者，除另行协商签订付费协议外，仅为您提供免费的神鱼体育平台服务。
        </view>
        <view>
          2、[交易主体]请您了解，您在神鱼体育平台购买的付费文章及内容作品均系由作品页面上标示的用户为您提供，我司并非作品内容的提供者和销售者。请您明白，您一旦支付费用购买我司平台上其他用户提供的相关作品内容服务，即与提供作品内容服务的作者建立了文章作品服务合同关系，我司不构成该服务合同关系的任一方。
        </view>
        <view>
          3、[作品内容服务不适用七日无理由退货]请您知悉神鱼体育平台上提供的作品为在线阅读、收听的数字化商品，根据《消费者权益保护法》等规定，不适用七日无理由退货规定(您与作品提供者达成退款意向的除外)，请您于购买时谨慎考虑。
        </view>
        <view>
          4、[退款相关解决办法]神鱼体育平台的用户，在购买付费内容时，所支付的资金如需退款，请联系作品提供者，平台不承担对应款项的退款责任！
        </view>
        <view>
          4.1、[特殊情形时的平台方协商]
          神鱼体育平台仅将出现特殊情况时(包含但不限于如作品质量较差、作品无法阅读等题文不符的内容时)，协助您与作品提供者协商退款事宜。
        </view>
        <view>
          5、[退款金额]您的退款申请符合上述第4.1条的退款情形的，您支付的款项将原路全额退回您的账户，但若因退款产生第三方支付平台手续费的，将在扣除该手续费后退回剩余款项。若您对以上条款有疑问，敬请您联系神鱼体育平台客服。
        </view>
        <view class="text-right mt-[20rpx]">神鱼体育平台服务提供者神鱼体育</view>
      </view>
    </scroll-view>
    <view class="flex justify-space">
      <button class="btn-box" @click="pass">我已阅读并同意</button>
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
const show = ref(false)
const emit = defineEmits(['close', 'pass'])

const closeQrcode = () => {
  show.value = false
  emit('close')
}

const pass = async () => {
  show.value = false
  emit('pass')
}

const showDialog = async () => {
  show.value = true
}
defineExpose({
  showDialog,
})
</script>
<style lang="scss" scoped>
.content {
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  height: 70vh;
  width: 75vw;
  overflow: unset;
}

.info {
  margin-top: 20px;
  font-size: 14px;
  line-height: 24px;
  color: #333;
}

.btn-box {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 30rpx;
  color: white;
  background-color: rgb(209, 48, 46);
  border-radius: 20rpx;
}
</style>
