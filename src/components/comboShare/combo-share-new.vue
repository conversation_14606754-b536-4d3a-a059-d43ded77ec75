<template>
  <wd-popup
    v-model="showModal"
    @close="closeModal"
    custom-style="background: transparent;border-radius: 24rpx; overflow: hidden;"
  >
    <view class="combo-share-canvas">
      <img width="100%" v-show="base64Data" :src="base64Data" />
    </view>
    <view style="position: absolute; top: 100%; left: 0; z-index: 0">
      <view
        class="combo-share-wrap"
        id="scoreimg"
        :style="{ backgroundImage: `url(${defaultTopBg})` }"
      >
        <text class="article-title">{{ privilege.privilegeName }}</text>
        <text class="article-content">{{ privilege.content }}</text>
        <image :src="privilege.qrcode" class="qrcode" />

        <view class="flex justify-center text-white items-center">
          <image :src="privilege.avatarUrl" class="pic" />
          <text class="txt">{{ privilege.authorName }}</text>
          <template v-if="privilege.type !== 4 && privilege.price">
            <image src="https://sacdn.850g.com/football/static/gold2.svg" class="pic" />
            <text class="txt">{{ privilege.price }}</text>
          </template>
          <template v-else>
            <image src="https://sacdn.850g.com/football/static/gold2.svg" class="pic" />
            <text class="txt">{{ privilege.children[0].price }}</text>
          </template>
        </view>
      </view>
      <!-- <view class="combo-share-wrap" id="scoreimg">
        <view class="combo-share-cont">
          <view class="title">
            {{ privilege.date || 0 }}日内畅读，最大优惠{{
              calculateDiscount() - (privilege.price || 0)
            }}元
          </view>
          <view class="cont-after">
            <view class="user-info">
              <image :src="privilege.avatarUrl" class="rounded-full" />
              <view class="user-text">
                <text class="username">{{ privilege.authorName }}</text>
                <text class="intro">{{ privilege.content }}</text>
              </view>
            </view>
            <view class="price">
              <view class="price-yuanjia">
                <text>原价：</text>
                <text class="shanchu">{{ calculateDiscount() }}</text>
              </view>
              <view class="price-xianjia">
                <img src="@/static/images/share/taocan_02.png" alt="" width="16"
                  style="position: relative; top: 6rpx" />
                <text style="padding-left: 10rpx">优惠价：{{ privilege.price }}</text>
              </view>
            </view>
            <view class="qrcode">
              <image :src="privilege.qrcode" alt="" />
            </view>
            <view class="f-text">长按识别二维码阅读专辑</view>
          </view>
        </view>
        <view class="image-container">
          <image src="https://sacdn.850g.com/football/static/scheme/top_bg.png" mode="aspectFit" />
        </view>
      </view> -->
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { getPrivilegeShareImg } from '@/api/combo'
import html2canvas from 'html2canvas'
const privilege = ref({})
const base64Data = ref('')
// 弹窗显示状态
const showModal = ref(false)
const loading = ref(false)
const defaultTopBg = ref('https://sacdn.850g.com/football/static/scheme/top_bg.png')

// 显示弹窗
const showDialog = async (data: any, bg) => {
  showModal.value = true
  loading.value = true
  console.info(data, bg)
  defaultTopBg.value = bg.value || defaultTopBg.value
  uni.showToast({
    title: '正在生成图片...',
    duration: 1000,
    icon: 'loading',
  })
  privilege.value = data

  console.log(privilege.value)
  setTimeout(() => {
    saveImage()
  }, 500)
}

// 关闭弹窗
const closeModal = () => {
  showModal.value = false
}

const saveImage = async () => {
  console.log('保存图片')
  const scoreimg = document.getElementById('scoreimg')
  // 使用 html2canvas 将 DOM 元素转换为图片
  const canvas = await html2canvas(scoreimg, {
    scale: window.devicePixelRatio || 2,
    useCORS: true, // 处理跨域问题
    allowTaint: true,
    logging: false,
    backgroundColor: null,
  })
  // 将 canvas 转换为 Base64 数据
  base64Data.value = canvas.toDataURL('image/png')
  loading.value = false
}

const calculateDiscount = () => {
  const num = parseFloat(privilege.value.num) || 0
  const articlePrice = parseFloat(privilege.value.articlePrice) || 0
  const date = parseFloat(privilege.value.date) || 0
  const price = parseFloat(privilege.value.price) || 0

  const originalPrice = parseFloat((num * articlePrice * date).toFixed(2))
  return isNaN(originalPrice) ? 0 : originalPrice
}

defineExpose({
  showDialog,
  base64Data,
})
</script>

<style lang="scss">
$zone-multiplier: 1;

.combo-share-canvas {
  width: 710rpx;
  height: 731rpx;
  position: relative;
  z-index: 9;
}

.combo-share-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 710rpx;
  height: 731rpx;
  position: relative;
  // background-image: url('https://sacdn.850g.com/football/static/scheme/top_bg.png');
  background-size: 100% 100%;

  .article-title {
    margin-top: $zone-multiplier * 50rpx;
    margin-bottom: $zone-multiplier * 30rpx;
    font-size: $zone-multiplier * 40rpx;
    color: #fff;
  }

  .article-content {
    display: -webkit-box;
    width: 100%;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    overflow: hidden;
    text-align: center;
    font-size: $zone-multiplier * 32rpx;
    color: #fff;
  }

  .qrcode {
    width: $zone-multiplier * 300rpx;
    height: $zone-multiplier * 300rpx;
    border-radius: $zone-multiplier * 12rpx;
    margin: $zone-multiplier * 50rpx 0;
  }

  .pic {
    width: $zone-multiplier * 60rpx;
    height: $zone-multiplier * 60rpx;
    margin-left: $zone-multiplier * 50rpx;
    border-radius: 50%;
  }

  .txt {
    margin-left: $zone-multiplier * 20rpx;
    font-size: $zone-multiplier * 32rpx;
  }
}

.combo-share-cont {
  width: calc(100% - 48rpx);
  height: calc(100% - 128rpx);
  background: #fff;
  position: absolute;
  left: 24rpx;
  top: 60rpx;
  z-index: 1;
  border-radius: 24rpx;
  overflow: hidden;

  .title {
    font-size: 34rpx;
    color: #cf302c;
    text-align: center;
    padding: 24rpx;
  }

  .cont-after {
    padding: 0 90rpx;
    box-sizing: border-box;
  }

  .user-info {
    background: #fbeaeb;
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    border-radius: 6rpx;

    .rounded-full {
      width: 90rpx;
      height: 90rpx;
      margin-right: 20rpx;
      /* 添加右边距 */
    }

    .user-text {
      max-width: calc(100% - 110rpx);
      display: flex;
      flex-direction: column;
    }

    .username {
      font-size: 30rpx;
      color: #333;
    }

    .intro {
      font-size: 26rpx;
      color: #999;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .price {
    font-size: 30rpx;
    text-align: center;
    padding: 24rpx 0;
    display: flex;
    justify-content: space-between;

    .price-yuanjia {
      color: #999;

      .shanchu {
        position: relative;

        &:after {
          content: '';
          width: 100%;
          height: 1px;
          background: #999;
          position: absolute;
          left: 0;
          top: 50%;
          z-index: 1;
        }
      }
    }

    .price-xianjia {
      color: #cf302c;
    }
  }

  // .qrcode {
  //   width: 200rpx;
  //   height: 200rpx;
  //   border: dashed 1rpx #999;
  //   margin: 0 auto;

  //   image {
  //     width: 100%;
  //     height: 100%;
  //   }
  // }
}

.image-container {
  width: 710rpx;
  height: 731rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;

  image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 24rpx;
  }
}

.f-text {
  text-align: center;
  color: #999;
  padding-top: 10rpx;
}

.combo-share-btn {
  margin: 16rpx auto;
  background: #fff;
  border-radius: 50rpx;
  text-align: center;
  line-height: 100rpx;
  width: 80%;
}
</style>
