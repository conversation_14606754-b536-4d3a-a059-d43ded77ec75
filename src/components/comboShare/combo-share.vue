<template>
  <wd-popup
    v-model="showArticleQrcode"
    @close="closeQrcode"
    custom-style="background: none;text-align: center;"
  >
    <wd-loading color="#d1302e" v-if="loading" />
    <view style="height: 0; overflow: hidden">
      <!-- <view> -->
      <view id="article-qrcode" ref="qrcodeRef" v-show="!base64Data">
        <!-- <view id="article-qrcode" > -->
        <!-- <image :src="showData.avatar" class="avatar"/> -->
        <text class="article-title">{{ showData.title }}</text>
        <text class="article-content">{{ showData.intro }}</text>
        <image :src="showData.qrcode" class="qrcode" />

        <view class="flex justify-center text-white items-center">
          <image :src="showData.avatar" class="pic" />
          <text class="txt">{{ showData.username }}</text>
          <image src="https://sacdn.850g.com/football/static/gold2.svg" class="pic" />
          <text class="txt">{{ showData.price }}</text>
        </view>
        <!-- <view class="content">
          <view class="title">{{ showData.title }}</view>
          <view class="intro">{{ showData.intro }}</view>
          <image class="qrcode" :src="showData.qrcode" />
          <view class="author items-center flex justify-center">
            <view><img class="avatar right" :src="showData.avatar" /></view>
            <text class="username">{{ showData.username }}</text>
            <image class="gold" src="https://sacdn.850g.com/football/static/gold.svg" />
            <text class="price">
              {{ showData.price }}
            </text>
          </view>
        </view>
        <view class="bottom items-center flex justify-center text-center">
          <text>长按识别二维码查看详情</text>
        </view> -->
      </view>
    </view>
    <view>
      <img style="height: 800rpx; width: 600rpx" v-show="base64Data" :src="base64Data" />
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import { updateArticleShareInfo } from '@/api/article'
import { getPrivilegeShareImg } from '@/api/combo'
import html2canvas from 'html2canvas'
const showArticleQrcode = ref(false)
const base64Data = ref('')
const loading = ref(false)
const showData = ref({
  title: '文章标题',
  intro: '简介',
  username: '这里显示用户昵称',
  price: '58.0',
  qrcode: '',
  avatar: '',
})
const emit = defineEmits(['close'])
const init = ref(false)
const qrcodeRef = ref()

const closeQrcode = () => {
  showArticleQrcode.value = false
  console.log('closeQrcode')
  emit('close')
}

const initImage = async () => {
  // const scoreimg = document.getElementById('article-qrcode')
  // const scoreimg = qrcodeRef.value
  // // 使用 html2canvas 将 DOM 元素转换为图片
  // const canvas = await html2canvas(scoreimg, {
  //   scale: window.devicePixelRatio || 2,
  //   useCORS: true, // 处理跨域问题
  //   allowTaint: true,
  //   logging: false,
  //   backgroundColor: null,
  // })

  // 将 canvas 转换为 Base64 数据
  // base64Data.value = canvas.toDataURL('image/svg')
  loading.value = false
  init.value = true
}

// const getData = async (id) => {
//   console.log('获取文章数据', id)
//   const data = await getArticleShareImg(id)
//   imageSrc.value = data
// }

const getData = async (id) => {
  console.log('获取文章数据', id)
  loading.value = true
  showArticleQrcode.value = true
  console.log('showArticleQrcode', showArticleQrcode.value)
  try {
    const data = await getPrivilegeShareImg(id)
    // showData.value = data
    base64Data.value = data
    loading.value = false
    setTimeout(() => {
      initImage()
    }, 1000)
  } catch (err) {
    showArticleQrcode.value = false
  }
}

const showDialog = async (id) => {
  console.log('aaaaaaaaaaaaaaa')
  base64Data.value = ''
  await getData(id)
  showArticleQrcode.value = true
}

const uploadPic = (id) => {
  let timeId = setInterval(async () => {
    if (init.value) {
      clearInterval(timeId)
      console.log('上传图片', id)
      const data = { id, base64Data: base64Data.value }
      await updateArticleShareInfo(data)
      console.log('上传图片成功')
    }
  }, 500)
}

defineExpose({
  showDialog,
  uploadPic,
})
</script>
<style lang="scss" scoped>
$zone-multiplier: 1;

#article-qrcode {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: $zone-multiplier * 600rpx;
  height: $zone-multiplier * 800rpx;
  // background-image: url('https://sacdn.850g.com/football/static/share/background.png');
  background-image: url('https://sacdn.850g.com/football/static/share_bg.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  text-align: center;
  padding: $zone-multiplier * 40rpx $zone-multiplier * 30rpx 0;
  // padding-bottom: $zone-multiplier * 50rpx;
  // padding-top: $zone-multiplier * 130rpx;

  .author-name {
    margin-top: $zone-multiplier * 12rpx;
    margin-bottom: $zone-multiplier * 9rpx;
    font-size: $zone-multiplier * 30rpx;
    color: #fff;
    line-height: $zone-multiplier * 40rpx;
  }

  .article-title {
    margin-top: $zone-multiplier * 50rpx;
    margin-bottom: $zone-multiplier * 30rpx;
    font-size: $zone-multiplier * 40rpx;
    color: #fff;
  }

  .article-content {
    display: -webkit-box;
    width: 100%;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
    overflow: hidden;
    font-size: $zone-multiplier * 32rpx;
    color: #fff;
  }

  .qrcode {
    width: $zone-multiplier * 300rpx;
    height: $zone-multiplier * 300rpx;
    border-radius: $zone-multiplier * 12rpx;
    margin: $zone-multiplier * 50rpx 0;
  }

  .pic {
    width: $zone-multiplier * 60rpx;
    height: $zone-multiplier * 60rpx;
    margin-left: $zone-multiplier * 50rpx;
    border-radius: 50%;
  }

  .txt {
    margin-left: $zone-multiplier * 20rpx;
    font-size: $zone-multiplier * 32rpx;
  }

  .gap-x {
    margin-left: $zone-multiplier * 80rpx;
  }
}
</style>
