<template>
  <view class="wrapper">
    <wd-popup v-model="visible" custom-class="mp-modal" @close="closeModal">
      <view class="mp-content">
        <image :src="code" class="absolute w-full h-full opacity-0" />
        <text class="text-white text-32rpx leading-40rpx">长按识别二维码</text>
        <text class="text-white text-28rpx leading-40rpx text-opacity-80 mt-10rpx">
          {{ type == 1 ? '关注公众号' : '添加您的专属服务小助理' }}
        </text>
        <!-- 二维码 -->
        <image :src="code" class="w-350rpx h-350rpx mt-48rpx mb-58rpx" />
        <!-- 底部文案 -->
        <view
          class="flex flex-col items-center w-520rpx h-450rpx rounded-xl pt-30rpx pb-40rpx"
          style="background: rgba(90, 3, 0, 0.3)"
        >
          <text class="text-white text-28rpx leading-40rpx">
            · {{ type == 1 ? '关注公众号可获得以下服务' : '添加我的企业微信可获得以下服务' }} ·
          </text>
          <!-- 第一行 -->
          <view class="flex mt-30rpx gap-x-70rpx">
            <view class="flex flex-col items-center gap-y-20rpx">
              <view
                class="flex justify-center items-center w-80rpx h-80rpx rounded-full"
                style="background-color: rgba(90, 3, 0, 0.4)"
              >
                <image
                  src="https://sacdn.850g.com/football/static/mp/push.png"
                  class="w-48rpx h-48rpx"
                />
              </view>
              <text class="text-white text-28rpx leading-40rpx">作者方案推送</text>
            </view>
            <view class="flex flex-col items-center gap-y-20rpx">
              <view
                class="flex justify-center items-center w-80rpx h-80rpx rounded-full"
                style="background-color: rgba(90, 3, 0, 0.4)"
              >
                <image
                  src="https://sacdn.850g.com/football/static/mp/discount.png"
                  class="w-48rpx h-48rpx"
                />
              </view>
              <text class="text-white text-28rpx leading-40rpx">专属优惠权益</text>
            </view>
          </view>
          <!-- 第二行 -->
          <view class="flex mt-30rpx gap-x-70rpx">
            <view class="flex flex-col items-center gap-y-20rpx">
              <view
                class="flex justify-center items-center w-80rpx h-80rpx rounded-full"
                style="background-color: rgba(90, 3, 0, 0.4)"
              >
                <image
                  src="https://sacdn.850g.com/football/static/mp/issue.png"
                  class="w-48rpx h-48rpx"
                />
              </view>
              <text class="text-white text-28rpx leading-40rpx">一对一问题处理</text>
            </view>
            <view class="flex flex-col items-center gap-y-20rpx">
              <view
                class="flex justify-center items-center w-80rpx h-80rpx rounded-full"
                style="background-color: rgba(90, 3, 0, 0.4)"
              >
                <image
                  src="https://sacdn.850g.com/football/static/mp/notice.png"
                  class="w-48rpx h-48rpx"
                />
              </view>
              <text class="text-white text-28rpx leading-40rpx">福利活动提醒</text>
            </view>
          </view>
        </view>
      </view>
      <wd-icon
        name="close-outline"
        size="64rpx"
        class="mx-auto"
        color="white"
        @click="closeModal"
      ></wd-icon>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
const visible = ref(false)

defineProps<{ code: string | undefined; type: number | undefined }>()
const emit = defineEmits<{ (e: 'afterClose'): void }>()

function open() {
  visible.value = true
}

function closeModal() {
  visible.value = false
  emit('afterClose')
}

defineExpose({ open })
</script>
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared', // 解除样式隔离
  },
}
</script>
<style lang="scss" scoped>
.wrapper {
  :deep(.mp-modal) {
    width: 580rpx;
    border-radius: 12rpx;
    background-color: unset;
    text-align: center;

    .mp-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      height: 1080rpx;
      margin-bottom: 35rpx;
      padding-top: 54rpx;
      // background-image: url('https://sacdn.850g.com/football/static/mp_bg.png');
      background-image: url('https://sacdn.850g.com/football/static/mp_bg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      text-align: center;
      box-sizing: border-box;
    }
  }
}
</style>
