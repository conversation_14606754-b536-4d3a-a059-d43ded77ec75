<template>
  <wd-message-box selector="wd-message-add-qq-child">
    <wd-row>输入微信号</wd-row>
    <wd-row>输入微信号以便于更好的推送方案</wd-row>
    <wd-form ref="qQFormRef" :model="qQFormData">
      <wd-input
        label="微信号"
        label-width="120rpx"
        prop="qq"
        clearable
        :maxlength="12"
        :minlength="6"
        type="idcard"
        custom-style="border-bottom:1px solid #eee;"
        v-model="qQFormData.qq"
        placeholder="请输入微信号"
        :rules="[{ required: true, message: '请输入微信号', trigger: 'blur' }]"
      />
    </wd-form>
  </wd-message-box>
</template>
<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store'
import { updateUserQQ } from '@/api/user'
import { getUserInfo } from '@/service/userService'
const userStore = useUserStore()
const childMessageBox = useMessage('wd-message-add-qq-child')
const qQFormRef = ref()
const qQFormData = ref({
  qq: '',
})
// 打开新增子账号弹窗
const showQQ = () => {
  qQFormData.value.qq = userStore.userInfo.qq
  childMessageBox
    .confirm({
      beforeConfirm: ({ resolve }) => {
        qQFormRef.value.validate().then(async ({ valid }) => {
          if (valid) {
            try {
              await updateUserQQ(qQFormData.value)
              const userInfoResult = await getUserInfo()
              userStore.setUserInfo(userInfoResult)
              uni.showToast({ title: '保存成功' })
            } catch (e) {
              resolve(false)
              return
            }

            resolve(true)
          } else {
            resolve(false)
          }
        })
      },
    })
    .then(async () => {})
}

defineExpose({
  showQQ,
})
</script>
