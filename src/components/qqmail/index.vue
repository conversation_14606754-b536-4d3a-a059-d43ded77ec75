<template>
  <wd-fab
    :expandable="false"
    position="right-bottom"
    class="mt-[-400rpx] ml-[-10rpx] rounded-[50%]"
    v-if="!userStore.userInfo?.isAuthor"
  >
    <template #trigger>
      <view
        @click="popupShow = true"
        class="flex items-center justify-center flex-col w-[90rpx] h-[90rpx] bg-[#d1302e] rounded-[50%] text-[24rpx] text-white text-center"
      >
        <wd-row>联系</wd-row>
        <wd-row>作者</wd-row>
      </view>
    </template>
  </wd-fab>
  <wd-popup v-model="popupShow" custom-style="border-radius:32rpx;z-index:9999;width: 90%;">
<!--    <view class="text-center font-medium text-43rpx py-20rpx">神鱼体育客服</view>-->
   <image class="w-600rpx h-800rpx" style="margin-left: calc(50% - 300rpx)" :src="qrCode"></image>
<!--    <view class="text-center text-#999999 text-24rpx py-30rpx">长按上图二维码图案, 即可加我企业微信</view>-->
  </wd-popup>
<!--  <wd-message-box selector="wd-message-add-child">-->
<!--    <wd-row>温馨提示：</wd-row>-->
<!--    <wd-row>为了保证消息及时,请确保qq和微信绑定,可见qq邮箱提醒,如有操作细节问题,详询客服</wd-row>-->
<!--    <a href="#" @click="openQQGuide">操作指引</a>-->
<!--    <wd-form ref="qQFormRef" :model="qQFormData">-->
<!--      <wd-input-->
<!--        label="QQ"-->
<!--        label-width="100rpx"-->
<!--        prop="qqMail"-->
<!--        clearable-->
<!--        :maxlength="12"-->
<!--        :minlength="6"-->
<!--        type="idcard"-->
<!--        custom-style="border-bottom:1px solid #eee;"-->
<!--        v-model="qQFormData.qqMail"-->
<!--        placeholder="请输入QQ"-->
<!--        :rules="[{ required: true, message: '请输入QQ', trigger: 'blur' }]"-->
<!--      />-->
<!--    </wd-form>-->
<!--  </wd-message-box>-->
</template>
<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store'
import { updateUserQQMail } from '@/api/user'
import { getUserInfo } from '@/service/userService'
const userStore = useUserStore()
const childMessageBox = useMessage('wd-message-add-child')
const qQFormRef = ref()
const qQFormData = ref({
  qqMail: '',
})

defineProps<{
  qrCode: string;
}>()

const openQQGuide = () => {
  uni.previewImage({ urls: ['https://sacdn.850g.com/football/config/qq_bind_wx.gif'] })
}

const popupShow = ref(false)
// 打开新增子账号弹窗
const showQQ = () => {
  // qQFormData.value.qqMail = userStore.userInfo.qqMail
  // childMessageBox
  //   .confirm({
  //     beforeConfirm: ({ resolve }) => {
  //       qQFormRef.value.validate().then(async ({ valid }) => {
  //         if (valid) {
  //           try {
  //             await updateUserQQMail(qQFormData.value)
  //             const userInfoResult = await getUserInfo()
  //             userStore.setUserInfo(userInfoResult)
  //             uni.showToast({ title: '保存成功' })
  //           } catch (e) {
  //             resolve(false)
  //             return
  //           }
  //
  //           resolve(true)
  //         } else {
  //           resolve(false)
  //         }
  //       })
  //     },
  //   })
  //   .then(async () => {})
}
</script>

<style lang="scss" scoped>
 :deep(.wd-overlay) {
   z-index: 999 !important;
  }
</style>
