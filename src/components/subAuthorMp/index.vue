<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen container">
    <image :src="mpAppInfo.appQrCode" class="absolute w-full h-full opacity-0" />
    <view class="text-center mt-[100rpx]">
      <image :src="options.avatarUrl" style="width: 120rpx; height: 120rpx; border-radius: 50%" />
    </view>
    <view class="text-area text-center mt-[20rpx] text-[#fff]">
      <view class="font-size-[36rpx] font-bold">{{ options.username }}</view>
      <view class="font-size-[32rpx] mt-[10rpx]">快来关注我吧，我有很多方案！</view>
    </view>
    <view class="text-center mt-[100rpx] w-[400rpx] h-[400rpx] m-auto">
      <image :src="mpAppInfo.appQrCode" style="width: 400rpx; height: 400rpx" />
    </view>
    <view class="flex items-center justify-center btn-tip">
      <view class="mr-[40rpx]">
        <image src="/static/images/share/ss.svg" style="width: 100rpx; height: 100rpx" />
      </view>
      <view>
        <view class="text-[#ffff] font-[34rpx]">接收消息推送，只差一步啦~</view>
        <view class="text-[#FFEA00] font-[34rpx] mt-[10rpx]">长按识别二维码关注公众号</view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { getAppQrcode } from '@/service/userService'
//接受引入页面传入的参数
const options = defineProps({
  authorId: Number,
  avatarUrl: String,
  username: String,
  articleId: Number,
  shareType: {
    type: Number,
    default: 0,
  },
})
const mpAppInfo = ref()

const showQrcode = () => {
  getAppQrcode(1, options.authorId, options.articleId, options.shareType).then((data) => {
    mpAppInfo.value = data
  })
}

onMounted(() => {
  showQrcode()
})
</script>
<style lang="scss" scoped>
.container {
  background-image: url('http://sacdn.850g.com/football/config/submp_bg.png');
  background-size: cover;
}
.btn-tip {
  margin: 100rpx auto 0rpx;
  border-radius: 79px;
  width: 690rpx;
  height: 158rpx;
  flex-shrink: 0;
  background: rgba(105, 19, 19, 0.8);
}
</style>
