<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen container">
    <view class="text-center mt-[100rpx] w-[400rpx] h-[400rpx] m-auto">
      <image :src="mpAppInfo.appQrCode" style="width: 400rpx; height: 400rpx" />
    </view>
    <view class="flex items-center justify-center btn-tip">
      <view class="mr-[40rpx]">
        <image src="/static/images/share/ss.svg" style="width: 80rpx; height: 80rpx" />
      </view>
      <view>
        <view class="text-[#ffff] font-[30rpx]">接收消息推送，只差一步啦~</view>
        <view class="text-[#FFEA00] font-[30rpx] mt-[10rpx]">长按识别二维码关注公众号</view>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { getAppQrcode } from '@/service/userService'
//接受引入页面传入的参数
const options = defineProps({
  authorId: Number,
  articleId: Number,
})
const mpAppInfo = ref()

const showQrcode = () => {
  getAppQrcode(1, options.authorId, options.articleId).then((data) => {
    mpAppInfo.value = data
  })
  console.log('showQrcode', mpAppInfo.value)
}

onMounted(() => {
  showQrcode()
})
</script>
<style lang="scss" scoped>
$zone-multiplier: 1;
.container {
  background-image: url('http://sacdn.850g.com/football/config/submp_bg.png');
  background-size: cover; /* 确保背景图片覆盖整个容器 */
  background-position: center; /* 居中显示背景图片 */
  background-repeat: no-repeat; /* 防止背景图片重复 */
  height: $zone-multiplier * 800rpx; /* 设置容器高度 */
  min-height: 550rpx; /* 设置最小高度 */
}
.btn-tip {
  margin: 100rpx auto 0rpx;
  border-radius: 79rpx;
  width: 690rpx;
  height: 158rpx;
  flex-shrink: 0;
  background: rgba(105, 19, 19, 0.8);
}
</style>
