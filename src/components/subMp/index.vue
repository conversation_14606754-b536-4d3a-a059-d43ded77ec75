<template>
  <wd-fab
    :expandable="false"
    position="right-bottom"
    class="mt-[-400rpx] ml-[-10rpx] rounded-[50%]"
  >
    <template #trigger>
      <view
        @click="goShare"
        class="flex items-center justify-center flex-col w-[90rpx] h-[90rpx] bg-[#d62c2c] rounded-[50%] text-[26rpx] text-white text-center"
      >
        <wd-row>我要</wd-row>
        <wd-row>订阅</wd-row>
      </view>
    </template>
  </wd-fab>
  <wd-popup v-model="pushVisible" close-on-click-overlay="true">
    <view v-if="pushVisible">
      <detailMp ref="mpdetailRef" :author-id="options.authorId" :article-id="options.articleId" />
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import detailMp from '@/components/subMp/detail.vue'
const pushVisible = ref(false)
const emit = defineEmits(['close'])
const options = defineProps({
  authorId: Number,
  articleId: Number,
})
const mpdetailRef = ref()
function goShare() {
  pushVisible.value = true
}
const closeQrcode = () => {
  pushVisible.value = false
  emit('close')
}
</script>
<style lang="scss" scoped>
.popup-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  width: 600rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;
}
.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  cursor: pointer;
}
</style>
