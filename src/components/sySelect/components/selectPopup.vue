<template>
  <wd-popup v-model="show" position="bottom" custom-style="background:transparent;">
    <view class="select-popup">
      <view class="select-popup__header">
        {{ title }}
        <view @click="close" class="close-wrap">
          <wd-icon name="close" custom-class="close-icon" />
        </view>
      </view>
      <!-- 搜索框 -->
      <view class="select-popup__search">
        <view class="search-content">
          <wd-icon name="search" size="18px" color="#d9d9d9" />
          <view class="search-content__input">
            <wd-input placeholder="搜索" clearable @input="handleSearch" v-model="searchKey" />
          </view>
        </view>
      </view>
      <!-- options -->
      <scroll-view scroll-y :show-scrollbar="false">
        <view class="option-content">
          <view v-for="e in currentOptions" :key="e[valueKey]" class="option-content__item"
            @click="handleOptionClick(e)">
            <text class="option-text">
              {{ e[labelKey] }}
            </text>
            <template v-if="checkedItem && e[valueKey] === checkedItem[valueKey]">
              <wd-icon name="check" color="	#D1302E" />
            </template>
          </view>
        </view>
      </scroll-view>
      <!-- 分页 -->
      <view>
        <wd-pagination v-model="pageNo" :total="total" :page-size="pageSize" show-icon />
      </view>
      <!-- 确认按钮 -->
      <view class="select-popup__footer">
        <wd-button block size="large" @click="handleConfirm">确认</wd-button>
      </view>
    </view>
  </wd-popup>
</template>
<script setup lang="ts">
import { isNil, debounce } from 'lodash-es'

const props = withDefaults(
  defineProps<{
    value: string | number | null
    options: (string | number)[]
    title?: string
    labelKey: string
    valueKey: string
    pageSize: number
  }>(),
  {
    title: '请选择',
  },
)

const emit = defineEmits<{ (e: 'confirm', value: string | number | null): void }>()

const show = ref(false)

const pageNo = ref(1)
const filtOptions = ref([])
const total = computed(() => filtOptions.value.length)

const checkedItem = ref<any>(null)

const searchKey = ref('')

const handleSearch = debounce(({ value }: { value: string }) => {
  const l = props.labelKey
  filtOptions.value = props.options.filter((e) => e[l].includes(value))
  pageNo.value = 1
}, 600)

const currentOptions = computed(() => {
  const startIndex = (pageNo.value - 1) * 10
  const endIndex = Math.min(startIndex + 10, filtOptions.value.length)
  return filtOptions.value.slice(startIndex, endIndex)
})

function open() {
  show.value = true
  const kv = props.valueKey
  if (!isNil(props.value)) {
    checkedItem.value = props.options.find((e) => e[kv] === props.value)
  }

  pageNo.value = 1
  filtOptions.value = props.options
}

function close() {
  show.value = false
}

function handleOptionClick(e: any) {
  if (!checkedItem.value || e[props.valueKey] !== checkedItem.value[props.valueKey]) {
    checkedItem.value = e
  }
}

function handleConfirm() {
  emit('confirm', checkedItem.value)
  close()
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.select-popup {
  border-radius: var(--sy-action-radius, 16px) var(--sy-action-radius, 16px) 0 0;
  background-color: white;
  padding-bottom: 30px;

  &__header {
    position: relative;
    height: 72px;
    line-height: 72px;
    text-align: center;

    .close-wrap {
      position: absolute;
      top: var(--sy-action-close, 25px);
      right: var(--sy-action-close, 15px);
      width: 16px;
      height: 16px;
      line-height: 16px;
    }
  }

  &__search {
    padding: var(--sy-search-padding, 10px) var(--sy-search-side-padding, 15px);
    height: 30px;

    .search-content {
      display: flex;
      align-items: center;
      column-gap: 8px;
      height: 100%;
      padding: 0 16px;
      border-radius: var(--sy-search-input-radius, 15px);
      background-color: var(--sy-search-bg, #f5f5f5);

      &__input {
        --wot-input-bg: #f5f5f5;
        --wot-input-inner-height: 30px;
        --wot-input-not-empty-border-color: transparent;
        flex: 1;
      }
    }
  }

  .option-content {
    height: 314px;
    padding: 0 10px;
    font-size: 14px;

    &__item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 13px 15px;
      line-height: 20px;
    }
  }

  &__footer {
    padding: 24px 15px;
  }
}
</style>
