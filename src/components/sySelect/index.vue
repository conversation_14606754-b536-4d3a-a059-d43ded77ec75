<template>
  <view class="sy-select" @click.native="openPopup">
    <view v-if="modelValue" class="flex items-center">
      <text class="sy-select__content">{{ display }}</text>
      <template v-if="clearable">
        <wd-icon name="error-fill" color="rgba(0,0,0,0.85)" @click.stop="clear" />
      </template>
    </view>
    <view v-else class="flex items-center">
      <text class="sy-select__placeholder">{{ placeholder }}</text>
      <wd-icon name="arrow-right" size="16px" color="rgba(0,0,0,0.25)" />
    </view>
  </view>
  <select-popup
    :value="modelValue"
    :options="options"
    ref="popupRef"
    :label-key="labelKey"
    :value-key="valueKey"
    :page-size="pageSize"
    @confirm="onConfirm"
  />
</template>
<script setup lang="ts">
import { isNil } from 'lodash-es'
import selectPopup from './components/selectPopup.vue'

interface ISelectProps {
  modelValue: string | number | null
  pageSize?: number
  labelKey?: string
  valueKey?: string
  options?: any[]
  placeholder?: string
  clearable?: boolean
}

const props = withDefaults(defineProps<ISelectProps>(), {
  labelKey: 'label',
  valueKey: 'value',
  placeholder: '请选择',
  pageSize: 10,
  clearable: false,
  options: () => [],
})

const emit = defineEmits<{ (e: 'update:modelValue', value: string | number | null): void }>()

const popupRef = ref()

const display = computed(() => {
  const v = props.valueKey
  const value = props.modelValue
  const o = props.options
  const l = props.labelKey

  const item = o.find((e) => e[v] === value)
  return item ? item[l] : value
})

function clear() {
  if (!isNil(props.modelValue)) {
    emit('update:modelValue', null)
  }
}

function openPopup() {
  popupRef.value.open()
}

function onConfirm(e: any) {
  const v = props.modelValue
  const kv = props.valueKey
  if ((!e && !isNil(v)) || (e && e[kv] !== v)) {
    // null值
    emit('update:modelValue', e ? e[kv] : null)
  }
}
</script>

<style lang="scss" scoped>
.sy-select {
  flex: 1;
  border: 1rpx solid rgba(121, 121, 121, 0.1);
  border-radius: 14rpx;
  padding: var(--sy-select-y-padding, 10px) var(--sy-select-x-padding, 15px);

  &__placeholder {
    flex: 1;
    margin-right: 10px;
    color: var(--sy-select-placeholder-color, #bfbfbf);
    font-size: var(--sy-select-placeholder-size, 14px);
  }

  &__content {
    flex: 1;
    margin-right: 10px;
    font-size: 14px;
    color: var(--sy-select-value-color, rgba(0, 0, 0, 0.85));
  }
}
</style>
