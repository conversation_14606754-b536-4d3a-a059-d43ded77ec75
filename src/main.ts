import { createSSRApp } from 'vue'
import App from './App.vue'
import store from './store'
import { routeInterceptor, requestInterceptor, prototypeInterceptor } from './interceptors'
import { setupInputDirective } from './utils/inputDecimal'
import 'virtual:uno.css'
import '@/style/index.scss'
import './permission'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(prototypeInterceptor)
  setupInputDirective(app)

  app.config.globalProperties.$onLaunched = new Promise((resolve) => {
    app.config.globalProperties.$isResolve = resolve
  })

  return {
    app,
  }
}
