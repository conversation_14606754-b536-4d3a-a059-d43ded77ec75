<route lang="json5">
{
  style: {
    navigationBarTitleText: '对话',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="chat-list-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <uni-icons type="search" size="18" color="#999"></uni-icons>
        <input class="search-input" placeholder="搜索" @input="handleSearchChatList" />
      </view>
      <view class="add-btn">
        <uni-icons type="plus" size="26" color="#D1302E"></uni-icons>
      </view>
    </view>

    <!-- 对话列表 -->
    <uni-list :border="false">
      <wd-swipe-action v-for="item in chatList" :key="item.authorId">
        <uni-list-chat :title="item.nickname" :note="item.lastMessage" :time="item.lastTime" :avatar="item.avatar" :avatarCircle="true"
          :badgeText="item.unreadCount > 0 ? item.unreadCount : ''" :clickable="true" @click="handleChatClick(item)" :class="{ 'pinned': item.pinned }">
          <view class="position-absolute right-4 text-[24rpx] text-[#999]">
            <text>{{ item.lastTime }}</text>
          </view>
        </uni-list-chat>
        <template #right>
          <view class="swipe-action">
            <view
              class="button"
              style="background: #FFB300;"
              @click.stop="setPinStatus(item, !item.pinned)"
            >
              {{ item.pinned ? '取消置顶' : '置顶' }}
            </view>
            <!-- <view class="button" style="background: #FF3B30;" @click.stop="handlePin(item)">清空聊天</view> -->
          </view>
        </template>
      </wd-swipe-action>
    </uni-list>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { aiChatList } from "@/api/aiChat";
import { filter } from 'lodash-es';
import { useQueue } from 'wot-design-uni';

const chatList = ref([])
// 保存过滤前的全部聊天列表
const originalChatList = ref([])

// 处理点击聊天项
const handleChatClick = (item) => {
  uni.navigateTo({
    url: `/pages/ai/detail?authorId=${item.authorId}`
  })
}

onMounted(() => {
  aiChatList().then(res => {
    if (res && Array.isArray(res)) {
      chatList.value = res
      decorateAndSortChatList()
    }
  })
})

// 定时器，用于刷新 lastTime
let refreshTimer: ReturnType<typeof setInterval> | null = null

onShow(() => {
  decorateAndSortChatList()
  // 定时刷新 lastTime，每分钟刷新一次
  if (refreshTimer) clearInterval(refreshTimer)
  refreshTimer = setInterval(() => {
    decorateAndSortChatList()
  }, 60 * 1000)
})

onHide(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})

// 处理搜索聊天列表
const handleSearchChatList = (event) => {
  const searchText = event.detail.value.trim().toLowerCase()

  // 如果搜索文本为空，重新加载全部聊天列表
  if (!searchText) {
    chatList.value = [...originalChatList.value]
    return
  }

  // 使用lodash的filter函数进行模糊搜索
  chatList.value = filter(chatList.value, (item) => {
    return item.nickname.toLowerCase().includes(searchText)
  })
}

// 整理并排序对话列表
function decorateAndSortChatList() {
  chatList.value = chatList.value.map((item) => {
    const chatHistory = uni.getStorageSync(`chatHistory-${item.authorId}`)

    // 检查是否存在聊天记录，增加代码健壮性
    if (Array.isArray(chatHistory) && chatHistory.length > 0) {
      const lastHistoryChatData = chatHistory[chatHistory.length - 1]

      let lastMessage;
      if (lastHistoryChatData.isChart) {
        lastMessage = '[图表消息]';
      } else {
        lastMessage = lastHistoryChatData.text ?? lastHistoryChatData.html ?? ''
      }

      const lastTime = formatLastTime(lastHistoryChatData.t)
      const lastChatData = {
        lastMessage: getTextContentPrefixRegex(lastMessage, 18),
        lastTime: lastTime,
        lastTimestamp: lastHistoryChatData.t || 0,
        unreadCount: 0, // TODO: 未读数逻辑可以后续完善
      }
      return { ...item, ...lastChatData }
    }

    // 没有聊天记录时提供默认值
    return { ...item, lastMessage: '暂无消息', lastTime: '', unreadCount: 0 }
  })

  // 按最后一次聊天时间降序排序
  // chatList.value.sort((a, b) => (b.lastTimestamp || 0) - (a.lastTimestamp || 0))
  // 标记置顶聊天
  let pinnedChatList = uni.getStorageSync('pinnedChatList')
  chatList.value.forEach(item => {
    item.pinned = pinnedChatList?.includes(item.authorId) || false
  })
  // 按置顶状态排序，置顶的在前 如果都是置顶 按聊天时间排序
  chatList.value.sort((a, b) => {
    if (a.pinned && !b.pinned) return -1; // a 置顶，b 不置顶，a 在前
    if (!a.pinned && b.pinned) return 1; // a 不置顶，b 置顶，b 在前
    return (b.lastTimestamp || 0) - (a.lastTimestamp || 0); // 都置顶或都不置顶，按聊天时间排序
  })


  // 保存原始聊天列表，便于后续搜索恢复
  originalChatList.value = [...chatList.value]
}

// 格式化最后一次聊天时间
function formatLastTime(timestamp) {
  const now = new Date();
  const lastTime = new Date(timestamp);
  const diff = now.getTime() - lastTime.getTime();

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  // 1分钟内显示"刚刚"
  if (minutes < 1) {
    return '刚刚';
  }

  // 1小时内显示"X分钟前"
  if (hours < 1) {
    return `${minutes}分钟前`;
  }

  // 判断是否为今天
  const nowDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const lastDate = new Date(lastTime.getFullYear(), lastTime.getMonth(), lastTime.getDate());
  const isToday = nowDate.getTime() === lastDate.getTime();

  if (isToday) {
    // 今天显示具体时间 HH:MM
    const hour = lastTime.getHours().toString().padStart(2, '0');
    const minute = lastTime.getMinutes().toString().padStart(2, '0');
    return `${hour}:${minute}`;
  }

  // 判断是否为昨天
  const yesterday = new Date(nowDate.getTime() - 24 * 60 * 60 * 1000);
  const isYesterday = yesterday.getTime() === lastDate.getTime();

  if (isYesterday) {
    // 昨天显示"昨天"
    return '昨天';
  }

  // 7天内显示星期几
  if (days <= 7) {
    const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return weekDays[lastTime.getDay()];
  }

  // 超过7天显示日期
  const month = (lastTime.getMonth() + 1).toString().padStart(2, '0');
  const date = lastTime.getDate().toString().padStart(2, '0');

  // 如果是今年，只显示月日
  if (lastTime.getFullYear() === now.getFullYear()) {
    return `${month}/${date}`;
  }

  // 不是今年，显示年月日
  return `${lastTime.getFullYear()}/${month}/${date}`;
}

// 获取HTML内容的前缀文本
// 该函数将HTML字符串转换为纯文本，并截取指定长度的前缀文本，超过长度则添加省略号。
function getTextContentPrefixRegex(htmlStr, length) {
  // 移除所有HTML标签
  // /<[^>]*>/g 正则表达式匹配任何以 < 开头，以 > 结尾的字符串，中间可以有任意非 > 字符。
  // g 标志表示全局匹配
  let plainText = htmlStr.replace(/<[^>]*>/g, '');

  // 移除多余的空白字符（包括换行符和连续空格）
  plainText = plainText.replace(/\s+/g, ' ').trim();

  // 如果文本长度超过指定长度，截取并添加省略号；否则返回全部内容
  if (plainText.length > length) {
    return plainText.substring(0, length) + '...';
  }

  return plainText;
}

const { closeOutside } = useQueue()

// 设置聊天置顶状态
function setPinStatus(item, pinned) {
  const idx = chatList.value.findIndex(i => i.authorId === item.authorId)
  if (idx !== -1) {
    chatList.value[idx].pinned = pinned
  }
  uni.setStorageSync('pinnedChatList', chatList.value.filter(i => i.pinned).map(i => i.authorId))
  decorateAndSortChatList()
  closeOutside()
}
</script>

<style lang="scss" scoped>
.chat-list-container {
  height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  gap: 12px;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 20px;
  padding: 8px 12px;
  gap: 8px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #333;

  &::placeholder {
    color: #999;
  }
}

.add-btn {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

// 覆盖uni-list-chat的默认样式
:deep(.uni-list-chat) {
  background-color: #ffffff;

  &:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
  }

  .uni-list-chat__container {
    padding: 12px 16px;
  }

  .uni-list-chat__header {
    width: 50px;
    height: 50px;

    .uni-list-chat__header-image {
      width: 50px;
      height: 50px;
      border-radius: 25px;
    }
  }

  .uni-list-chat__content {
    margin-left: 12px;
    flex: 1;

    .uni-list-chat__content-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .uni-list-chat__content-note {
      font-size: 14px;
      color: #999;
      line-height: 1.4;
    }
  }

  .uni-list-chat__meta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;

    .uni-list-chat__meta-time {
      font-size: 12px;
      color: #999;
    }
  }
}

// 未读消息角标样式
:deep(.uni-badge) {
  .uni-badge--dot {
    background-color: #ff3b30;
  }

  .uni-badge--text {
    background-color: #ff3b30;
    color: #ffffff;
    font-size: 12px;
    min-width: 18px;
    height: 18px;
    line-height: 18px;
    border-radius: 9px;
  }
}

.swipe-action {
  height: 100%;
  display: flex;

  .button {
    height: 100%;
    width: 120rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 500;
    padding: 0 32rpx;
    cursor: pointer;
  }
}

.pinned {
  background-color: whitesmoke;
  /* 置顶聊天项背景色 */
  // border-left: 4px solid #007AFF; /* 置顶标识 */
}
</style>
