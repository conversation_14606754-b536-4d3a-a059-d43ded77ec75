<route lang="json5">
{
  style: {
    navigationBarTitleText: '追加内容',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <wd-form ref="formRef" :model="formData">
    <wd-cell prop="title" title-width="0">
      <view class="title-container">
        <text>方案：{{ formData.title }}</text>
      </view>
    </wd-cell>
    <wd-cell prop="contents" title-width="0">
      <view class="title-container">
        <text class="title">{{ formData.type === 0 ? '追加免费内容' : '追加付费内容' }}</text>
      </view>
      <piaoyi-editor
        class="w-700rpx"
        ref="editorRef"
        :value="formData.contents"
        @changes="changeContent"
        :photoUrl="photoUrl"
        :maxlength="10000"
      />
    </wd-cell>
    <view class="flex justify-center items-center bg-white w-full fixed bottom-0 py-[30rpx]">
      <button class="w-350rpx rounded-10rpx color-[rgb(152,152,152)]" @click="resetFormData">
        清空内容
      </button>
      <button class="w-350rpx rounded-10rpx" type="warn" @click="submitForm">发布</button>
    </view>
  </wd-form>
  <back />
</template>
<script lang="ts" setup>
import { appendArticle } from '@/api/author'
import { useMessage } from 'wot-design-uni'
import back from '@/components/back/index.vue'
const message = useMessage()
const formRef = ref()
const editorRef = ref()
const photoUrl = import.meta.env.VITE_UPLOAD_BASEURL
const formData = ref<any>({
  articleId: null,
  title: '', // 文章标题，不能为空
  contents: '', // 付费内容
  type: 1, // 0：免费 1：付费
})

const resetFormData = () => {
  message
    .confirm({
      title: '确定要清空内容吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(() => {
      reset()
    })
    .catch(() => {
      console.log('cancel')
    })
}

const reset = () => {
  editorRef.value.clear()
}
const changeContent = (value) => {
  formData.value.contents = value.html
}
const submitForm = () => {
  formRef.value.validate().then(async ({ valid, errors }) => {
    console.log('errors', errors)
    if (valid) {
      if (!formData.value.contents) {
        message.show({
          title: '追加内容不能同时为空',
          showErr: true,
        })
        return
      }

      message
        .confirm({
          msg: '确定要发布追加内容吗？',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
        })
        .then(() => {
          submit()
        })
        .catch(() => {
          console.log('cancel')
        })
    } else {
      console.log('fail')
    }
  })
}

const submit = async () => {
  const id = await appendArticle(formData.value)
  formData.value.id = id
  uni.showToast({
    title: '发布成功',
  })
  uni.navigateBack()
  return
}

onLoad((e) => {
  if (e.id) {
    formData.value.articleId = e.id
  }
  if (e.type) {
    formData.value.type = Number(e.type)
  }
  if (e.title) {
    formData.value.title = e.title
  }
})
</script>
<style lang="scss" scoped>
:deep(.wd-cell__left) {
  display: none;
}

:deep(.wd-input) {
  box-sizing: border-box;
  padding: 5rpx;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;
}

:deep(.wd-picker) {
  box-sizing: border-box;
  padding: 5rpx;
  text-align: left;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;
}

:deep(.wd-picker__cell) {
  background-color: none;
}

$raido-checked-color: rgb(209, 48, 46);

:deep(.is-checked) {
  .wd-radio__label {
    color: $raido-checked-color;
  }

  .wd-radio__shape {
    background-color: white !important;
    border-color: $raido-checked-color !important;

    &::before {
      background-color: $raido-checked-color !important;
    }
  }
}

$radio-disabled-color: rgb(217, 217, 217);
$radio-disabled-bg-color: rgb(243, 243, 243);

:deep(.is-checked.is-disabled) {
  .wd-radio__label {
    color: $radio-disabled-color;
  }

  .wd-radio__shape {
    background-color: $radio-disabled-bg-color !important;
    border-color: $radio-disabled-color !important;

    &::before {
      background-color: $radio-disabled-color !important;
    }
  }
}

.date-picker {
  padding: 20rpx;
  color: rgb(140, 140, 140);
  text-align: left;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;
}

.title-container {
  margin: 10rpx 0;
  font-weight: 600;
  text-align: left;

  .title {
    &::after {
      color: red;
      content: '*';
    }
  }
}

:deep(.ql-container .ql-editor) {
  padding-bottom: 80rpx;
  background-color: rgb(250, 250, 250);
}
</style>
