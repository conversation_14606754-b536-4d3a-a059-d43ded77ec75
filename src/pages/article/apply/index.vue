<route lang="json5">
{
  style: {
    navigationBarTitleText: '申请成为作者',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen">
    <wd-form
      class="flex justify-between flex-col"
      style="min-height: 100vh"
      :rules="rules"
      ref="formRef"
      :model="formData"
      label-width="200rpx"
    >
      <wd-cell-group border>
        <wd-input
          prop="name"
          label="姓名"
          :disabled="edit"
          v-model="formData.name"
          placeholder="请输入姓名"
        />
        <wd-input
          prop="idCard"
          :disabled="edit"
          label="身份证"
          v-model="formData.idCard"
          placeholder="请输入身份证号"
        />
        <wd-cell title-width="200rpx" title="战绩截图" prop="imgUrl">
          <wd-upload
            ref="uploadRef"
            :disabled="edit"
            :file-list="formData.imgUrl"
            :header="uploadHearder"
            :limit="1"
            image-mode="aspectFill"
            :action="action"
            @change="changeFileList"
            @success="successUpload"
          ></wd-upload>
        </wd-cell>
        <!-- <wd-cell
          title="审核被拒原因"
          title-width="200rpx"
          v-if="formData.status === -1"
          prop="failReason"
        >
          <view class="text-left text-[#d1302e]">
            <text>{{ formData.failReason }}</text>
          </view>
        </wd-cell> -->
      </wd-cell-group>
      <view class="flex justify-center items-center mb-100rpx text-22rpx">
        <wd-checkbox v-model="confirm" shape="square">我已阅读并同意</wd-checkbox>
        <text class="text-blue" @click="showAuthorProtocal">《作者入驻平台协议》</text>
      </view>
      <wd-button size="large" custom-class="save" v-if="!edit" :disabled="edit" @click="submitForm">
        提交申请
      </wd-button>
    </wd-form>
  </view>
  <back />
</template>
<script lang="ts" setup>
import { submitAuthorAudit, getAuthorAuditList } from '@/api/authorAudits'
import { useUserStore } from '@/store/user'
import back from '@/components/back/index.vue'
import { ID_CARD_REG } from '@/utils/regex'

const formData = ref({
  imgUrl: [],
  idCard: null,
  name: null,
  failReason: '',
  status: -2,
})

const edit = computed(() => {
  if (formData.value.status >= 0) {
    return true
  } else {
    return false
  }
})

const confirm = ref(false)

function showAuthorProtocal() {
  uni.navigateTo({ url: '/pages/article/apply/authProtocal' })
}

const formRef = ref()

const userInfo = useUserStore().userInfo

const uploadRef = ref()
const uploadHearder = computed(() => {
  return {
    Authorization: `Bearer ${userInfo.token}`,
  }
})

const action = computed(() => {
  return import.meta.env.VITE_UPLOAD_BASEURL
})

const changeFileList = ({ fileList: files }) => {
  formData.value.imgUrl = files
}

const rules = reactive({
  imgUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
  idCard: [{ required: true, pattern: ID_CARD_REG, message: '请输入正确的身份证号' }],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { required: false, pattern: /^(?:[\u4e00-\u9fa5·]{2,16})$/, message: '只支持中文名字' },
  ],
})

const init = async () => {
  const res: any = await getAuthorAuditList()
  if (res) {
    formData.value = {
      name: res.name,
      failReason: res.failReason,
      idCard: res.idCard,
      imgUrl: res.imgUrl ? [{ url: res.imgUrl }] : [],
      status: res.status,
    }
  }
}

const submitForm = () => {
  formRef.value.validate().then(async ({ valid, errors }) => {
    if (!confirm.value) {
      uni.showModal({
        title: '作者入驻平台协议',
        content: '请阅读并同意作者入驻平台协议',
        confirmText: '同意',
        cancelText: '不同意',
        cancelColor: 'rgb(150,150,150)',
        success: (res) => {
          if (res.confirm) {
            confirm.value = true
          }
        },
      })
      return
    }

    if (valid) {
      if (formData.value.imgUrl.length === 0) {
        uni.showToast({
          title: '请上传图片',
          icon: 'none',
          duration: 2000,
        })
        return
      }
      // 提交表单
      // 上传图片
      console.log('校验成功')

      const param = {
        name: formData.value.name,
        idCard: formData.value.idCard,
        imgUrl: formData.value.imgUrl[0].url,
      }
      const res = await submitAuthorAudit(param)
      uni.showToast({
        title: '提交成功，等待审核',
        icon: 'none',
        duration: 2000,
      })
      // 刷新列表
      init()
    } else {
      console.log('校验失败')
      console.log(errors)
    }
  })
}

const successUpload = ({ file, fileList }) => {
  const response = file.response
  const result = JSON.parse(response)
  formData.value.imgUrl = [{ url: result.data }]
  console.log('上传成功', formData.value)
}

onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.save {
  bottom: 30rpx;
  width: 90%;
  background: #d1302e !important;
}
</style>
