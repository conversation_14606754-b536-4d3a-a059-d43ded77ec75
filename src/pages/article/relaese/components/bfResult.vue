<template>
  <view class="flex-1 pr-40rpx text-end pb-20rpx">
    <view v-if="isEmpty(modelValue)">
      <view @click="openMask">
        <text class="mr-10rpx text-28rpx text-#bfbfbf text-opacity-80">请选择</text>
        <wd-icon name="arrow-right" size="28rpx" color="rgba(191, 191, 191,0.8)" />
      </view>
    </view>
    <view v-else class="flex items-center gap-x-10rpx">
      <view class="flex justify-end items-center gap-10rpx flex-wrap">
        <view
          v-for="e in modelValue"
          :key="e"
          class="w-160rpx h-70rpx text-center leading-70rpx border-solid border-1rpx border-black border-opacity-10"
        >
          {{ e }}
        </view>
      </view>
      <wd-icon name="arrow-right" size="28rpx" color="rgba(191, 191, 191,0.8)" @click="openMask" />
    </view>
    <!-- 遮罩 -->
    <view class="fixed inset-0 z-20 bg-black bg-opacity-60" v-if="isShow" @click="closeMask"></view>
    <!-- 选择框 -->
    <view
      class="fixed justify-between items-center inset-x-0 bottom-0 max-h-80% overflow-y-auto pb-100rpx rounded-2xl bg-white z-30"
      v-if="isShow"
      @click.stop
    >
      <!-- 弹框头(主客队名称) -->
      <view
        class="relative flex flex-col justify-center items-center h-144rpx pt-40rpx px-30rpx text-28rpx"
      >
        <view class="flex justify-center items-baseline leading-50rpx">
          <text class="text-36rpx text-black text-opacity-90">{{ match.homeName }}</text>
          <text class="text-30rpx text-black">(主)</text>
          <text class="text-36rpx text-black text-opacity-50 px-10rpx">VS</text>
          <text class="text-36rpx text-black text-opacity-90">{{ match.awayName }}</text>
          <text class="text-30rpx text-black">(客)</text>
        </view>
      </view>
      <!-- 主胜，平分，客胜 -->
      <view class="flex flex-col gap-y-20rpx mb-40rpx">
        <!-- 主胜 -->
        <view class="flex px-30rpx">
          <!-- 左边标题 -->
          <view
            class="flex items-center text-center w-40rpx bg-#747ECE bg-opacity-70 text-white text-24rpx"
            style="text-orientation: upright; width: 40rpx"
          >
            主胜比分
          </view>
          <!-- 内容 -->
          <view class="flex flex-wrap flex-1" v-if="!isEmpty(bonusList.win)">
            <template v-for="(e, index) in bonusList.win" :key="e">
              <view
                @click="checkItem(e)"
                class="flex flex-col justify-center items-center h-100rpx border-solid border-1rpx box-border border-opacity-10 box-border"
                :class="dClazz(e)"
                :style="
                  index === bonusList.win.length - 1
                    ? 'width: calc(3* 100% /5)'
                    : 'width: calc(100% /5)'
                "
              >
                <text class="text-28rpx text-opacity-80">{{ e }}</text>
                <text class="text-24rpx text-opacity-50" v-if="b && b.win && b.win[index]">
                  {{ b.win[index] }}
                </text>
              </view>
            </template>
          </view>
        </view>
        <!-- 平分 -->
        <view class="flex px-30rpx">
          <!-- 左边标题 -->
          <view
            class="flex items-center text-center w-40rpx bg-#40A875 bg-opacity-70 text-white text-24rpx"
            style="text-orientation: upright; width: 40rpx"
          >
            平比分
          </view>
          <!-- 内容 -->
          <view class="flex flex-wrap flex-1" v-if="!isEmpty(bonusList.draw)">
            <template v-for="(e, index) in bonusList.draw" :key="e">
              <view
                @click="checkItem(e)"
                class="flex flex-col justify-center items-center h-100rpx border-solid border-1rpx border-black border-opacity-10 box-border"
                :class="dClazz(e)"
                style="width: calc(100% / 5)"
              >
                <text class="text-28rpx text-opacity-80">{{ e }}</text>
                <text class="text-24rpx text-opacity-50" v-if="b && b.draw && b.draw[index]">
                  {{ b.draw[index] }}
                </text>
              </view>
            </template>
          </view>
        </view>
        <!-- 客胜 -->
        <view class="flex px-30rpx">
          <!-- 左边标题 -->
          <view
            class="flex items-center text-center w-40rpx bg-#747ECE bg-opacity-70 text-white text-24rpx"
            style="text-orientation: upright; width: 40rpx"
          >
            客胜比分
          </view>
          <!-- 内容 -->
          <view class="flex flex-wrap flex-1" v-if="!isEmpty(bonusList.lose)">
            <template v-for="(e, index) in bonusList.lose" :key="e">
              <view
                @click="checkItem(e)"
                class="flex flex-col justify-center items-center h-100rpx border-solid border-1rpx border-black border-opacity-10 box-border"
                :class="dClazz(e)"
                :style="
                  index === bonusList.lose.length - 1
                    ? 'width: calc(3* 100% / 5)'
                    : 'width: calc(100%/5)'
                "
              >
                <text class="text-28rpx text-opacity-80">{{ e }}</text>
                <text class="text-24rpx text-opacity-50" v-if="b && b.lose && b.lose[index]">
                  {{ b.lose[index] }}
                </text>
              </view>
            </template>
          </view>
        </view>
      </view>
      <!-- 底部按钮 -->
      <view class="flex gap-x-20rpx h-80rpx px-30rpx text-32rpx">
        <text
          class="flex-1 text-center leading-80rpx rounded-lg bg-black bg-opacity-10 text-black text-opacity-50"
          @click="closeMask"
        >
          取消
        </text>
        <text
          class="flex-1 text-center leading-80rpx rounded-lg bg-#D1302E text-white"
          @click="confirm"
        >
          确认
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { isEmpty } from 'lodash-es'
import { BF_SCORES } from '@/utils/constant'

const props = defineProps<{
  match: { id: number; homeName: string; awayName: string }
  bonus: string | null
  limit: number
  modelValue: string[]
}>()
const emits = defineEmits(['update:modelValue', 'onCheck'])

const bonusList = ref<{ win: string[]; draw: string[]; lose: string[] }>({
  win: [],
  draw: [],
  lose: [],
})

const selected = ref<string[]>([])

const isShow = ref(false)

const dClazz = computed(() => {
  return (m: string) => {
    const s = selected.value
    if (s.includes(m)) return 'bg-#D1302E text-white'
    if (s.length >= props.limit) return 'bg-black bg-opacity-10 text-black text-opacity-20'
    return 'border-black border-opacity-10'
  }
})

const b = computed(() => {
  if (props.bonus) {
    const b = props.bonus.split(',')
    return {
      win: b.slice(0, 13),
      draw: b.slice(13, 18),
      lose: b.slice(18),
    }
  }

  return null
})

function checkItem(m: string) {
  const s = selected.value
  if (s.includes(m)) {
    // 删除操作
    selected.value = selected.value.filter((e) => e !== m)
  } else if (s.length >= props.limit) {
    // 添加的条目操作限制
    return
  } else {
    // 添加条目
    selected.value = [...selected.value, m]
  }
}

function confirm() {
  emits('update:modelValue', selected.value)
  emits('onCheck', selected.value)
  closeMask()
}

function openMask() {
  isShow.value = true
  selected.value = isEmpty(props.modelValue) ? [] : props.modelValue
}

function closeMask() {
  isShow.value = false
}

onMounted(() => {
  bonusList.value = {
    win: BF_SCORES.slice(0, 13),
    draw: BF_SCORES.slice(13, 18),
    lose: BF_SCORES.slice(18),
  }
})
</script>
