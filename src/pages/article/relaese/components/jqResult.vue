<template>
  <view class="flex-1 pr-40rpx pb-20rpx text-end">
    <view v-if="isEmpty(modelValue)">
      <view @click="openMask">
        <text class="mr-10rpx text-28rpx text-#bfbfbf text-opacity-80">请选择</text>
        <wd-icon name="arrow-right" size="28rpx" color="rgba(191, 191, 191,0.8)" />
      </view>
    </view>
    <view v-else class="flex items-center gap-x-10rpx">
      <view class="flex justify-end items-center gap-10rpx flex-wrap">
        <view
          v-for="e in modelValue"
          :key="e"
          class="w-160rpx h-70rpx text-center leading-70rpx border-solid border-1rpx border-black border-opacity-10"
        >
          {{ e }}
        </view>
      </view>
      <wd-icon name="arrow-right" size="28rpx" color="rgba(191, 191, 191,0.8)" @click="openMask" />
    </view>
    <!-- 遮罩 -->
    <view class="fixed inset-0 z-20 bg-black bg-opacity-60" v-if="isShow" @click="closeMask"></view>
    <!-- 选择框 -->
    <view
      class="fixed justify-between items-center inset-x-0 bottom-0 max-h-80% overflow-y-auto pb-100rpx rounded-2xl bg-white z-30"
      v-if="isShow"
      @click.stop
    >
      <!-- 主客队名称 -->
      <view
        class="relative flex flex-col justify-center items-center h-144rpx pt-40rpx px-30rpx text-28rpx"
      >
        <view class="flex justify-center items-baseline leading-50rpx">
          <text class="text-36rpx text-black text-opacity-90">{{ match.homeName }}</text>
          <text class="text-30rpx text-black">(主)</text>
          <text class="text-36rpx text-black text-opacity-50 px-10rpx">VS</text>
          <text class="text-36rpx text-black text-opacity-90">{{ match.awayName }}</text>
          <text class="text-30rpx text-black">(客)</text>
        </view>
      </view>
      <!-- 选择框 -->
      <view class="flex flex-wrap mt-20rpx mb-30rpx px-30rpx">
        <view
          v-for="(e, index) in JQ_SCORE"
          :key="e"
          @click="checkItem(e)"
          class="flex justify-center items-center h-100rpx px-30rpx border-solid border-1rpx border-opacity-10 box-border"
          :class="dClazz(e)"
          style="width: calc(100% / 4)"
        >
          <text class="text-28rpx text-blackk text-opacity-80">{{ e }}</text>
          <text class="ml-auto text-24rpx text-black text-opacity-50" v-if="b && b[index]">
            {{ b[index] }}
          </text>
        </view>
      </view>
      <!-- 底部按钮 -->
      <view class="flex gap-x-20rpx h-80rpx px-30rpx text-32rpx">
        <text
          class="flex-1 text-center leading-80rpx rounded-lg bg-black bg-opacity-10 text-black text-opacity-50"
          @click="closeMask"
        >
          取消
        </text>
        <text
          class="flex-1 text-center leading-80rpx rounded-lg bg-#D1302E text-white"
          @click="confirm"
        >
          确认
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { JQ_SCORE } from '@/utils/constant'
import { isEmpty } from 'lodash-es'

const props = defineProps<{
  match: { id: number; homeName: string; awayName: string }
  bonus: string | null
  limit: number
  modelValue: string[]
}>()
const emits = defineEmits(['update:modelValue', 'onCheck'])

const selected = ref<string[]>([])

const isShow = ref(false)

const dClazz = computed(() => {
  return (m: string) => {
    const s = selected.value

    if (s.includes(m)) return 'bg-#D1302E text-white'
    if (s.length >= props.limit) return 'bg-black bg-opacity-10 text-black text-opacity-20'
    return 'border-black border-opacity-10'
  }
})

function checkItem(m: string) {
  const s = selected.value
  if (s.includes(m)) {
    // 删除操作
    selected.value = selected.value.filter((e) => e !== m)
  } else if (s.length >= props.limit) {
    // 添加的条目操作限制
    return
  } else {
    // 添加条目
    selected.value = [...selected.value, m]
  }
}

const b = computed(() => (props.bonus ? props.bonus.split(',') : null))

function openMask() {
  console.log(props.bonus)
  isShow.value = true
  selected.value = isEmpty(props.modelValue) ? [] : props.modelValue
}

function closeMask() {
  isShow.value = false
}

function confirm() {
  emits('update:modelValue', selected.value)
  emits('onCheck', selected.value)
  closeMask()
}
</script>
