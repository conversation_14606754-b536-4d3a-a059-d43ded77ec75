<template>
  <view>
    <view class="flex justify-between items-center py-40rpx pr-40rpx">
      <text class="text-28rpx text-black text-opacity-90">{{ label }}</text>
      <view class="flex gap-x-20rpx" @click="openMask">
        <template v-if="isEmpty(modelValue)">
          <view>
            <text class="mr-10rpx text-28rpx text-#bfbfbf text-opacity-80">请选择</text>
            <wd-icon name="arrow-right" size="28rpx" color="rgba(191, 191, 191,0.8)" />
          </view>
        </template>
        <template v-else>
          <view>
            <!-- <view :key="id" class="flex flex-col text-28rpx text-black text-opacity-50 leading-40rpx"
              v-for="{ id } in modelValue">
              <text>{{ `${match(id).roundName} ${match(id).issueNum || ''}` }}</text>
              <text>{{ `${match(id).homeName} VS ${match(id).awayName}` }}</text>
            </view> -->
            <view
              :key="id"
              class="flex flex-col text-28rpx text-black text-opacity-50 leading-40rpx"
              v-for="{ id, roundName, homeName, awayName, issueNum } in modelValue"
            >
              <text>{{ `${roundName} ${issueNum || ''}` }}</text>
              <text>{{ `${homeName} VS ${awayName}` }}</text>
            </view>
          </view>
          <view class="flex items-center">
            <wd-icon name="arrow-right" size="28rpx" />
          </view>
        </template>
      </view>
    </view>
    <!-- 遮罩 -->
    <view class="fixed inset-0 z-20 bg-black bg-opacity-60" v-if="isShow" @click="closeMask"></view>
    <!-- 选择框 -->
    <view
      class="fixed justify-between items-center inset-x-0 bottom-0 max-h-60% overflow-y-auto rounded-tl-2xl rounded-tr-2xl bg-white z-30"
      v-if="isShow"
      @click.stop
    >
      <view class="relative flex justify-center items-center h-144rpx text-28rpx">
        请选择
        <view class="absolute right-30rpx top-44rpx" @click="closeMask">
          <wd-icon name="close" size="28rpx" />
        </view>
      </view>
      <view class="px-30rpx">
        <!-- <wd-input @input="handleInput" placeholder="请输入主队或客队名称" /> -->
        <wd-input @input="handleInput" placeholder="请输入竞足编号或球队名称" />
      </view>
      <template v-if="!isEmpty(displayedMatchs)">
        <!-- 内容选项 -->
        <view
          v-for="e in displayedMatchs"
          :key="e.id"
          class="flex items-center gap-x-30rpx p-30rpx"
          @click="handleMatchCheck(e.id)"
        >
          <wd-icon
            name="check-circle-filled"
            size="32rpx"
            v-if="selected.some((m) => m.id === e.id)"
            color="#D1302E"
          />
          <wd-icon name="circle" size="28rpx" v-else />
          <view class="flex flex-col text-28rpx text-black text-opacity-50 leading-40rpx">
            <!-- <text>{{ `${e.issueNum ? e.issueNum.slice(1) : ''} ${e.roundName}` }}</text> -->
            <text>{{ matchCaption(e) }}</text>
            <text>{{ matchTxt(e) }}</text>
          </view>
        </view>
        <!-- 分页 -->
        <wd-pagination
          v-model="pageNo"
          :total="totalItem"
          :page-size="DEFAULT_PAGE_SIZE"
          @change="handlePageChange"
          show-icon
          hide-if-one-page
        />
      </template>
      <view
        class="sticky bottom-0 py-30rpx px-100rpx bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)]"
      >
        <wd-button block type="primary" @click="confirm">确定</wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { IMatchInfoItem } from '@/api/match'
import { DEFAULT_PAGE_SIZE, WEEK_CN } from '@/utils/constant'
import { debounce, isEmpty } from 'lodash-es'

const props = defineProps<{
  label: string
  modelValue: {
    id: number
    homeName: string
    awayName: string
    roundName: string
    issueNum: string | null
  }[]
  matchs: (IMatchInfoItem & { id: number })[]
  limit: number | null
}>()
const emits = defineEmits(['update:modelValue'])

const isShow = ref(false)

const selected = ref<
  { id: number; homeName: string; awayName: string; roundName: string; issueNum: string | null }[]
>([])

const pageNo = ref(1)

const match = computed(() => {
  return (mid: number) =>
    props.matchs.find(({ id }) => id === mid) ?? {
      roundName: '',
      homeName: '',
      awayName: '',
      issueNum: null,
    }
})

const matchTxt = computed(
  () => (item: Pick<IMatchInfoItem, 'homeName' | 'awayName'>) =>
    `${item.homeName}VS${item.awayName}`,
)

const filteMatchs = ref<
  { id: number; homeName: string; awayName: string; roundName: string; issueNum: string | null }[]
>([])

const totalItem = computed(() => (isEmpty(filteMatchs) ? 0 : filteMatchs.value.length))

const displayedMatchs = computed(() =>
  pageNo.value > 0
    ? filteMatchs.value.slice(
        (pageNo.value - 1) * DEFAULT_PAGE_SIZE,
        (pageNo.value - 1) * DEFAULT_PAGE_SIZE + DEFAULT_PAGE_SIZE,
      )
    : [],
)

function matchCaption(e) {
  const { issueNum, roundName } = e
  let txt = ''
  if (issueNum) {
    const wIndex = issueNum.slice(0, 1)
    const week = wIndex === '7' ? WEEK_CN[0] : WEEK_CN[+wIndex]
    txt += `${week} ${issueNum.slice(1)}`
  }
  txt += ` ${roundName}`
  return txt
}

const handleInput = debounce(({ value }) => {
  filteMatchs.value = props.matchs.filter(({ homeName, awayName, issueNum }) => {
    if (issueNum)
      return (
        homeName.includes(value) || awayName.includes(value) || issueNum.slice(1).includes(value)
      )
    return homeName.includes(value) || awayName.includes(value)
  })
  pageNo.value = 1
}, 1000)

function handleMatchCheck(mid: number) {
  if (selected.value.some(({ id }) => id === mid)) {
    // 删除比赛
    selected.value = selected.value.filter(({ id }) => id !== mid)
  } else if (props.limit && selected.value.length >= props.limit) {
    // limit为null则不限制
    // 选择的比赛数量已达上限
    uni.showToast({ title: `只能选择${props.limit}场比赛`, icon: 'none' })
  } else {
    // 添加比赛条目
    const match = props.matchs.find(({ id }) => id === mid)!
    selected.value = [...selected.value, match]
  }
}

function openMask() {
  isShow.value = true
  filteMatchs.value = props.matchs
  selected.value = isEmpty(props.modelValue) ? [] : props.modelValue
}
function closeMask() {
  isShow.value = false
}

function handlePageChange({ value }: { value: number }) {
  pageNo.value = value
}

function confirm() {
  emits('update:modelValue', selected.value)
  closeMask()
}
</script>
