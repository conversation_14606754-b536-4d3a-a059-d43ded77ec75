<template>
  <wd-popup v-model="visible" title="选择发布方式" position="center" :close-on-click-modal="false">
    <view class="relative flex flex-col items-center w-600rpx pt-50rpx px-30rpx pb-80rpx">
      <text class="text-30rpx text-black text-opacity-90 leading-40rpx">选择发布方式</text>
      <view
        @click="gotoCustomRelease"
        class="flex justify-between items-center w-600rpx h-100rpx pl-30rpx pr-20rpx mt-50rpx mb-20rpx rounded-12rpx border-normal bg-#FAFAFA box-border"
      >
        <text class="text-30rpx text-black text-opacity-90">自主发布</text>
        <wd-icon name="arrow-right" size="40rpx" color="#999999" />
      </view>
      <view
        @click="gotoReferenceRelease"
        class="flex justify-between items-center w-600rpx h-100rpx pl-30rpx pr-20rpx rounded-12rpx border-normal bg-#FAFAFA box-border"
      >
        <text class="text-30rpx text-black text-opacity-90">引用发布</text>
        <wd-icon name="arrow-right" size="40rpx" color="#999999" />
      </view>
    </view>
  </wd-popup>
</template>
<script setup lang="ts">
const visible = ref(false)

function open() {
  // visible.value = true
}

function close() {
  visible.value = false
}

function gotoCustomRelease() {
  close()
  uni.navigateTo({ url: '/pages/article/relaese/index' })
}

function gotoReferenceRelease() {
  close()
  uni.navigateTo({ url: '/pages/article/relaese/reference' })
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
:deep(.wd-popup) {
  border-radius: 12rpx;
}
</style>
