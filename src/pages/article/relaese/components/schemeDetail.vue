<template v-if="schemeDetail">
  <view class="pt-30rpx px-30rpx pb-40rpx bg-#F4F8FA min-h-screen">
    <!-- 玩法列表 -->
    <view class="pt-30rpx bg-white">
      <view
        v-for="{ playId, result } in detail.matchScheme[0].matchPlays"
        :key="playId"
        class="w-full"
      >
        <view class="flex flex-col items-center gap-y-20rpx w-full pt-30rpx pb-40rpx border-bottom">
          <text>{{ PLAY_TYPE_MAP[playId] }}</text>
          <!-- 让球胜平负 -->
          <view v-if="playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW">
            <view class="flex">
              <view
                class="flex flex-col justify-center items-center w-160rpx h-100rpx border-normal text-black"
                :class="result.includes('3') ? 'bg-#D1302E text-white' : ''"
              >
                <text class="text-28rpx leading-40rpx">
                  {{ `主队${detail.odds.rq ? detail.odds.rq.split(',')[0] : ''}` }}
                </text>
                <text class="text-24rpx leading-34rpx">
                  {{ `${detail.odds.rq ? detail.odds.rq.split(',')[1] : ''}` }}
                </text>
              </view>
              <view
                class="flex flex-col justify-center items-center w-160rpx h-100rpx text-black border-y-1rpx border-y-solid border-y-#797979 border-y-opacity-10"
                :class="result.includes('1') ? 'bg-#D1302E text-white' : ''"
              >
                <text class="text-28rpx text-opacity-90 leading-40rpx">平</text>
                <text class="text-24rpx text-opacity-50 leading-34rpx">
                  {{ `${detail.odds.rq ? detail.odds.rq.split(',')[2] : ''}` }}
                </text>
              </view>
              <view
                class="flex flex-col justify-center items-center w-160rpx h-100rpx text-black border-normal"
                :class="result.includes('0') ? 'bg-#D1302E text-white' : ''"
              >
                <text class="text-28rpx text-opacity-90 leading-40rpx">
                  {{ `客队${detail.odds.rq ? 0 - +detail.odds.rq.split(',')[0] : ''}` }}
                </text>
                <text class="text-24rpx text-opacity-50 leading-34rpx">
                  {{ `${detail.odds.rq ? detail.odds.rq.split(',')[3] : ''}` }}
                </text>
              </view>
            </view>
          </view>
          <!-- 胜平负 -->
          <view v-if="playId === PLAY_TYPE.WIN_LOSE_DRAW">
            <view class="flex">
              <view
                class="flex flex-col justify-center items-center w-160rpx h-100rpx border-normal text-black"
                :class="result.includes('3') ? 'bg-#D1302E text-white' : ''"
              >
                <text class="text-28rpx leading-40rpx">主胜</text>
                <text class="text-24rpx leading-34rpx">
                  {{ `${detail.odds.spf ? detail.odds.spf.split(',')[0] : ''}` }}
                </text>
              </view>
              <view
                class="flex flex-col justify-center items-center w-160rpx h-100rpx text-black border-y-1rpx border-y-solid border-y-#797979 border-y-opacity-10"
                :class="result.includes('1') ? 'bg-#D1302E text-white' : ''"
              >
                <text class="text-28rpx text-opacity-90 leading-40rpx">平</text>
                <text class="text-24rpx text-opacity-50 leading-34rpx">
                  {{ `${detail.odds.spf ? detail.odds.spf.split(',')[1] : ''}` }}
                </text>
              </view>
              <view
                class="flex flex-col justify-center items-center w-160rpx h-100rpx text-black border-normal"
                :class="result.includes('0') ? 'bg-#D1302E text-white' : ''"
              >
                <text class="text-28rpx text-opacity-90 leading-40rpx">客胜</text>
                <text class="text-24rpx text-opacity-50 leading-34rpx">
                  {{ `${detail.odds.spf ? detail.odds.spf.split(',')[2] : ''}` }}
                </text>
              </view>
            </view>
          </view>
          <!-- 比分 -->
          <view v-if="playId === PLAY_TYPE.SCORE" class="flex flex-col gap-y-20rpx">
            <view class="flex">
              <view
                class="center w-38rpx h-300rpx bg-#747ECE bg-opacity-70 text-24rpx text-white"
                style="writing-mode: vertical-rl"
              >
                主胜比分
              </view>
              <view class="grid grid-cols-5 w-600rpx">
                <template v-for="(e, index) in BF_SCORES.slice(0, 13)" :key="e">
                  <view
                    v-if="index === 12"
                    class="flex flex-col justify-center items-center col-span-3 border-normal"
                    :class="result.includes(e) ? 'bg-#D1302E' : ''"
                  >
                    <text
                      class="text-28rpx leading-40rpx text-black text-opacity-90"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ e }}
                    </text>
                    <text
                      v-if="detail.odds.bf"
                      class="text-24rpx leading-34rpx text-black text-opacity-50"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ detail.odds.bf.split(',')[index] }}
                    </text>
                  </view>
                  <view
                    v-else
                    class="flex flex-col justify-center items-center border-normal"
                    :class="result.includes(e) ? 'bg-#D1302E' : ''"
                  >
                    <text
                      class="text-28rpx leading-40rpx text-black text-opacity-90"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ e }}
                    </text>
                    <text
                      v-if="detail.odds.bf"
                      class="text-24rpx leading-34rpx text-black text-opacity-50"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ detail.odds.bf.split(',')[index] }}
                    </text>
                  </view>
                </template>
              </view>
            </view>
            <view class="flex">
              <view
                class="center w-38rpx h-100rpx bg-#40A875 bg-opacity-70 text-24rpx text-white"
                style="writing-mode: vertical-rl"
              >
                平比分
              </view>
              <view class="grid grid-cols-5 w-600rpx">
                <template v-for="(e, index) in BF_SCORES.slice(13, 18)" :key="e">
                  <view
                    class="flex flex-col justify-center items-center border-normal"
                    :class="result.includes(e) ? 'bg-#D1302E' : ''"
                  >
                    <text
                      class="text-28rpx leading-40rpx text-black text-opacity-90"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ e }}
                    </text>
                    <text
                      v-if="detail.odds.bf"
                      class="text-24rpx leading-34rpx text-black text-opacity-50"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ detail.odds.bf.split(',')[index + 13] }}
                    </text>
                  </view>
                </template>
              </view>
            </view>
            <view class="flex">
              <view
                class="center w-38rpx h-300rpx bg-#747ECE bg-opacity-70 text-24rpx text-white"
                style="writing-mode: vertical-rl"
              >
                客胜比分
              </view>
              <view class="grid grid-cols-5 w-600rpx">
                <template v-for="(e, index) in BF_SCORES.slice(18)" :key="e">
                  <view
                    v-if="index === 12"
                    class="flex flex-col justify-center items-center col-span-3 border-normal"
                    :class="result.includes(e) ? 'bg-#D1302E' : ''"
                  >
                    <text
                      class="text-28rpx leading-40rpx text-black text-opacity-90"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ e }}
                    </text>
                    <text
                      v-if="detail.odds.bf"
                      class="text-24rpx leading-34rpx text-black text-opacity-50"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ detail.odds.bf.split(',')[index + 18] }}
                    </text>
                  </view>
                  <view
                    v-else
                    class="flex flex-col justify-center items-center border-normal"
                    :class="result.includes(e) ? 'bg-#D1302E' : ''"
                  >
                    <text
                      class="text-28rpx leading-40rpx text-black text-opacity-90"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ e }}
                    </text>
                    <text
                      v-if="detail.odds.bf"
                      class="text-24rpx leading-34rpx text-black text-opacity-50"
                      :class="result.includes(e) ? 'text-white' : ''"
                    >
                      {{ detail.odds.bf.split(',')[index + 18] }}
                    </text>
                  </view>
                </template>
              </view>
            </view>
          </view>
          <!-- 半全场 -->
          <view v-if="playId === PLAY_TYPE.BQC" class="flex">
            <view
              class="center w-38rpx h-300rpx bg-#747ECE bg-opacity-70 text-24rpx text-white"
              style="writing-mode: vertical-rl"
            >
              半全场
            </view>
            <view class="grid grid-cols-3 w-600rpx">
              <view
                v-for="(e, index) in BQC_SCORE"
                class="flex flex-col justify-center items-center border-normal"
                :class="result.includes(e) ? 'bg-#D1302E' : ''"
                :key="e"
              >
                <text
                  class="text-28rpx text-black text-opacity-90 leading-40rpx"
                  :class="result.includes(e) ? 'text-white' : ''"
                >
                  {{ e }}
                </text>
                <text
                  v-if="detail.odds.bqc"
                  class="text-24rpx text-black text-opacity-50 leading-40rpx"
                  :class="result.includes(e) ? 'text-white' : ''"
                >
                  {{ detail.odds.bqc.split(',')[index] }}
                </text>
              </view>
            </view>
          </view>
          <!-- 总进球数 -->
          <view v-if="playId === PLAY_TYPE.JQ">
            <view class="grid grid-cols-4 w-640rpx h-140rpx">
              <view
                v-for="(e, index) in JQ_SCORE"
                :key="e"
                class="flex flex-col justify-center items-center border-normal"
                :class="result.includes(e) ? 'bg-#D1302E' : ''"
              >
                <text
                  class="text-28rpx text-black text-opacity-90 leading-40rpx"
                  :class="result.includes(e) ? 'text-white' : ''"
                >
                  {{ e }}
                </text>
                <text
                  v-if="detail.odds.jq"
                  class="text-24rpx text-black text-opacity-50 leading-34rpx"
                  :class="result.includes(e) ? 'text-white' : ''"
                >
                  {{ detail.odds.jq.split(',')[index] }}
                </text>
              </view>
            </view>
          </view>
          <!-- 让球 -->
          <view v-if="playId === PLAY_TYPE.RQ">
            <view class="flex justify-center items-center">
              <view
                class="flex flex-col justify-center items-center w-160rpx h-100rpx border-normal"
                :class="result.includes('3') ? 'bg-#D1302E' : ''"
              >
                <text
                  class="text-28rpx text-black text-opacity-90 leading-40rpx"
                  :class="result.includes('3') ? 'text-white' : ''"
                >
                  {{ `主队${detail.odds.rq ? detail.odds.rq.split(',')[0] : ''}` }}
                </text>
                <text
                  v-if="detail.odds.rq"
                  class="text-24rpx text-black text-opacity-50 leading-34rpx"
                  :class="result.includes('3') ? 'text-white' : ''"
                >
                  {{ detail.odds.rq.split(',')[1] }}
                </text>
              </view>
              <view
                class="flex flex-col justify-center items-center w-160rpx h-100rpx border-normal"
                :class="result.includes('0') ? 'bg-#D1302E' : ''"
              >
                <text
                  class="text-28rpx text-black text-opacity-90 leading-40rpx"
                  :class="result.includes('0') ? 'text-white' : ''"
                >
                  {{ `客队${detail.odds.rq ? 0 - +detail.odds.rq.split(',')[0] : ''}` }}
                </text>
                <text
                  v-if="detail.odds.rq"
                  class="text-24rpx text-black text-opacity-50 leading-34rpx"
                  :class="result.includes('0') ? 'text-white' : ''"
                >
                  {{ detail.odds.rq.split(',')[3] }}
                </text>
              </view>
            </view>
          </view>
          <!-- 大小球 -->
          <view v-if="playId === PLAY_TYPE.DXQ">
            <view class="flex text-28rpx text-black text-opacity-90 leading-40rpx">
              <view class="center w-200rpx h-70rpx border-normal">
                {{ `进球数${detail.odds.dxq ? detail.odds.dxq.split(',')[1] : ''}` }}
              </view>
              <view
                class="center w-200rpx h-70rpx border-normal"
                :class="result.includes('3') ? 'bg-#D1302E text-white' : ''"
              >
                {{ `大${detail.odds.dxq ? detail.odds.dxq.split(',')[0] : ''}` }}
              </view>
              <view
                class="center w-200rpx h-70rpx border-normal"
                :class="result.includes('0') ? 'bg-#D1302E text-white' : ''"
              >
                {{ `小${detail.odds.dxq ? detail.odds.dxq.split(',')[2] : ''}` }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!--  -->
    <wd-gap bg-color="#F4F8FA" height="30rpx" />
    <view class="flex flex-col mb-30rpx p-30rpx bg-white">
      <text class="mb-20rpx">付费内容</text>
      <view v-html="detail.contents" class="h-500rpx p-30rpx border-normal" />
    </view>
    <wd-button block size="large" :round="false" @click="confirmReference">确定引用</wd-button>
  </view>
</template>
<script setup lang="ts">
import { PLAY_TYPE } from '@/utils/enum'
import { PLAY_TYPE_MAP, BF_SCORES, BQC_SCORE, JQ_SCORE } from '@/utils/constant'
import { IBroomerItem, IOdds } from '@/api/match'
const props = defineProps<{ detail: IBroomerItem & { odds: IOdds } }>()
const emit = defineEmits<{ (e: 'reference', value: IBroomerItem & { odds: IOdds }): void }>()

function confirmReference() {
  emit('reference', props.detail)
}
</script>
