<template>
  <template v-if="detail">
    <scheme-detail :detail="detail" @reference="handleReference" />
  </template>
  <template v-else>
    <view v-if="isEmpty(list)" class="mt-200rpx">
      <wd-status-tip image="content" tip="暂无数据" />
    </view>
    <view v-else>
      <view
        v-for="e in list"
        :key="e.id"
        @click="showDetail(e)"
        class="flex items-center justify-between h-150rpx px-30rpx border-bottom last:border-none"
      >
        <view class="flex flex-col gap-y-10rpx">
          <text class="text-30rpx text-black text-opacity-90 leading-40rpx">
            {{ matchInfo(e.matchName, e.dataType) }}
          </text>
          <text class="text-26rpx text-black text-opacity-30 leading-36rpx">
            {{ formatDataTime(e.matchTime * 1000) }}
          </text>
        </view>
        <view class="flex gap-x-32rpx">
          <template v-if="selected.find((s) => s.id === e.id)">
            <wd-icon
              name="check-rectangle-filled"
              color="#D1302E"
              size="40rpx"
              @click.stop="handleMatchDeselected(e.id)"
            />
          </template>
          <!-- 1.同一场赛事不能重复选择 2.最多只能选择14场比赛 3 竞足和足球只能选择一种类型 -->
          <template
            v-else-if="
              selected.find((s) => s.matchId === e.matchId) ||
              selected.length >= 14 ||
              (dataType && dataType !== e.dataType)
            "
          >
            <wd-icon name="rectangle" size="40rpx" color="rgba(0,0,0,0.1)" />
          </template>
          <template v-else>
            <wd-icon
              name="rectangle"
              size="40rpx"
              color="rgba(0,0,0,0.3)"
              @click.stop="handleMatchSelected(e)"
            />
          </template>
          <wd-icon name="arrow-right" size="40rpx" color="#999" />
        </view>
      </view>
    </view>
    <view class="px-30rpx mt-60rpx">
      <wd-button
        :round="false"
        block
        size="large"
        :disabled="isEmpty(selected)"
        @click="showSheetAction"
      >
        确定引用
      </wd-button>
    </view>
  </template>
  <wd-action-sheet v-model="showAction" :close-on-click-modal="false">
    <view class="pt-20rpx pb-60rpx">
      <view class="relative mb-50rpx text-center text-30rpx text-black text-opacity-90">
        选择玩法
        <view class="absolute right-30rpx inset-y-0" @click="closeSheetAction">
          <wd-icon name="close" color="rgba(0,0,0,0.5)" />
        </view>
      </view>
      <!-- 赛事玩法选择列表 -->
      <scroll-view scroll-y :show-scrollbar="false">
        <view class="h-860rpx">
          <view
            v-for="s in selected"
            :key="s.id"
            class="flex flex-col gap-y-30rpx py-30rpx px-60rpx border-bottom last:border-none"
          >
            <view class="flex justify-between text-28rpx">
              <text class="text-black text-opacity-90">赛事名称></text>
              <text class="text-black text-opacity-50">{{ s.matchName }}</text>
            </view>
            <wd-select-picker
              title="请选择主玩法"
              label="主玩法>"
              custom-class="play-picker"
              align-right
              :columns="s.main"
              @confirm="({ value }) => handleMainPlayConfirm(value, s.id)"
              v-model="s.mmatchPlays"
              :before-confirm="beforeMainPlayConfirm"
            />
            <wd-select-picker
              title="请选择附赠玩法"
              label="附赠玩法>"
              custom-class="play-picker"
              align-right
              :columns="s.bonus"
              @confirm="({ value }) => handleBonusPlayConfirm(value, s.id)"
              v-model="s.bmatchPlays"
            />
          </view>
        </view>
      </scroll-view>
      <view class="px-30rpx">
        <wd-button block size="large" :round="false" @click="confirm">确定</wd-button>
      </view>
    </view>
  </wd-action-sheet>
</template>
<script setup lang="ts">
import { IBroomerItem, IOdds, ISelectedMatchScheme, getMatchPlayOdds } from '@/api/match'
import { isEmpty } from 'lodash-es'
import { formatDataTime } from '@/utils/format'
import { PLAY_TYPE_MAP } from '@/utils/constant'
import schemeDetail from './schemeDetail.vue'
import { useSchemeStore } from '@/store/scheme'
import { IMatchSchemItem } from '@/api/article'

const emit = defineEmits<{ (e: 'detail', value: boolean, name: string): void }>()

const showAction = ref(false)
const dataType = ref<number>()
const selected = ref<ISelectedMatchScheme[]>([])
const detail = ref<IBroomerItem & { odds: IOdds }>()

const { setScheme } = useSchemeStore()

defineProps<{ list: IBroomerItem[] }>()
function matchInfo(name: string, type: number) {
  return `${name}（${type === 2 ? '足球' : '竞足'}）`
}

function beforeMainPlayConfirm(v: number[], resolve: (r: boolean) => void) {
  if (v.length >= 3) {
    uni.showToast({ title: '主玩法最多只能选择两种', icon: 'none' })
    resolve(false)
  } else {
    resolve(true)
  }
}

function handleMainPlayConfirm(v: number[], id: number) {
  selected.value = selected.value.map((e) =>
    e.id === id
      ? {
          ...e,
          bonus: e.bonus.map((b) =>
            v.includes(b.playId) ? { ...b, disabled: true } : { ...b, disabled: false },
          ),
        }
      : e,
  )
}

function handleBonusPlayConfirm(v: number[], id: number) {
  selected.value = selected.value.map((e) =>
    e.id === id
      ? {
          ...e,
          main: e.main.map((b) =>
            v.includes(b.playId) ? { ...b, disabled: true } : { ...b, disabled: false },
          ),
        }
      : e,
  )
}

/* 选择赛事 */
function handleMatchSelected(e: IBroomerItem) {
  dataType.value = e.dataType
  const m = e.matchScheme[0].matchPlays
  console.log(m, 'm')
  selected.value = [
    ...selected.value,
    {
      ...e,
      main: m.map((item) => ({
        ...item,
        type: 0,
        value: item.playId,
        label: PLAY_TYPE_MAP[item.playId],
        disabled: false,
      })),
      bonus: m.map((item) => ({
        ...item,
        type: 1,
        value: item.playId,
        label: PLAY_TYPE_MAP[item.playId],
        disabled: false,
      })),
      mmatchPlays: [],
      bmatchPlays: [],
    },
  ]
}

/* 取消选中赛事 */
function handleMatchDeselected(i: number) {
  selected.value = selected.value.filter((e) => e.id !== i)
  if (selected.value.length === 0) dataType.value = undefined
}

function showSheetAction() {
  showAction.value = true
}

function closeSheetAction() {
  showAction.value = false
}

async function showDetail(item: IBroomerItem) {
  try {
    uni.showLoading()
    const { matchId, id, dataType: d, matchName } = item
    const selectedItem = selected.value.find((e) => e.id === id)
    // 如果没有被选中并且是同一场赛事，则不进行详情展示
    if (
      (!selectedItem && selected.value.find((e) => e.matchId === matchId)) ||
      (dataType.value && dataType.value !== item.dataType)
    )
      return

    const [odds] = await getMatchPlayOdds(matchId, d)
    detail.value = { ...item, odds }
    emit('detail', true, matchName)
  } finally {
    uni.hideLoading()
  }
}

function exitDetailModel() {
  detail.value = undefined
}

function handleReference(scheme: IBroomerItem & { odds: IOdds }) {
  const { odds, ...rest } = scheme
  const selectedItem = selected.value.find((e) => e.id === rest.id)
  if (!selectedItem) {
    handleMatchSelected(rest)
  }
  detail.value = undefined
  emit('detail', false, '')
  showSheetAction()
}

function confirm() {
  // 校验，每场比赛最少主玩法最少选择一项
  const invalid = selected.value.some((e) => isEmpty(e.mmatchPlays))
  if (invalid) {
    uni.showToast({ title: '请设置所有赛事的主玩法', icon: 'none' })
    return
  }

  const matchScheme: IMatchSchemItem[] = selected.value.map((m) => {
    const { matchScheme: mss, dataType, mmatchPlays, bmatchPlays } = m
    const [{ competitionName, matchPlays, ...other }] = mss

    return {
      ...other,
      dataType,
      matchPlays: [
        ...mmatchPlays.map((e) => ({ type: 0, playId: e })),
        ...bmatchPlays.map((e) => ({ type: 1, playId: e })),
      ].map(({ type, playId }) => {
        const mps = matchPlays.find((e) => e.playId === playId)!
        return {
          ...mps,
          type,
        }
      }),
    }
  })
  console.log(matchScheme, 'matchScheme')
  setScheme(matchScheme)
  // 跳转至发布页面 标记继续引用
  uni.navigateTo({ url: '/pages/article/relaese/index?continue=true' })
}

defineExpose({ exitDetailModel })
</script>

<style lang="scss" scoped>
.play-picker {
  --wot-cell-wrapper-padding: 0;
  --wot-cell-padding: 0;
}
</style>
