<template>
  <wd-action-sheet v-model="visible">
    <view class="pb-30rpx px-30rpx">
      <view class="relative center h-100rpx text-30rpx text-black text-opacity-90">
        选择战绩
        <view class="absolute right-30rpx w-30rpx h-30rpx" @click="closeAction">
          <wd-icon name="close" size="30rpx" color="rgba(0,0,0,0.5)" />
        </view>
      </view>
      <!-- 日期过滤栏 -->
      <view>
        <wd-datetime-picker
          v-model="date"
          type="date"
          :max-date="Date.now()"
          :min-date="minDate"
          placeholder="请选择日期"
        />
      </view>
      <!-- 战绩列表选项 -->
      <template v-if="!isEmpty(data)">
        <scroll-view scroll-y @touchmove.stop :show-scrollbar="false">
          <view class="h-600rpx py-30rpx">
            <wd-checkbox-group v-model="zhanji" :max="1">
              <template
                v-for="{ id, data: { keyword, accomplishmentReqVO }, createTime } in data"
                :key="id"
              >
                <wd-checkbox :modelValue="id" use-label-slot>
                  <view class="flex flex-col gap-y-10rpx py-30rpx">
                    <text class="text-28rpx text-black text-opacity-90 leading-40rpx">
                      {{ `${accomplishmentReqVO.startDate} ${keyword}` }}
                    </text>
                    <text class="text-26rpx text-black text-opacity-30 leading-36rpx">
                      {{ time(createTime) }}
                    </text>
                  </view>
                </wd-checkbox>
              </template>
            </wd-checkbox-group>
          </view>
        </scroll-view>
      </template>
      <view class="my-80rpx text-28rpx text-black text-opacity-30 text-center">
        仅保存30天内生成的战绩
      </view>
      <view>
        <wd-pagination
          v-model="pageNo"
          :total="total"
          :page-size="DEFAULT_PAGE_SIZE"
          show-icon
          @change="handleChange"
        />
      </view>
      <view>
        <wd-button size="large" block @click="confirm">确定</wd-button>
      </view>
    </view>
  </wd-action-sheet>
</template>
<script setup lang="ts">
import { IZhanji, getAccomplishmentPage } from '@/api/zhanji'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
import { format } from '@/utils/format'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash-es'

const visible = ref(false)
const pageNo = ref(1)
const zhanji = ref<number[]>([])
const total = ref(0)
const data = ref<IZhanji[]>([])

const minDate = ref(0)

const date = ref([])

const time = computed(() => {
  return (t: number) => (t ? format(t, 'YYYY-MM-DD HH:mm:ss') : '')
})

function closeAction() {
  visible.value = false
}

function open() {
  zhanji.value = []
  date.value = []
  visible.value = true
}

async function getData(no: number, d?: { date: string; endDate: string }) {
  const { list, total: t } = await getAccomplishmentPage(no, d)
  total.value = t
  data.value = list
}

function handleChange({ value }) {
  if (isEmpty(date.value)) {
    getData(value)
  } else {
    const [startDate, endDate] = date.value
    getData(value, { date: startDate, endDate })
  }
}

function confirm() {
  if (isEmpty(zhanji.value)) return
  const item = data.value.find(({ id }) => id === zhanji.value[0])
  console.log(item)
  emit('submit', zhanji.value, item)
  closeAction()
}

defineExpose({ open })

const emit = defineEmits<{ (e: 'submit', value: number[], o: IZhanji): void }>()

onLoad(async () => {
  getData(pageNo.value)
  minDate.value = dayjs().subtract(30, 'day').valueOf()
})
</script>
