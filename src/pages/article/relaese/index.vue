<route lang="json5">
{
  style: {
    //navigationStyle: 'custom',
    navigationBarTitleText: '发布方案',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <!-- <wd-navbar title="发布方案" left-arrow @click-left="handleClickLeft">
    <template #right>
      <view class="flex items-center gap-x-7rpx" @click="gotoReferenceRelease">
        <wd-icon name="translate-bold" color="#D1302E" size="30rpx" />
        <text class="text-24rpx">{{ c ? '继续发布' : '引用发布' }}</text>
      </view>
    </template>
</wd-navbar> -->
  <view class="p-30rpx bg-#F4F8FA pb-80rpx">
    <!-- 文章设置选项 -->
    <view class="p-30rpx rounded-md bg-white">
      <wd-form ref="articleRef" :model="article">
        <!-- 标题 -->
        <view class="title-container">
          <text class="title">方案标题</text>
        </view>
        <wd-input :readonly="!canEdit" v-model="article.title" prop="title" placeholder="请输入方案标题，不超过20个汉字字符" clearable
          :maxlength="20" />
        <!-- 简介 -->
        <view class="title-container">
          <text>精彩简介</text>
        </view>
        <wd-input :readonly="!canEdit" v-model="article.intro" prop="intro" placeholder="请输入方案的精彩简介，不超过50个汉字字符"
          clearable :maxlength="50" />
        <!-- 免费内容 -->
        <view class="title-container">
          <text>免费内容</text>
          <!-- <text class="text-[red]">(当前状态不允许编辑)</text> -->
          <!-- <wd-textarea
            v-model="article.freeContents"
            prop="freeContents"
            placeholder="请输入方案的免费内容，不超过50个汉字字符"
            :maxlength="50"
            auto-height
            clearable
          /> -->
          <piaoyi-editor class="w-650rpx" ref="freeEditorRef" v-model="article.freeContents"
            @changes="changeFreeContent" @ready="freeEditorReady" :photoUrl="photoUrl" :maxlength="10000" />
        </view>
        <!-- 付费内容 -->
        <view class="title-container">
          <text>付费内容</text>
          <!-- <text class="text-[red]">(当前状态不允许编辑)</text> -->
          <piaoyi-editor class="w-650rpx" ref="editorRef" v-model="article.contents" @changes="changeContent"
            @ready="readyEditor" :photoUrl="photoUrl" :maxlength="10000" />
        </view>
      </wd-form>
    </view>
    <!-- 其他设置选项 -->
    <view class="mt-20rpx p-30rpx rounded-md bg-white">
      <!-- 新增二维码背景选择 -->
      <!-- <view class="flex items-center h-100rpx border-bottom" @click="showBgList = !showBgList">
        <text class="text-black text-opacity-90 text-28rpx">选择二维码背景></text>
      </view> -->
      <scroll-view v-show="showBgList" :show-scrollbar="false" scroll-x
        class="whitespace-nowrap py-20rpx gap-30rpx px-10rpx">
        <view v-for="(img, index) in bgList" :key="index" class="relative inline-block mx-10rpx">
          <image :src="img.url" class="w-200rpx h-200rpx rounded-lg"
            :class="{ 'border-6rpx border-solid border-#FF0000': article.topBg === img.url }"
            @click="article.topBg = img.url" @error="showNameMap[index] = true" />
          <view v-if="showNameMap[index]"
            class="absolute inset-0 bg-gray-100/80 flex items-center justify-center text-24rpx p-10rpx break-all">
            {{ img.name }}
          </view>
        </view>
      </scroll-view>
      <!-- 优惠策略 -->
      <view class="flex justify-between items-center h-100rpx border-bottom">
        <text class="shrink-0 text-black text-opacity-90 text-28rpx">优惠策略></text>
        <wd-select-picker type="radio" align-right :columns="PREFERENTIAL" v-model="preferentialValue" />
      </view>
      <!-- 同步至套餐 -->
      <view class="flex justify-between items-center h-100rpx pr-30rpx">
        <text class="shrink-0 text-black text-opacity-90 text-28rpx">同步至套餐></text>
        <wd-switch v-model="tbztc" disabled />
      </view>
      <view class="h-60rpx text-right border-bottom pr-30rpx" @click="adthorAddPackage" v-if="!taocanList.length">
        <text class="text-red font-size-[28rpx] pr-10rpx">暂无套餐,去创建</text>
        <wd-icon name="arrow-right" color="rgba(0,0,0,0.3)" />
      </view>
      <view class="pr-30rpx taocan_list">
        <wd-checkbox-group v-model="selectedTaocan" cell>
          <template v-for="i of taocanList" :key="i">
            <wd-checkbox :modelValue="i.id">{{ i.name }}</wd-checkbox>
          </template>
        </wd-checkbox-group>
      </view>
      <!-- 设置初始购买人数 -->
      <view class="flex justify-between items-center h-100rpx border-bottom">
        <text class="shrink-0 text-black text-opacity-90 text-28rpx">设置初始购买人数></text>
        <wd-input-number v-model="initBuyCount" :min="0" :max="99999" input-width="100%" allow-null
          placeholder="请输入初始购买人数" class="goumairenshu pr-30rpx" />
      </view>
      <!-- end -->
      <!-- 新旧方案切换 -->
      <view class="flex items-center gap-x-20rpx mt-20rpx">
        <text class="text-28rpx text-black text-opacity-90">关联赛事></text>
        <wd-switch v-model="schemeMode" :active-value="true" :inactive-value="false" size="30rpx"
          @change="handleSchemeModeChange" />
      </view>
      <template v-if="schemeMode">
        <!-- 方案玩法 -->
        <view class="flex justify-between items-center h-100rpx border-bottom">
          <text class="text-black text-opacity-90 text-28rpx">选择方案玩法></text>
          <wd-select-picker type="radio" v-model="schemeValue" align-right :columns="schemes" @confirm="confirmScheme"
            :before-confirm="beforeConfirmScheme" />
        </view>
        <!-- <view
          class="flex justify-between items-center h-100rpx pr-30rpx border-bottom"
          @click="showZhanjiAction"
        >
          <text class="shrink-0 text-black text-opacity-90 text-28rpx">选择战绩></text>
          <view class="flex items-center gap-x-10px text-14px">
            <text v-if="accomplishment">{{ zhanjiTxt }}</text>
            <wd-icon name="arrow-right" color="rgba(0,0,0,0.3)" />
          </view>
        </view> -->
        <template v-if="showIssue">
          <view class="flex justify-between items-center h-100rpx">
            <text class="shrink-0 text-black text-opacity-90 text-28rpx">选择期数></text>
            <wd-select-picker type="radio" :columns="issueOptions" v-model="issue" @confirm="handleIssueConfirm" />
          </view>
        </template>
        <!-- 选择赛事(14场不显示选择赛事) -->
        <!-- 14场与任九只需要选择期数，不用选择赛事 -->
        <template v-if="![SCHEME_TYPE.MATCH_LOTTERY, SCHEME_TYPE.ANY_NINE].includes(schemeValue)">
          <match-info label="选择赛事>" :matchs="matchs" v-model="comp" :limit="matchLimit" />
        </template>
      </template>
    </view>
    <template v-if="schemeMode">
      <!-- 赛事预测 -->
      <wd-gap bg-color="#F4F8FA" height="20rpx" />
      <template v-for="(e, index) in matchResults" :key="e.matchId">
        <view class="px-40rpx pb-40rpx rounded-12rpx bg-white">
          <!-- 赛事头 -->
          <view class="flex justify-between items-center border-bottom">
            <text class="text-28rpx text-black text-opacity-90">{{ `赛事${index + 1}>` }}</text>
            <view
              class="flex flex-col justify-between items-end py-30rpx text-28rpx text-black text-opacity-50 leading-39rpx">
              <text>{{ matchInfoTxt(e) }}</text>
              <text>{{ `${e.homeName}VS${e.awayName}` }}</text>
            </view>
          </view>
          <!-- 主玩法 -->
          <view class="flex justify-between items-center h-140rpx" v-if="!hidePlayMethod">
            <text class="shrink-0 text-black text-opacity-90 text-28rpx">选择主玩法></text>
            <wd-select-picker align-right :type="gamePlayType" :columns="matchResults[index].gamePlayOptions"
              v-model="matchResults[index].main" :before-confirm="(v, r) => beforeConfirmGamePlay(v, r, index)"
              @confirm="(v) => confirmGamePlay(v, index)" />
          </view>
          <!-- 附赠玩法 -->
          <view v-if="showBonusGamePlay && !hidePlayMethod" class="flex justify-between items-center h-140rpx">
            <text class="shrink-0 text-black text-opacity-90 text-28rpx">选择附赠玩法(选填)></text>
            <wd-select-picker align-right :type="gamePlayType" :columns="matchResults[index].bonusGamePlayOptions"
              v-model="matchResults[index].bonus" @confirm="(v) => confirmBonusGamePlay(v, index)" />
          </view>
          <!-- 主玩法 -->
          <template v-for="({ playId }, idx) in matchResults[index].mmatchPlays" :key="playId">
            <!-- 胜平负 -->
            <template v-if="playId === PLAY_TYPE.WIN_LOSE_DRAW">
              <result-caption caption="胜平负" type="main" />
              <wd-checkbox-group shape="button" custom-class="match-checkbox"
                v-model="matchResults[index].mmatchPlays[idx].result" use-label-slot
                :max="typeLimit(PLAY_TYPE.WIN_LOSE_DRAW)">
                <wd-checkbox :modelValue="3" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>主队赢</text>
                    <text class="text-opacity-50">
                      {{ winLoseDrawRates(e.odds.spf)[0] }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="1" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>平局</text>
                    <text class="text-opacity-50">
                      {{ winLoseDrawRates(e.odds.spf)[1] }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="0" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>客队赢</text>
                    <text class="text-opacity-50">
                      {{ winLoseDrawRates(e.odds.spf)[2] }}
                    </text>
                  </view>
                </wd-checkbox>
              </wd-checkbox-group>
            </template>
            <!-- 比分 -->
            <template v-else-if="playId === PLAY_TYPE.SCORE">
              <result-caption caption="比分" type="main" />
              <bf-result :limit="typeLimit(PLAY_TYPE.SCORE)" :match="{ id: e.matchId, ...e }"
                v-model="matchResults[index].mmatchPlays[idx].result" :bonus="e.odds.bf" />
            </template>
            <!-- 让球胜平负 -->
            <template v-else-if="playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW">
              <result-caption caption="让球胜平负" type="main" />
              <wd-checkbox-group shape="button" custom-class="match-checkbox" use-label-slot :disabled="!e.odds.rq"
                v-model="matchResults[index].mmatchPlays[idx].result"
                :max="typeLimit(PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW)">
                <wd-checkbox :modelValue="3" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>{{ handicapHomeTxt(e.odds.rq) }}</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[1] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="1" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>平局</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[2] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="0" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>{{ handicapAwayTxt(e.odds.rq) }}</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[3] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
              </wd-checkbox-group>
            </template>
            <!-- 让球(与让球胜平负的区别只有胜和负可选) -->
            <template v-else-if="playId === PLAY_TYPE.RQ">
              <result-caption caption="让球" type="main" />
              <wd-checkbox-group shape="button" custom-class="rq-checkbox" use-label-slot :disabled="!e.odds.rq"
                v-model="matchResults[index].mmatchPlays[idx].result" :max="typeLimit(PLAY_TYPE.RQ)">
                <wd-checkbox :modelValue="3" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>{{ handicapHomeTxt(e.odds.rq) }}</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[1] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="0" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>{{ handicapAwayTxt(e.odds.rq) }}</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[3] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
              </wd-checkbox-group>
            </template>
            <!-- 半全场 -->
            <template v-else-if="playId === PLAY_TYPE.BQC">
              <result-caption caption="半全场" type="main" />
              <bqc-result :limit="typeLimit(PLAY_TYPE.BQC)" :match="{ id: e.matchId, ...e }"
                v-model="matchResults[index].mmatchPlays[idx].result" :bonus="e.odds.bqc" />
            </template>
            <!-- 进球 -->
            <template v-else-if="playId === PLAY_TYPE.JQ">
              <result-caption caption="进球" type="main" />
              {{ typeLimit(PLAY_TYPE.JQ) }}撒大大
              <jq-result :limit="typeLimit(PLAY_TYPE.JQ)" :match="{ id: e.matchId, ...e }"
                v-model="matchResults[index].mmatchPlays[idx].result" :bonus="e.odds.jq" />
            </template>
            <!-- 大小球 -->
            <template v-else-if="playId === PLAY_TYPE.DXQ">
              <result-caption caption="大小球" type="main" />
              <view class="flex items-center border-1rpx border-solid border-#797979 border-opacity-10">
                <text class="center flex-1 text-24rpx">
                  {{ `进球数（${e.odds.dxq ? e.odds.dxq.split(',')[1] : 0}）` }}
                </text>
                <wd-checkbox-group shape="button" custom-class="dxq-checkbox" use-label-slot :disabled="!e.odds.dxq"
                  v-model="matchResults[index].mmatchPlays[idx].result" :max="typeLimit(PLAY_TYPE.DXQ)">
                  <wd-checkbox :modelValue="3" max-width="100rpx" use-label-slot>
                    <text>{{ `大（${e.odds.dxq ? e.odds.dxq.split(',')[0] : 0}）` }}</text>
                  </wd-checkbox>
                  <wd-checkbox :modelValue="0" max-width="100rpx" use-label-slot>
                    <text>{{ `小（${e.odds.dxq ? e.odds.dxq.split(',')[2] : 0}）` }}</text>
                  </wd-checkbox>
                </wd-checkbox-group>
              </view>
            </template>
          </template>
          <!-- 附赠玩法 -->
          <template v-for="({ playId }, idx) in matchResults[index].bmatchPlays" :key="playId">
            <!-- 胜平负 -->
            <template v-if="playId === PLAY_TYPE.WIN_LOSE_DRAW">
              <result-caption caption="胜平负" type="bonus" />
              <wd-checkbox-group shape="button" custom-class="match-checkbox"
                v-model="matchResults[index].bmatchPlays[idx].result" use-label-slot
                :max="typeLimit(PLAY_TYPE.WIN_LOSE_DRAW)">
                <wd-checkbox :modelValue="3" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>主队赢</text>
                    <text class="text-opacity-50">
                      {{ winLoseDrawRates(e.odds.spf)[0] }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="1" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>平局</text>
                    <text class="text-opacity-50">
                      {{ winLoseDrawRates(e.odds.spf)[1] }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="0" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>客队赢</text>
                    <text class="text-opacity-50">
                      {{ winLoseDrawRates(e.odds.spf)[2] }}
                    </text>
                  </view>
                </wd-checkbox>
              </wd-checkbox-group>
            </template>
            <!-- 比分 -->
            <template v-else-if="playId === PLAY_TYPE.SCORE">
              <result-caption caption="比分" type="bonus" />
              <bf-result :limit="typeLimit(PLAY_TYPE.SCORE)" :match="{ id: e.matchId, ...e }"
                v-model="matchResults[index].bmatchPlays[idx].result" :bonus="e.odds.bf" />
            </template>
            <!-- 让球胜平负 -->
            <template v-else-if="playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW">
              <result-caption caption="让球胜平负" type="bonus" />
              <wd-checkbox-group shape="button" custom-class="match-checkbox" use-label-slot :disabled="!e.odds.rq"
                v-model="matchResults[index].bmatchPlays[idx].result"
                :max="typeLimit(PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW)">
                <wd-checkbox :modelValue="3" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>{{ handicapHomeTxt(e.odds.rq) }}</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[1] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="1" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>平局</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[2] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="0" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>{{ handicapAwayTxt(e.odds.rq) }}</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[3] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
              </wd-checkbox-group>
            </template>
            <!-- 让球(与让球胜平负的区别只有胜和负可选) -->
            <template v-else-if="playId === PLAY_TYPE.RQ">
              <result-caption caption="让球" type="bonus" />
              <wd-checkbox-group shape="button" custom-class="rq-checkbox" use-label-slot :disabled="!e.odds.rq"
                v-model="matchResults[index].bmatchPlays[idx].result" :max="typeLimit(PLAY_TYPE.RQ)">
                <wd-checkbox :modelValue="3" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>{{ handicapHomeTxt(e.odds.rq) }}</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[1] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
                <wd-checkbox :modelValue="0" max-width="100rpx" use-label-slot>
                  <view class="flex flex-col items-center">
                    <text>{{ handicapAwayTxt(e.odds.rq) }}</text>
                    <text class="text-opacity-50">
                      {{ e.odds.rq ? e.odds.rq.split(',')[3] : 0 }}
                    </text>
                  </view>
                </wd-checkbox>
              </wd-checkbox-group>
            </template>
            <!-- 半全场 -->
            <template v-else-if="playId === PLAY_TYPE.BQC">
              <result-caption caption="半全场" type="bonus" />
              <bqc-result :limit="typeLimit(PLAY_TYPE.BQC)" :match="{ id: e.matchId, ...e }"
                v-model="matchResults[index].bmatchPlays[idx].result" :bonus="e.odds.bqc" />
            </template>
            <!-- 进球 -->
            <template v-else-if="playId === PLAY_TYPE.JQ">
              <result-caption caption="进球" type="bonus" />
              <jq-result :limit="typeLimit(PLAY_TYPE.JQ)" :match="{ id: e.matchId, ...e }"
                v-model="matchResults[index].bmatchPlays[idx].result" :bonus="e.odds.jq" />
            </template>
            <!-- 大小球 -->
            <template v-else-if="playId === PLAY_TYPE.DXQ">
              <result-caption caption="大小球" type="bonus" />
              <view class="flex items-center border-1rpx border-solid border-#797979 border-opacity-10">
                <text class="center flex-1 text-24rpx">
                  {{ `进球数（${e.odds.dxq ? e.odds.dxq.split(',')[1] : 0}）` }}
                </text>
                <wd-checkbox-group shape="button" custom-class="dxq-checkbox" use-label-slot :disabled="!e.odds.dxq"
                  v-model="matchResults[index].bmatchPlays[idx].result" :max="typeLimit(PLAY_TYPE.DXQ)">
                  <wd-checkbox :modelValue="3" max-width="100rpx" use-label-slot>
                    <text>{{ `大（${e.odds.dxq ? e.odds.dxq.split(',')[0] : 0}）` }}</text>
                  </wd-checkbox>
                  <wd-checkbox :modelValue="0" max-width="100rpx" use-label-slot>
                    <text>{{ `小（${e.odds.dxq ? e.odds.dxq.split(',')[2] : 0}）` }}</text>
                  </wd-checkbox>
                </wd-checkbox-group>
              </view>
            </template>
          </template>
        </view>
        <wd-gap bg-color="#F4F8FA" height="20rpx" />
      </template>
    </template>
    <!-- 设置售价 -->
    <view class="flex items-start gap-x-40rpx mt-30rpx mb-20rpx">
      <text class="text-28rpx text-black text-opacity-90">设置售价></text>
      <view class="flex flex-wrap gap-20rpx flex-1">
        <template v-for="p in ARTICLE_PRICE_TEMPLATE" :key="p">
          <text @click="selectPrice(p)" v-if="p === '0'"
            class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
            style="width: calc((100% - 40rpx) / 3)" :style="pStyle(p)">
            免费
          </text>
          <text @click="selectPrice(p)" v-else
            class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
            style="width: calc((100% - 40rpx) / 3)" :style="pStyle(p)">
            {{ p }}
          </text>
        </template>
        <text @click="toggleCustomizePrice"
          class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
          style="width: calc((100% - 40rpx) / 3)">
          自定义
        </text>
      </view>
    </view>
    <template v-if="showCustomizePrice">
      <wd-input-number v-model="price" :precision="2" :min="0" :max="99999" input-width="100%" allow-null
        placeholder="请输入自定义价格" />
    </template>
    <!-- 发布 -->
    <view @click="publish"
      class="h-100rpx my-30rpx px-30rpx rounded-lg bg-#D1302E text-white text-32rpx text-center leading-100rpx">
      发布
    </view>
    <!-- 保存为草稿箱 -->
    <view class="flex justify-center items-center gap-x-10rpx" @click="saveAsDraft">
      <wd-icon name="evaluation" size="30rpx" color="rgba(0,0,0,0.5)" />
      <text class="text-28rpx text-black text-opacity-50 leading-40rpx">保存草稿箱</text>
    </view>
    <!-- 战绩弹框 -->
    <zhanji ref="zhanjiRef" @submit="handleZhanjiSelected" />
  </view>
</template>

<script setup lang="ts">
import {
  IGamePlayItem,
  IScheme,
  getArticleDraft,
  getGameplay,
  getPlayMethodById,
  publishArticle,
} from '@/api/author'
import { isArray, isEmpty, isNil, pickBy, groupBy, keys, omit } from 'lodash-es'
import { ARTICLE_PRICE_TEMPLATE, PREFERENTIAL } from '@/utils/constant'
import { IMatchInfoItem, IMatchSchemeItem, getMatchListInfo, getMatchPlayOdds } from '@/api/match'
import matchInfo from './components/matchInfo.vue'
import resultCaption from './components/resultCaption.vue'
import bfResult from './components/bfResult.vue'
import bqcResult from './components/bqcResult.vue'
import jqResult from './components/jqResult.vue'
import { PLAY_TYPE, GAME_PLAY_TYPE, SCHEME_TYPE } from '@/utils/enum'
import zhanji from './components/zhanji.vue'
import { format, formatWeek } from '@/utils/format'
import { IMatchResult, getArticleDetail } from '@/api/article'
import { getPublishPrivilegeList, getPrivilegePvLog, getPrivilegeStatistics } from '@/api/member'
import { useIntervalFn } from '@vueuse/core'
import { IZhanji } from '@/api/zhanji'
import { useSchemeStore } from '@/store/scheme'

const zhanjiRef = ref()
const photoUrl = import.meta.env.VITE_UPLOAD_BASEURL

const articleRef = ref()
const editorRef = ref()
const freeEditorRef = ref()
const article = ref({
  title: '',
  intro: '',
  freeContents: '',
  contents: '',
  topBg: '',
})

const showBgList = ref(false)
const showNameMap = ref({})
const bgList = ref([
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg.png', name: '默认背景' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg1.png', name: '背景1' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg2.png', name: '背景2' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg3.png', name: '背景3' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg4.png', name: '背景4' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg5.png', name: '背景5' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg6.png', name: '背景6' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg7.png', name: '背景7' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg8.png', name: '背景8' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg9.png', name: '背景9' },
])

const schemes = ref<(IScheme & { label: string; value: number })[]>([])
const schemeValue = ref<number>()
const accomplishment = ref<number | null>(null)
const selectedAccomplishmentItem = ref<IZhanji>()
// 是否继续引用
const c = ref(false)
const schemeMode = ref(true)
const taocanList = ref([]) // 套餐列表

function handleSchemeModeChange({ value }: { value: boolean }) {
  if (!value) {
  }
}

const zhanjiTxt = computed(() => {
  const item = selectedAccomplishmentItem.value
  if (item) {
    const {
      data: { keyword, accomplishmentReqVO },
    } = item
    return `${accomplishmentReqVO.startDate} ${keyword}`
  }
  return ''
})

const gamePlay = ref<IGamePlayItem[]>([])
/* 玩法是否允许多选 radio 单选  checkbox 多选 */
const gamePlayType = ref<'radio' | 'checkbox'>('radio')
// const gamePlayValue = ref<number[] | number>([])

function showZhanjiAction() {
  zhanjiRef.value.open()
}

/* 是否显示附赠玩法 */
const showBonusGamePlay = computed(() => {
  const scheme = schemes.value.find(({ id }) => id === schemeValue.value)
  if (!scheme) return false
  return !!scheme.bundled
})

/* 是否显示期数 */
const showIssue = computed(() => {
  const scheme = schemes.value.find(({ id }) => id === schemeValue.value)
  if (!scheme) return false
  return scheme.dataType === 1
})

const { scheme, clearScheme } = useSchemeStore()

// 附赠玩法
// const bonusGamePlayValue = ref<number[] | number>([])

// 是否隐藏主玩法和附赠玩法
const hidePlayMethod = computed(() =>
  [SCHEME_TYPE.MATCH_LOTTERY, SCHEME_TYPE.ANY_NINE].includes(schemeValue.value),
)

// 主队胜率(胜平负)
const winLoseDrawRates = computed(() => {
  return (o: string | null) => (o ? o.split(',').map(parseFloat) : [0, 0, 0])
})

// 主队胜率(让胜负平)
const handicapWinLoseDrawRates = computed(() => {
  return (o: string | null) => (o ? o.split(',').map(parseFloat) : [0, 0, 0, 0])
})

const handicapHomeTxt = computed(() => {
  return (o: string | null) => {
    const handicapRates = handicapWinLoseDrawRates.value(o)
    // return `主队${0 - handicapRates[0]}`
    return `主队${handicapRates[0]}`
  }
})

const handicapAwayTxt = computed(() => {
  return (o: string | null) => {
    const handicapRates = handicapWinLoseDrawRates.value(o)
    // return `客队${handicapRates[0]}`
    return `客队${0 - handicapRates[0]}`
  }
})

function handleZhanjiSelected(e: number[], o: IZhanji) {
  accomplishment.value = e[0]
  selectedAccomplishmentItem.value = o
}

interface IData {
  id?: number
  schemePlay: number // 方案玩法schemeValue
  /*
   * 下面的这些都是垃圾字段，是为了兼容以前的版本数据
   *  后端调整后可以删除，对本版本没有任何影响
   */
  consumeStatus: number
  consumeMinNum: number
  consumeMaxNum: number
  consumeMinAmount: number
  consumeMaxAmount: number
}

const canEdit = ref(1) // 是否可以编辑(0 不能 1 可以)

/* 结果预测 */
const data = ref<IData>({
  consumeStatus: 0,
  consumeMinNum: 0,
  consumeMaxNum: 0,
  consumeMinAmount: 0,
  consumeMaxAmount: 0,
} as IData)

// 比赛结果数据
const matchResults = ref<IMatchSchemeItem[]>([])

const matchInfoTxt = computed(() => {
  // return (match: IMatchInfoItem) => {
  return (match: Pick<IMatchSchemeItem, 'matchTime' | 'roundName' | 'issueNum'>) => {
    const { matchTime, roundName, issueNum } = match
    const datetime = matchTime * 1000
    const week = formatWeek(datetime)
    const time = format(datetime, 'HH:mm')

    let ret = `${week} ${roundName}`
    ret = issueNum ? `${ret} ${issueNum}` : ret
    ret = `${ret} ${time}`
    return ret
  }
})

const originMatchs = ref<(IMatchInfoItem & { id: number })[]>([])
const comp = ref<(Omit<IMatchInfoItem, 'comp'> & { id: number })[]>([])
/* 选中的期数 */
const issue = ref('')

/* 14场与任九(选择期数的时候使用) */
const matchGroup = computed(() =>
  schemeValue.value < 3 ? groupBy(originMatchs.value, 'issue') : [],
)
const matchs = computed(() => {
  if (schemeValue.value === SCHEME_TYPE.ANY_NINE) {
    return issue.value ? matchGroup.value[issue.value] : []
  }
  return originMatchs.value
})

/* 期数 */
const issueOptions = computed(() =>
  schemeValue.value < 3 ? keys(matchGroup.value).map((k) => ({ label: `第${k}期`, value: k })) : [],
)

function handleIssueConfirm({ value }: { value: string }) {
  comp.value = []
  const m = matchGroup.value[value] || []
  // if (schemeValue.value === SCHEME_TYPE.MATCH_LOTTERY) {
  //   comp.value = m
  // }

  if ([SCHEME_TYPE.MATCH_LOTTERY, SCHEME_TYPE.ANY_NINE].includes(schemeValue.value)) {
    comp.value = m
  }
}

/* 赛事场次选择限制 */
const matchLimit = ref<null | number>(null)

/* 结果预测选项限制 */
// const typeLimit = computed(() => {
//   return (type: GAME_PLAY_TYPE) => {
//     // 14场与任九默认自动选择胜平负玩法，并且选项数量不做限制(最多可以选择三项)
//     if ([SCHEME_TYPE.MATCH_LOTTERY, SCHEME_TYPE.ANY_NINE].includes(schemeValue.value)) return 3
//     const gp = gamePlay.value.find(({ resultType }) => resultType === type)
//     return gp ? gp.resultNum : 0
//   }
// })
const typeLimit = computed(() => {
  return (type: PLAY_TYPE) => {
    // 14场与任九默认自动选择胜平负玩法，并且选项数量不做限制(最多可以选择三项)
    if ([SCHEME_TYPE.MATCH_LOTTERY, SCHEME_TYPE.ANY_NINE].includes(schemeValue.value)) return 3
    const gp = gamePlay.value.find(({ id }) => id === type)

    return gp ? gp.resultNum : 0
  }
})

/* 优惠策略 */
const preferentialValue = ref(0)

/* 设置初始购买人数 */
const initBuyCount = ref(20)

const selectedTaocan = ref([])
const checked = ref(false)
const value = ref<string[]>([])
const checkBox1 = ref()
const checkBox2 = ref()
const checkBox3 = ref()
/* 同步至套餐 */
const tbztc = computed(() => {
  if (selectedTaocan.value.length > 0) {
    return true
  } else {
    return false
  }
})

function handleCheck1() {
  checkBox1.value && checkBox1.value.toggle()
}

function handleCheck2() {
  checkBox2.value && checkBox2.value.toggle()
}
function handleCheck3() {
  checkBox3.value && checkBox3.value.toggle()
}

function noop() { }

/* 不中即退 */
const refundType = computed(() => (preferentialValue.value === 2 ? 1 : 0))

/* 可补单 */
const autoReplacement = computed(() => (preferentialValue.value === 1 ? 1 : 0))

const showCustomizePrice = ref(false)
const price = ref<string | number>('')

function changeContent(v: { html: string }) {
  article.value.contents = v.html
}

function changeFreeContent(v: { html: string }) {
  article.value.freeContents = v.html
}

const pStyle = computed(() => {
  return (p: string) => {
    if (showCustomizePrice.value) return 'background: rgba(0,0,0,0.04);opacity: 0.4'

    if (p === price.value)
      return 'background: rgba(209, 48, 46, 0.10);border: 1px solid #D1302E; color: #D1302E'

    return ''
  }
})

/* 选择模板价格 */
function selectPrice(p: string) {
  if (showCustomizePrice.value || p === price.value) return
  price.value = p
}

/* 切换自定义输入价格或使用模板价格 */
function toggleCustomizePrice() {
  price.value = ''
  showCustomizePrice.value = !showCustomizePrice.value
}

/* 根据id查询对应的玩法对象 */
const gamePlayItem = computed(() => {
  return (gid: number) => gamePlay.value.find(({ id }) => id === gid) ?? ({} as IGamePlayItem)
})

const schemeChange = ref(false)

function beforeConfirmScheme(value: number, resolve: (r: boolean) => void) {
  if (value !== schemeValue.value) schemeChange.value = true
  resolve(true)
}

function handleClickLeft() {
  uni.switchTab({ url: '/pages/index/index' })
}

function gotoReferenceRelease() {
  uni.navigateTo({ url: '/pages/article/relaese/reference' })
}

/* 确认方案玩法 */
/* 确认方案玩法 */
async function confirmScheme({
  selectedItems,
}: {
  selectedItems: IScheme & { label: string; value: number }
}) {
  // 如果选择没有更改，则无需执行下面的业务逻辑
  if (!schemeChange.value) return

  // mainMatchResults.value = []
  matchResults.value = []

  comp.value = [] // 重置选中的比赛列表
  const { session, multiple, play, dataType, value } = selectedItems
  gamePlayType.value = multiple === 1 ? 'radio' : 'checkbox'
  matchLimit.value = session ? session : null

  data.value.schemePlay = value
  const [g, m] = await Promise.all([getPlayMethodById(play), getMatchListInfo(dataType)])

  const gp = g.map((e) => ({ ...e, label: e.name, value: e.id }))

  gamePlay.value = gp
  originMatchs.value = m.map((e) => ({ ...e, id: e.matchId }))

  schemeChange.value = false

  issue.value = ''

  if (value < 3) {
    comp.value = []
  }
}

const readyEditor = () => {
  editorReady.value = true
}

function beforeConfirmGamePlay(v: number | number[], resolve: (r: boolean) => void, idx: number) {
  if (isArray(v) && v.length > 2) {
    uni.showToast({ title: '主玩玩法最多只能选择两个', icon: 'none' })
    return resolve(false)
  }

  resolve(true)
}

/* 主玩法与附赠玩法选择要互斥(主玩法选取了的玩法，附赠玩法不能再选择) */
function confirmGamePlay({ value, selectedItems }, index: number) {
  let v = []
  if (value) {
    v = isArray(value) ? value : [value]
  }

  const mr = matchResults.value[index]
  const bpo = mr.bonusGamePlayOptions
  let s = []
  if (isArray(selectedItems)) {
    s = selectedItems
  } else if (selectedItems && value) {
    s = [selectedItems]
  }
  matchResults.value[index] = {
    ...mr,
    bonusGamePlayOptions: bpo.map((item) => ({ ...item, disabled: v.includes(item.id) })),
    mmatchPlays: s.map(({ id: playId, resultType }) => ({
      opinion: 0,
      playId,
      type: 0,
      resultType,
      result: gamePlayType.value === 'radio' ? null : [],
      dataType: schemeValue.value,
    })),
  }
}

/* 附赠玩法与主玩法选择要互斥(附赠玩法选取了的玩法，主玩法不能再选择) */
function confirmBonusGamePlay({ value, selectedItems }, index: number) {
  let v = []
  if (value) {
    v = isArray(value) ? value : [value]
  }

  const mr = matchResults.value[index]
  const po = mr.gamePlayOptions
  matchResults.value[index] = {
    ...mr,
    gamePlayOptions: po.map((item) => ({ ...item, disabled: v.includes(item.id) })),
    bmatchPlays: selectedItems.map(({ id: playId, resultType }) => ({
      opinion: 0,
      playId,
      type: 1,
      resultType,
      result: gamePlayType.value === 'radio' ? null : [],
      dataType: schemeValue.value,
    })),
  }
}

const unWatch = watch([comp, schemeValue], async ([n, s]) => {
  if (isEmpty(n)) {
    matchResults.value = []
    return
  }

  const matchId = n.map(({ id }) => id).join(',')
  const scheme = schemes.value.find(({ id }) => id === schemeValue.value)
  const dataType = scheme.dataType
  const oddsData = await getMatchPlayOdds(matchId, dataType)
  const msr = matchResults.value

  /* 14场与任九需要隐藏附赠玩法，并默认选择胜平负 */
  const hidePlay = [SCHEME_TYPE.MATCH_LOTTERY, SCHEME_TYPE.ANY_NINE].includes(s)

  // matchResults.value = n.map(({ matchId, homeName, awayName, roundName, matchTime, issueNum }) => {
  matchResults.value = n.map(({ matchId, ...rest }) => {
    const odds = oddsData.find((o) => o.matchId === matchId)!
    // gamePlayType.value === ''
    // 旧数据需要保留下来
    const old = msr.find((mr) => mr.matchId === matchId)

    if (old) return old

    let d: IMatchSchemeItem = {
      ...rest,
      matchId,
      opinion: 0,
      odds,
      mmatchPlays: [],
      gamePlayOptions: gamePlay.value,
      bonusGamePlayOptions: gamePlay.value,
      main: gamePlayType.value === 'checkbox' ? [] : null,
    }

    d = hidePlay
      ? {
        ...d,
        mmatchPlays: [
          {
            opinion: 0,
            type: 0,
            playId: PLAY_TYPE.WIN_LOSE_DRAW,
            dataType: dataType,
            resultType: GAME_PLAY_TYPE.WIN_LOSE_DRAW,
            result: [],
          },
        ],
      }
      : { ...d, mmatchPlays: [], bmatchPlays: [] }

    return showBonusGamePlay.value
      ? { ...d, bonus: gamePlayType.value === 'checkbox' ? [] : null }
      : d
  })
})

function submit(draft: 0 | 1 = 0) {
  if (!article.value.title) {
    // 如果草稿没有标题
    uni.showToast({
      title: '文章标题不能为空',
      icon: 'none',
    })
    return
  }

  if (schemeMode.value && !schemeValue.value) {
    uni.showToast({
      title: '请选择方案玩法',
      icon: 'none',
    })
    return
  }

  if (!draft) {
    /* 表单验证(如果不是草稿则要进行表单验证) */
    const { title, contents, freeContents } = article.value
    // 验证文章标题与简介是否有写
    if (!title) {
      uni.showToast({
        title: '文章标题不能为空',
        icon: 'none',
      })
      return
    }

    // 文章价格不能为空
    if (!price.value) {
      uni.showToast({
        title: '请输入文章价格',
        icon: 'none',
      })
      return
    }

    if (price.value && !contents) {
      uni.showToast({
        title: '请填写付费内容',
        icon: 'none',
      })
      return
    }

    if (!freeContents && !contents) {
      uni.showToast({
        title: '免费内容与付费内容不能为空',
        icon: 'none',
      })
      return
    }
  }

  if (!article.value.title) {
    // 如果草稿没有标题
    uni.showToast({
      title: '文章标题不能为空',
      icon: 'none',
    })
    return
  }

  // 1、验证场次数目选择是否正确
  const selectedMatchCount = matchResults.value.reduce((p, { mmatchPlays }) => {
    return mmatchPlays.every(({ result }) => !isEmpty(result)) ? p + 1 : p
  }, 0)

  if (schemeMode.value && matchLimit.value && selectedMatchCount != matchLimit.value) {
    uni.showToast({
      title: `需要选择${matchLimit.value}场比赛,已选择${selectedMatchCount}场`,
      icon: 'none',
    })
    return
  }

  // 组装数据
  let d: any = schemeMode.value ? data.value : omit(data.value, 'schemePlay')

  /* 14场与任九需要选择期数 */
  if (schemeValue.value < 3) {
    if (!issue.value) {
      uni.showToast({
        title: '请选择期数',
        icon: 'none',
      })
      return
    }
    d = { ...d, issue: issue.value }
  }

  // 战绩
  d = accomplishment.value ? { ...d, accomplishment: accomplishment.value } : d

  // 除了14场与任九,其余都需要验证玩法赔率
  // const noVerifyScheme = [SCHEME_TYPE.MATCH_LOTTERY, SCHEME_TYPE.ANY_NINE]

  // 验证赔率
  // if (!noVerifyScheme.includes(schemeValue.value)) {
  //   const oddsArrray = matchResults.value.reduce((prev, curr) => {
  //     const { odds, mmatchPlays: matchPlays } = curr

  //     const currentOdds = matchPlays.reduce((pcmp, cmp) => {
  //       const { type, resultType, result } = cmp
  //       // 只有主玩法才需要进行赔率验证
  //       if (type === 0) {
  //         switch (resultType) {
  //           case GAME_PLAY_TYPE.WIN_LOSE_DRAW: // 胜平负
  //             const spfo = odds && odds.spf ? odds.spf.split(',').map(parseFloat) : [0, 0, 0]
  //             return [
  //               ...pcmp,
  //               ...result.map((v) => {
  //                 if (v === 3) return spfo[0]
  //                 if (v === 1) return spfo[1]
  //                 if (v === 0) return spfo[2]
  //               }),
  //             ]
  //           case GAME_PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW: // 让球胜平负
  //             const rqo = odds && odds.rq ? odds.rq.split(',').map(parseFloat) : [0, 0, 0, 0]
  //             return [
  //               ...pcmp,
  //               ...result.map((v) => {
  //                 if (v === 3) return rqo[1]
  //                 if (v === 1) return rqo[2]
  //                 if (v === 0) return rqo[3]
  //               }),
  //             ]
  //           case GAME_PLAY_TYPE.SCORE: // 比分
  //             const bfo =
  //               odds && odds.bf
  //                 ? odds.bf.split(',').map(parseFloat)
  //                 : Array.from({ length: BF_SCORES.length }, () => 0)
  //             return [
  //               ...pcmp,
  //               ...result.map((v) => {
  //                 const idx = BF_SCORES.findIndex((e) => e === v)
  //                 return idx >= 0 ? bfo[idx] : 0
  //               }),
  //             ]
  //           case GAME_PLAY_TYPE.BQC: // 半全场
  //             const bqco =
  //               odds && odds.bqc
  //                 ? odds.bqc.split(',')
  //                 : Array.from({ length: BQC_SCORE.length }, () => 0)
  //             return [
  //               ...pcmp,
  //               ...result.map((v) => {
  //                 const idx = BQC_SCORE.findIndex((e) => e === v)
  //                 return idx >= 0 ? bqco[idx] : 0
  //               }),
  //             ]
  //           case GAME_PLAY_TYPE.JQ: // 进球
  //             const jqo =
  //               odds && odds.jq
  //                 ? odds.jq.split(',')
  //                 : Array.from({ length: JQ_SCORE.length }, () => 0)
  //             return [
  //               ...pcmp,
  //               ...result.map((v) => {
  //                 const idx = JQ_SCORE.findIndex((e) => e === v)
  //                 return idx >= 0 ? jqo[idx] : 0
  //               }),
  //             ]
  //         }
  //       }
  //       return pcmp
  //     }, [])

  //     return [...prev, ...currentOdds]
  //   }, [])

  //   const len = oddsArrray.length
  //   if (len) {
  //     const averageOdds = (oddsArrray.reduce((p, v) => p + v, 0) - len) / len
  //     if (averageOdds < 2.4) {
  //       uni.showToast({ title: '平均赔率不能低于2.4', icon: 'none' })
  //       return
  //     }
  //   }
  // }

  if (schemeMode.value) {
    let matchScheme = matchResults.value.map(
      ({
        odds,
        matchPlays,
        mmatchPlays,
        bmatchPlays,
        main,
        bonus,
        gamePlayOptions,
        bonusGamePlayOptions,
        ...rest
      }) => {
        const _matchPlays = [...mmatchPlays, ...(bmatchPlays || [])]

        // const mps = matchPlays.map((mp) => ({
        //   playId: mp.playId,
        //   type: mp.type,
        //   resultType: mp.resultType,
        //   opinion: mp.opinion,
        //   result: mp.result.join(','),
        // }))
        const mps = _matchPlays.map((mp) => {
          let _mp = {
            playId: mp.playId,
            type: mp.type,
            resultType: mp.resultType,
            opinion: mp.opinion,
            // result: mp.result.join(','),
            result: mp.result.join(','),
          }

          if (mp.playId === PLAY_TYPE.DXQ) {
            const dxqNum = odds.dxq ? odds.dxq.split(',')[1] : 0
            _mp = { ..._mp, result: [dxqNum, ...mp.result].join(',') }
          } else if ([PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, PLAY_TYPE.RQ].includes(mp.playId)) {
            const rqNum = odds.rq ? parseInt(odds.rq.split(',')[0]) : 0
            // 让球胜平负,大小球，让球还需要将让球的数据放在result中
            _mp = { ..._mp, result: [rqNum, ...mp.result].join(',') }
          }

          return _mp
        })

        return {
          matchPlays: mps,
          ...rest,
        }
      },
    )

    // 任九里面有五场是空的，需要过滤
    if (schemeValue.value === SCHEME_TYPE.ANY_NINE) {
      matchScheme = matchScheme.filter(({ matchPlays }) => !isEmpty(matchPlays))
    }

    d = {
      ...d,
      schemePlay: schemeValue.value,
      matchIds: comp.value.reduce(
        (prev, { matchId }, index) =>
          index === comp.value.length - 1 ? `${prev}${matchId}` : `${prev}${matchId},`,
        '',
      ),
    }

    // 赛事预测结果信息(包括主玩法与附赠玩法的预测结果信息)，草稿的时候可以不填写
    if (!isEmpty(matchScheme)) d = { ...d, matchScheme: JSON.stringify(matchScheme) }
  }

  // 处理价格
  let p: null | number | string = null
  if (price.value) {
    p = price.value.toString()
    p = (p as string).includes('.') ? parseFloat(p as string) : parseInt(p as string)
  }

  d = {
    ...d,
    ...pickBy(article.value),
    draft,
    refundType: refundType.value, // 不中即退
    autoReplacement: autoReplacement.value, // 可补单
    price: p,
    privilegeIds: selectedTaocan.value.join(','),
    initBuyCount: initBuyCount.value,
    // matchIds: comp.value.reduce(
    //   (prev, { matchId }, index) =>
    //     index === comp.value.length - 1 ? `${prev}${matchId}` : `${prev}${matchId},`,
    //   '',
    // ),
  }

  uni.showLoading({ title: '提交中' })

  // 提交数据
  publishArticle(d).then((aid) => {
    uni.hideLoading()
    if (draft) {
      uni.showToast({ title: '保存成功', icon: 'success' })
    } else {
      uni.showToast({ title: '提交成功', icon: 'success' })
      uni.redirectTo({ url: `/pages/article/setting/index?id=${aid}` })
    }
  })
}

/* 发布 */
function publish() {
  submit()
}

/* 保存为草稿 */
async function saveAsDraft() {
  await submit(1)

  // uni.reLaunch({ url: '/pages/article/relaese/index' })
  window.location.reload()
  /* 等待100毫秒 */
  // await wait(100)
  // 新增
  // const [d, s] = await Promise.all([getArticleDraft(), getGameplay()])

  /* if (!isEmpty(s)) {
    schemes.value = s.map(({ id, dataType, multiple, name, play, session, bundled }) => ({
      id,
      name,
      dataType,
      multiple,
      label: name,
      session,
      play,
      value: id,
      bundled,
    }))

    if (d) {
      // 如果有草稿
      let {
        id,
        title,
        intro,
        freeContents,
        contents,
        autoReplacement,
        refundType,
        schemePlay,
        matchScheme,
        price: p,
      } = d
      title = title || ''
      intro = intro || ''
      freeContents = freeContents || ''
      contents = contents || ''

      data.value = { ...data.value, schemePlay, id }
      article.value = { title, intro, freeContents, contents }
      resume()

      if (!autoReplacement && !refundType) {
        preferentialValue.value = 0
      } else if (autoReplacement === 1) {
        preferentialValue.value = 1
      } else if (refundType === 1) {
        preferentialValue.value = 2
      }

      if (!isNil(p)) price.value = p.toString()

      if (!ARTICLE_PRICE_TEMPLATE.includes(price.value as string)) showCustomizePrice.value = true

      if (schemePlay) {
        await initData(schemePlay, matchScheme ? JSON.parse(matchScheme) : null)
      }

      const dataType = s.find(({ id }) => id === schemePlay).dataType
      const m = await getMatchListInfo(dataType)
      matchs.value = m.map((e) => ({ ...e, id: e.matchId }))
    } else {
      // 如果没有草稿
      const [first] = s
      gamePlayType.value = first.multiple === 1 ? 'radio' : 'checkbox'
      schemeValue.value = first.id
      matchLimit.value = first.session ? first.session : null
      const [g, m] = await Promise.all([
        getPlayMethodById(first.play),
        getMatchListInfo(first.dataType),
      ])

      data.value = { ...data.value, schemePlay: first.id }
      gamePlay.value = g.map((e) => ({ ...e, label: e.name, value: e.id }))
      matchs.value = m.map((e) => ({ ...e, id: e.matchId }))
      gamePlayValue.value = first.multiple ? undefined : [] // 重置比赛玩法的值
    }
  } */
}

async function initData(sv: number, ms: any) {
  const selectedScheme = schemes.value.find(({ id }) => id === sv)
  if (!selectedScheme) return
  // 初始化方案玩法
  schemeValue.value = sv
  gamePlayType.value = selectedScheme.multiple === 1 ? 'radio' : 'checkbox'
  // 赛事场次限制
  matchLimit.value = selectedScheme.session ? selectedScheme.session : null
  const scheme = schemes.value.find(({ id }) => id === sv)
  // 获取方案对应下的玩法和赛事列表 并初始化

  if (ms && !isEmpty(ms)) {
    const matchIds = ms.map(({ matchId }) => matchId).join(',')
    const [g, o] = await Promise.all([
      getPlayMethodById(selectedScheme.play),
      getMatchPlayOdds(matchIds, scheme.dataType),
    ])
    gamePlay.value = g.map((e) => ({ ...e, label: e.name, value: e.id }))

    let mrs: IMatchSchemeItem[] = []
    let cs: (Omit<IMatchInfoItem, 'comp'> & { id: number })[] = []
    ms.forEach((e) => {
      let mmatchPlays: IMatchResult[] = []
      let bmatchPlays: IMatchResult[] = []
      let main: number[] | number | null = selectedScheme.multiple === 1 ? null : []
      let bonus: number[] | number | null = selectedScheme.multiple === 1 ? null : []

      e.matchPlays.forEach((i) => {
        if (i.type === 0) {
          main = isArray(main) ? [...main, i.playId] : i.playId
          let result = i.result ? i.result.split(',') : []
          result = [
            PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW,
            PLAY_TYPE.WIN_LOSE_DRAW,
            PLAY_TYPE.JQ,
          ].includes(i.playId)
            ? result.map((e) => parseInt(e))
            : result

          if ([PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, PLAY_TYPE.RQ, PLAY_TYPE.DXQ].includes(i.playId)) {
            // 兼容旧数据
            result = i.result.length === 1 ? result : result.slice(1)
          }

          // if (i.playId === PLAY_TYPE.BQC) {
          //   console.log('半全场: ', result)
          // }

          mmatchPlays.push({
            ...i,
            result,
          })
        } else {
          bonus = isArray(bonus) ? [...bonus, i.playId] : i.playId
          let result = i.result ? i.result.split(',') : []
          result = [
            PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW,
            PLAY_TYPE.WIN_LOSE_DRAW,
            PLAY_TYPE.JQ,
          ].includes(i.playId)
            ? result.map((e) => parseInt(e))
            : result

          if ([PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, PLAY_TYPE.RQ, PLAY_TYPE.DXQ].includes(i.playId)) {
            // 兼容旧数据
            result = i.result.length === 1 ? result : result.slice(1)
          }

          bmatchPlays.push({
            ...i,
            result,
          })
        }
      })

      mrs.push({
        ...e,
        odds: o.find(({ matchId }) => e.matchId === matchId),
        main,
        bonus,
        gamePlayOptions: gamePlay.value.map((e) => ({
          ...e,
          label: e.name,
          value: e.id,
          // disabled: isArray(main) ? main.includes(e.id) : main === e.id,
        })),
        bonusGamePlayOptions: gamePlay.value.map((e) => ({
          ...e,
          label: e.name,
          value: e.id,
          // disabled: isArray(bonus) ? bonus.includes(e.id) : bonus === e.id,
        })),
        mmatchPlays,
        bmatchPlays,
      })

      cs.push({
        id: e.matchId,
        matchId: e.matchId,
        homeName: e.homeName,
        awayName: e.awayName,
        roundName: e.roundName,
        matchTime: e.matchTime,
        issueNum: e.issueNum,
      })
    })

    matchResults.value = mrs
    comp.value = cs

    // 初始化主玩法
    // 初始化附赠玩法
  } else {
    const g = await getPlayMethodById(selectedScheme.play)
    const gp = g.map((e) => ({ ...e, label: e.name, value: e.id }))
    gamePlay.value = gp
  }
}

const editorReady = ref(false)

const freeEditorReady = ref(false)

const { resume, pause } = useIntervalFn(setContent, 500)

function setContent() {
  const contents = article.value.contents
  if (!contents) pause()
  if (editorReady.value) {
    editorRef.value.setOtherContent(contents)
    pause()
  }
}

const freeInterval = useIntervalFn(setFreeContent, 500)

function setFreeContent() {
  const contents = article.value.freeContents
  if (!contents) pause()
  if (editorReady.value) {
    freeEditorRef.value.setOtherContent(contents)
    freeInterval.pause()
  }
}

// 跳转到创建套餐页面
function adthorAddPackage() {
  uni.navigateTo({
    url: '/pages/index/adthorAddPackage',
  })
}

onUnmounted(() => {
  unWatch()
  pause()
  freeInterval.pause()
})

onShow(async () => {
  const [taocan] = await Promise.all([getPublishPrivilegeList()])
  if (!isEmpty(taocan)) {
    taocanList.value = taocan as any
  }
})
onLoad(async ({ id }) => {
  uni.showLoading()
  if (id) {
    // 编辑
    const [d, s, taocan] = await Promise.all([
      getArticleDetail(id),
      getGameplay(),
      getPublishPrivilegeList(id),
    ])

    let {
      title,
      intro,
      freeContents,
      contents,
      autoReplacement,
      refundType,
      schemePlay,
      matchScheme,
      price: p,
      canEdit: c,
      issue: i,
      privilegeIds,
    } = d

    canEdit.value = c

    if (privilegeIds) {
      selectedTaocan.value = privilegeIds.split(',').map((i) => +i)
    }

    title = title || ''
    intro = intro || ''
    freeContents = freeContents || ''
    contents = contents || ''
    data.value = { ...data.value, id }
    article.value = { title, intro, freeContents, contents, topBg: '' }
    resume()
    freeInterval.resume()

    schemes.value = s.map(({ id, dataType, multiple, name, play, session, bundled }) => ({
      id,
      name,
      dataType,
      multiple,
      label: name,
      session,
      play,
      value: id,
      bundled,
    }))

    taocanList.value = taocan as any

    if (!autoReplacement && !refundType) {
      preferentialValue.value = 0
    } else if (autoReplacement === 1) {
      preferentialValue.value = 1
    } else if (refundType === 1) {
      preferentialValue.value = 2
    }

    if (!isNil(p)) price.value = p.toString()

    if (ARTICLE_PRICE_TEMPLATE.indexOf(price.value.toString()) === -1) {
      showCustomizePrice.value = true
    }

    /* 如果选择的是14场或任九 */
    if (schemePlay < 3) {
      issue.value = i
    }

    if (schemePlay) {
      await initData(schemePlay, matchScheme ? JSON.parse(matchScheme) : null)
    } else {
      schemeMode.value = false
    }

    data.value = { ...data.value, id }

    const dt = s.find(({ id }) => id === schemePlay)
    const dataType = dt ? dt.dataType : 3
    const m = await getMatchListInfo(dataType)
    originMatchs.value = m.map((e) => ({ ...e, id: e.matchId }))
  } else if (!isEmpty(scheme)) {
    // 引用发布
    const s = await getGameplay()
    schemes.value = s.map(({ id, dataType, multiple, name, play, session, bundled }) => ({
      id,
      name,
      dataType,
      multiple,
      label: name,
      session,
      play,
      value: id,
      bundled,
    }))

    const [{ dataType }] = scheme
    const count = scheme.length > 2 ? null : scheme.length
    const selectedItem = s.find((item) => item.dataType === dataType && count === item.session)!
    await initData(selectedItem.id, scheme)
    const m = await getMatchListInfo(dataType)
    originMatchs.value = m.map((e) => ({ ...e, id: e.matchId }))
    clearScheme()
  } else {
    // 新增
    const [d, s, taocan] = await Promise.all([
      getArticleDraft(),
      getGameplay(),
      getPublishPrivilegeList(),
    ])

    if (!isEmpty(s)) {
      schemes.value = s.map(({ id, dataType, multiple, name, play, session, bundled }) => ({
        id,
        name,
        dataType,
        multiple,
        label: name,
        session,
        play,
        value: id,
        bundled,
      }))

      if (d) {
        // 如果有草稿
        let {
          id,
          title,
          intro,
          freeContents,
          contents,
          autoReplacement,
          refundType,
          schemePlay,
          matchScheme,
          price: p,
        } = d
        title = title || ''
        intro = intro || ''
        freeContents = freeContents || ''
        contents = contents || ''

        data.value = { ...data.value, schemePlay, id }
        article.value = { title, intro, freeContents, contents, topBg: '' }
        resume()
        freeInterval.resume()

        if (!autoReplacement && !refundType) {
          preferentialValue.value = 0
        } else if (autoReplacement === 1) {
          preferentialValue.value = 1
        } else if (refundType === 1) {
          preferentialValue.value = 2
        }

        if (!isNil(p)) price.value = p.toString()

        if (!ARTICLE_PRICE_TEMPLATE.includes(price.value as string)) showCustomizePrice.value = true

        if (schemePlay) {
          await initData(schemePlay, matchScheme ? JSON.parse(matchScheme) : null)
        }

        if (!schemePlay) {
          const play = s.find(({ id }) => id === SCHEME_TYPE.SINGLE)
          gamePlayType.value = play.multiple === 1 ? 'radio' : 'checkbox'
          schemeValue.value = play.id
          schemeMode.value = false
        }

        schemeMode.value = schemePlay ? true : false
        const dt = s.find(({ id }) => id === schemePlay)
        const dataType = dt ? dt.dataType : 3
        const m = await getMatchListInfo(dataType)
        originMatchs.value = m.map((e) => ({ ...e, id: e.matchId }))
      } else {
        // 如果没有草稿
        // const [first] = s
        const play = s.find(({ id }) => id === SCHEME_TYPE.SINGLE)
        gamePlayType.value = play.multiple === 1 ? 'radio' : 'checkbox'
        schemeValue.value = play.id
        matchLimit.value = play.session ? play.session : null
        const [g, m] = await Promise.all([
          getPlayMethodById(play.play),
          getMatchListInfo(play.dataType),
        ])

        data.value = { ...data.value, schemePlay: play.id }
        gamePlay.value = g.map((e) => ({ ...e, label: e.name, value: e.id }))
        originMatchs.value = m.map((e) => ({ ...e, id: e.matchId }))
      }
    }
    if (!isEmpty(taocan)) {
      taocanList.value = taocan as any
    }
  }

  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
:deep(.wd-cell__left) {
  display: none;
}

:deep(.wd-cell__value) {
  text-align: left;
}

:deep(.wd-button.is-primary) {
  background-color: #d1302e;
}

:deep(.wd-icon-check) {
  color: #d1302e;
}

:deep(.wd-input) {
  box-sizing: border-box;
  padding: 5rpx;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;

  &.is-not-empty {
    &::after {
      display: none;
    }
  }

  :deep(.wd-input__value) {
    padding: 0 10rpx;
  }
}

:deep(.wd-picker) {
  box-sizing: border-box;
  padding: 5rpx;
  text-align: left;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;
}

:deep(.wd-picker__cell) {
  background-color: none;
}

$raido-checked-color: rgb(209, 48, 46);

:deep(.is-checked) {
  .wd-radio__label {
    color: $raido-checked-color;
  }

  .wd-radio__shape {
    background-color: white !important;
    border-color: $raido-checked-color !important;

    &::before {
      background-color: $raido-checked-color !important;
    }
  }
}

$radio-disabled-color: rgb(217, 217, 217);
$radio-disabled-bg-color: rgb(243, 243, 243);

:deep(.is-checked.is-disabled) {
  .wd-radio__label {
    color: $radio-disabled-color;
  }

  .wd-radio__shape {
    background-color: $radio-disabled-bg-color !important;
    border-color: $radio-disabled-color !important;

    &::before {
      background-color: $radio-disabled-color !important;
    }
  }
}

.date-picker {
  padding: 20rpx;
  color: rgb(140, 140, 140);
  text-align: left;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;
}

.title-container {
  margin: 10rpx 0;
  font-weight: 600;
  text-align: left;

  .title {
    &::after {
      color: red;
      content: '*';
    }
  }
}

:deep(.ql-container .ql-editor) {
  padding-bottom: 80rpx;
  background-color: rgb(250, 250, 250);
}

:deep(.wd-cell__left) {
  display: none !important;
  padding-left: 0 !important;
  margin-right: 15rpx !important;
}

:deep(.wd-radio-group) {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
}

:deep(.wd-cell-group) {
  display: flex;
  flex-direction: column;
  height: 1750rpx !important;
}

:deep(.wd-input-number) {
  width: 100%;

  .wd-input-number__inner {
    width: 100%;
  }

  .wd-input-number__action {
    display: none;
  }

  .wd-input-number__input-border {
    border: none;
  }

  .wd-input-number__input {
    font-size: 14px;
  }
}

:deep(.match-checkbox) {
  display: flex;
  margin-bottom: 20rpx;

  .wd-checkbox__label {
    height: unset !important;
  }

  .wd-checkbox__btn-check {
    display: none;
  }
}

.rq-checkbox {
  --wot-checkbox-button-height: 100rpx;
  display: flex;
  height: 80rpx;
  margin-bottom: 20rpx;

  :deep(.wd-checkbox) {
    flex: 1;
    width: unset;
  }

  :deep(.wd-checkbox__txt) {
    max-width: unset !important;
  }
}

.dxq-checkbox {
  --wot-checkbox-button-bg: #fff;
  --wot-checkbox-margin: 0;
  --wot-checkbox-button-radius: 0;
  --wot-checkbox-button-font-size: 24rpx;
  display: flex;
  flex: 2;

  :deep(.wd-checkbox) {
    flex: 1;
    padding: 0 !important;
  }

  :deep(.wd-checkbox__label) {
    padding: 0 !important;
  }

  :deep(.wd-checkbox__txt) {
    max-width: unset !important;
  }

  :deep(.is-first-child) {
    border-left: 1rpx solid rgba(121, 121, 121, 0.1);
  }
}

.goumairenshu {
  :deep(.wd-input-number__inner) {
    .wd-input-number__input {
      .uni-input-wrapper {
        .uni-input-input {
          font-size: 14px;
          text-align: right;
        }
      }
    }
  }
}

.taocan_list {
  background: rgb(250, 250, 250);
  border-radius: 5px;

  :deep(.wd-checkbox-group) {
    background-color: transparent;
  }
}
</style>
