<route lang="json5">
{
  style: {
    // navigationStyle: 'custom',
    navigationBarTitleText: '发布方案',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <!-- <wd-navbar title="发布方案" left-arrow @click-left="handleClickLeft">
    <template #right>
      <view class="flex items-center gap-x-7rpx" @click="gotoReferenceRelease">
        <wd-icon name="translate-bold" color="#D1302E" size="30rpx" />
        <text class="text-24rpx">引用发布</text>
      </view>
    </template>
</wd-navbar> -->
  <view class="p-30rpx bg-#F4F8FA pb-80rpx">
    <!-- 文章设置选项 -->
    <view class="p-30rpx rounded-md bg-white">
      <wd-form ref="articleRef" :model="article">
        <!-- 标题 -->
        <view class="title-container">
          <text class="title">方案标题</text>
        </view>
        <wd-input :readonly="!canEdit" v-model="article.title" prop="title" placeholder="请输入方案标题，不超过20个汉字字符" clearable
          :maxlength="20" />
        <!-- 简介 -->
        <view class="title-container">
          <text>精彩简介</text>
        </view>
        <wd-input :readonly="!canEdit" v-model="article.intro" prop="intro" placeholder="请输入方案的精彩简介，不超过50个汉字字符"
          clearable :maxlength="50" />
        <!-- 免费内容 -->
        <view class="title-container">
          <text>免费内容</text>
          <!-- <text class="text-[red]">(当前状态不允许编辑)</text> -->
          <!-- <wd-textarea
            v-model="article.freeContents"
            prop="freeContents"
            placeholder="请输入方案的免费内容，不超过50个汉字字符"
            :maxlength="50"
            auto-height
            clearable
          /> -->
          <piaoyi-editor class="w-650rpx" ref="freeEditorRef" v-model="article.freeContents"
            @changes="changeFreeContent" @ready="freeEditorReady" :photoUrl="photoUrl" :maxlength="10000" />
        </view>
        <!-- 付费内容 -->
        <view class="title-container">
          <text>付费内容</text>
          <!-- <text class="text-[red]">(当前状态不允许编辑)</text> -->
          <piaoyi-editor class="w-650rpx" ref="editorRef" v-model="article.contents" @changes="changeContent"
            @ready="readyEditor" :photoUrl="photoUrl" :maxlength="10000" />
        </view>
      </wd-form>
    </view>
    <!-- 其他设置选项 -->
    <view class="mt-20rpx p-30rpx rounded-md bg-white">
      <!-- 新增二维码背景选择 -->
      <view class="flex items-center h-100rpx border-bottom" @click="showBgList = !showBgList">
        <text class="text-black text-opacity-90 text-28rpx">选择二维码背景></text>
      </view>

      <scroll-view :show-scrollbar="false" v-show="showBgList" scroll-x
        class="whitespace-nowrap py-20rpx gap-30rpx px-10rpx">
        <view v-for="(img, index) in bgList" :key="index" class="relative inline-block mx-10rpx">
          <image :src="img.url" class="w-200rpx h-200rpx rounded-lg"
            :class="{ 'border-6rpx border-solid border-#FF0000': article.topBg === img.url }"
            @click="article.topBg = img.url" @error="showNameMap[index] = true" />
          <view v-if="showNameMap[index]"
            class="absolute inset-0 bg-gray-100/80 flex items-center justify-center text-24rpx p-10rpx break-all">
            {{ img.name }}
          </view>
        </view>
      </scroll-view>
      <!-- 优惠策略 -->
      <view class="flex justify-between items-center h-100rpx border-bottom">
        <text class="shrink-0 text-black text-opacity-90 text-28rpx">优惠策略></text>
        <wd-select-picker type="radio" align-right :columns="PREFERENTIAL" v-model="preferentialValue" />
      </view>
      <!-- 同步至套餐 -->
      <view class="flex justify-between items-center h-100rpx pr-30rpx">
        <text class="shrink-0 text-black text-opacity-90 text-28rpx">同步至套餐></text>
        <wd-switch v-model="tbztc" disabled />
      </view>
      <view class="h-60rpx text-right border-bottom pr-30rpx" @click="adthorAddPackage" v-if="!taocanList.length">
        <text class="text-red font-size-[28rpx] pr-10rpx">暂无套餐,去创建</text>
        <wd-icon name="arrow-right" color="rgba(0,0,0,0.3)" />
      </view>
      <view class="pr-30rpx taocan_list">
        <wd-checkbox-group v-model="selectedTaocan" cell>
          <template v-for="i of taocanList" :key="i">
            <wd-checkbox :modelValue="i.id">{{ i.name }}</wd-checkbox>
          </template>
        </wd-checkbox-group>
      </view>
      <!-- 设置初始购买人数 -->
      <view class="flex justify-between items-center h-100rpx border-bottom">
        <text class="shrink-0 text-black text-opacity-90 text-28rpx">设置初始购买人数></text>
        <wd-input-number v-model="initBuyCount" :min="0" :max="99999" input-width="100%" allow-null
          placeholder="请输入初始购买人数" class="goumairenshu pr-30rpx" />
      </view>
    </view>
    <!-- 赛事预测 -->
    <!-- 设置售价 -->
    <view class="flex items-start gap-x-40rpx mt-30rpx mb-20rpx">
      <text class="text-28rpx text-black text-opacity-90">设置售价></text>
      <view class="flex flex-wrap gap-20rpx flex-1">
        <template v-for="p in ARTICLE_PRICE_TEMPLATE" :key="p">
          <text @click="selectPrice(p)" v-if="p === '0'"
            class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
            style="width: calc((100% - 40rpx) / 3)" :style="pStyle(p)">
            免费
          </text>
          <text @click="selectPrice(p)" v-else
            class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
            style="width: calc((100% - 40rpx) / 3)" :style="pStyle(p)">
            {{ p }}
          </text>
        </template>
        <text @click="toggleCustomizePrice"
          class="h-70rpx text-26rpx border-solid border-1rpx border-black border-opacity-10 text-center box-border leading-70rpx rounded-lg"
          style="width: calc((100% - 40rpx) / 3)">
          自定义
        </text>
      </view>
    </view>
    <template v-if="showCustomizePrice">
      <wd-input-number v-model="price" :precision="2" :min="0" :max="99999" input-width="100%" allow-null
        placeholder="请输入自定义价格" />
    </template>
    <!-- 发布 -->
    <view @click="publish"
      class="h-100rpx my-30rpx px-30rpx rounded-lg bg-#D1302E text-white text-32rpx text-center leading-100rpx">
      发布
    </view>
    <!-- 保存为草稿箱 -->
    <view class="flex justify-center items-center gap-x-10rpx" @click="saveAsDraft">
      <wd-icon name="evaluation" size="30rpx" color="rgba(0,0,0,0.5)" />
      <text class="text-28rpx text-black text-opacity-50 leading-40rpx">保存草稿箱</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  IGamePlayItem,
  IScheme,
  getArticleDraft,
  getGameplay,
  getPlayMethodById,
  publishArticle,
} from '@/api/author'
import { isArray, isEmpty, isNil, pickBy, groupBy, keys } from 'lodash-es'
import { ARTICLE_PRICE_TEMPLATE, PREFERENTIAL } from '@/utils/constant'
import { IMatchInfoItem, IMatchSchemeItem, getMatchListInfo, getMatchPlayOdds } from '@/api/match'
import { PLAY_TYPE, GAME_PLAY_TYPE, SCHEME_TYPE } from '@/utils/enum'
import { format, formatWeek } from '@/utils/format'
import { IMatchResult, getArticleDetail } from '@/api/article'
import { useIntervalFn } from '@vueuse/core'
import { IZhanji } from '@/api/zhanji'
import { getPublishPrivilegeList, getPrivilegePvLog, getPrivilegeStatistics } from '@/api/member'

const zhanjiRef = ref()
const photoUrl = import.meta.env.VITE_UPLOAD_BASEURL

const articleRef = ref()
const editorRef = ref()
const freeEditorRef = ref()
const article = ref({
  title: '',
  intro: '',
  freeContents: '',
  contents: '',
  topBg: '',
})
const taocanList = ref([]) // 套餐列表

const showBgList = ref(false)
const showNameMap = ref({})
const bgList = ref([
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg.png', name: '默认背景' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg1.png', name: '背景1' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg2.png', name: '背景2' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg3.png', name: '背景3' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg4.png', name: '背景4' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg5.png', name: '背景5' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg6.png', name: '背景6' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg7.png', name: '背景7' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg8.png', name: '背景8' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg9.png', name: '背景9' },
])

interface IData {
  id?: number
  schemePlay: number // 方案玩法schemeValue
  /*
   * 下面的这些都是垃圾字段，是为了兼容以前的版本数据
   *  后端调整后可以删除，对本版本没有任何影响
   */
  consumeStatus: number
  consumeMinNum: number
  consumeMaxNum: number
  consumeMinAmount: number
  consumeMaxAmount: number
}

const canEdit = ref(1) // 是否可以编辑(0 不能 1 可以)

/* 结果预测 */
const data = ref<IData>({
  consumeStatus: 0,
  consumeMinNum: 0,
  consumeMaxNum: 0,
  consumeMinAmount: 0,
  consumeMaxAmount: 0,
} as IData)

const originMatchs = ref<(IMatchInfoItem & { id: number })[]>([])
const comp = ref<(Omit<IMatchInfoItem, 'comp'> & { id: number })[]>([])
/* 选中的期数 */
const issue = ref('')

/* 优惠策略 */
const preferentialValue = ref(0)

/* 设置初始购买人数 */
const initBuyCount = ref(0)

const selectedTaocan = ref([])
const checked = ref(false)
const value = ref<string[]>([])
const checkBox1 = ref()
const checkBox2 = ref()
const checkBox3 = ref()
/* 同步至套餐 */
const tbztc = computed(() => {
  console.log('selectedTaocan.value.length: ', selectedTaocan.value.length)
  if (selectedTaocan.value.length > 0) {
    return true
  } else {
    return false
  }
})

function handleCheck1() {
  checkBox1.value && checkBox1.value.toggle()
}

function handleCheck2() {
  checkBox2.value && checkBox2.value.toggle()
}
function handleCheck3() {
  checkBox3.value && checkBox3.value.toggle()
}

function noop() { }

/* 不中即退 */
const refundType = computed(() => (preferentialValue.value === 2 ? 1 : 0))

/* 可补单 */
const autoReplacement = computed(() => (preferentialValue.value === 1 ? 1 : 0))

const showCustomizePrice = ref(false)
const price = ref<string | number>('')

function changeContent(v: { html: string }) {
  article.value.contents = v.html
}

const pStyle = computed(() => {
  return (p: string) => {
    if (showCustomizePrice.value) return 'background: rgba(0,0,0,0.04);opacity: 0.4'

    if (p === price.value)
      return 'background: rgba(209, 48, 46, 0.10);border: 1px solid #D1302E; color: #D1302E'

    return ''
  }
})

/* 选择模板价格 */
function selectPrice(p: string) {
  if (showCustomizePrice.value || p === price.value) return
  price.value = p
}

/* 切换自定义输入价格或使用模板价格 */
function toggleCustomizePrice() {
  price.value = ''
  showCustomizePrice.value = !showCustomizePrice.value
}

const readyEditor = () => {
  editorReady.value = true
}

function submit(draft: 0 | 1 = 0) {
  if (!article.value.title) {
    // 如果草稿没有标题
    uni.showToast({
      title: '文章标题不能为空',
      icon: 'none',
    })
    return
  }

  if (!draft) {
    /* 表单验证(如果不是草稿则要进行表单验证) */
    const { title, contents, freeContents } = article.value
    // 验证文章标题与简介是否有写
    if (!title) {
      uni.showToast({
        title: '文章标题不能为空',
        icon: 'none',
      })
      return
    }

    // 文章价格不能为空
    if (!price.value) {
      uni.showToast({
        title: '请输入文章价格',
        icon: 'none',
      })
      return
    }

    if (price.value && !contents) {
      uni.showToast({
        title: '请填写付费内容',
        icon: 'none',
      })
      return
    }

    if (!freeContents && !contents) {
      uni.showToast({
        title: '免费内容与付费内容不能为空',
        icon: 'none',
      })
      return
    }
  }

  if (!article.value.title) {
    // 如果草稿没有标题
    uni.showToast({
      title: '文章标题不能为空',
      icon: 'none',
    })
    return
  }

  // 组装数据
  let d: any = data.value

  // 处理价格
  let p: null | number | string = null
  if (price.value) {
    p = price.value.toString()
    p = (p as string).includes('.') ? parseFloat(p as string) : parseInt(p as string)
  }

  d = { ...d, price: p }

  d = {
    ...d,
    ...pickBy(article.value),
    draft,
    refundType: refundType.value, // 不中即退
    autoReplacement: autoReplacement.value, // 可补单
    price: p,
    privilegeIds: selectedTaocan.value.join(','),
    initBuyCount: initBuyCount.value,
  }

  uni.showLoading({ title: '提交中' })

  // 提交数据
  publishArticle(d).then((aid) => {
    uni.hideLoading()
    if (draft) {
      uni.showToast({ title: '保存成功', icon: 'success' })
    } else {
      uni.showToast({ title: '提交成功', icon: 'success' })
      uni.redirectTo({ url: `/pages/article/setting/index?id=${aid}` })
    }
  })
}

/* 发布 */
function publish() {
  submit()
}

const sleep = (delay) => new Promise((resolve) => setTimeout(resolve, delay))

/* 保存为草稿 */
async function saveAsDraft() {
  await submit(1)

  await sleep(500)

  window.location.reload()
}

const editorReady = ref(false)

const freeEditorReady = ref(false)

const { resume, pause } = useIntervalFn(setContent, 500)

function setContent() {
  const contents = article.value.contents
  if (!contents) pause()
  if (editorReady.value) {
    editorRef.value.setOtherContent(contents)
    pause()
  }
}

const freeInterval = useIntervalFn(setFreeContent, 500)

function setFreeContent() {
  const contents = article.value.freeContents
  if (!contents) pause()
  if (editorReady.value) {
    freeEditorRef.value.setOtherContent(contents)
    freeInterval.pause()
  }
}

function changeFreeContent(v: { html: string }) {
  article.value.freeContents = v.html
}

// 跳转到创建套餐页面
function adthorAddPackage() {
  uni.navigateTo({
    url: '/pages/index/adthorAddPackage',
  })
}

onUnmounted(() => {
  pause()
  freeInterval.pause()
})

onLoad(async ({ id }) => {
  console.log('on load')
  uni.showLoading()

  if (id) {
    // 编辑
    const [d, s, taocan] = await Promise.all([
      getArticleDetail(id),
      getGameplay(),
      getPublishPrivilegeList(id),
    ])

    let {
      title,
      intro,
      freeContents,
      contents,
      autoReplacement,
      refundType,
      price: p,
      canEdit: c,
      issue: i,
    } = d

    canEdit.value = c

    title = title || ''
    intro = intro || ''
    freeContents = freeContents || ''
    contents = contents || ''
    data.value = { ...data.value, id }
    article.value = { title, intro, freeContents, contents, topBg: '' }
    resume()
    freeInterval.resume()

    taocanList.value = taocan

    if (!autoReplacement && !refundType) {
      preferentialValue.value = 0
    } else if (autoReplacement === 1) {
      preferentialValue.value = 1
    } else if (refundType === 1) {
      preferentialValue.value = 2
    }

    if (!isNil(p)) price.value = p.toString()

    if (ARTICLE_PRICE_TEMPLATE.indexOf(price.value.toString()) === -1) {
      showCustomizePrice.value = true
    }

    data.value = { ...data.value, id }
  } else {
    // 新增
    const [taocan] = await Promise.all([getPublishPrivilegeList()])
    if (!isEmpty(taocan)) {
      taocanList.value = taocan
    }
  }

  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
:deep(.wd-cell__left) {
  display: none;
}

:deep(.wd-cell__value) {
  text-align: left;
}

:deep(.wd-button.is-primary) {
  background-color: #d1302e;
}

:deep(.wd-icon-check) {
  color: #d1302e;
}

:deep(.wd-input) {
  box-sizing: border-box;
  padding: 5rpx;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;

  &.is-not-empty {
    &::after {
      display: none;
    }
  }

  :deep(.wd-input__value) {
    padding: 0 10rpx;
  }
}

:deep(.wd-picker) {
  box-sizing: border-box;
  padding: 5rpx;
  text-align: left;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;
}

:deep(.wd-picker__cell) {
  background-color: none;
}

$raido-checked-color: rgb(209, 48, 46);

:deep(.is-checked) {
  .wd-radio__label {
    color: $raido-checked-color;
  }

  .wd-radio__shape {
    background-color: white !important;
    border-color: $raido-checked-color !important;

    &::before {
      background-color: $raido-checked-color !important;
    }
  }
}

$radio-disabled-color: rgb(217, 217, 217);
$radio-disabled-bg-color: rgb(243, 243, 243);

:deep(.is-checked.is-disabled) {
  .wd-radio__label {
    color: $radio-disabled-color;
  }

  .wd-radio__shape {
    background-color: $radio-disabled-bg-color !important;
    border-color: $radio-disabled-color !important;

    &::before {
      background-color: $radio-disabled-color !important;
    }
  }
}

.date-picker {
  padding: 20rpx;
  color: rgb(140, 140, 140);
  text-align: left;
  background-color: rgb(250, 250, 250);
  border: 1px solid rgb(241, 241, 241);
  border-radius: 10rpx;
}

.title-container {
  margin: 10rpx 0;
  font-weight: 600;
  text-align: left;

  .title {
    &::after {
      color: red;
      content: '*';
    }
  }
}

:deep(.ql-container .ql-editor) {
  padding-bottom: 80rpx;
  background-color: rgb(250, 250, 250);
}

:deep(.wd-cell__left) {
  display: none !important;
  padding-left: 0 !important;
  margin-right: 15rpx !important;
}

:deep(.wd-radio-group) {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
}

:deep(.wd-cell-group) {
  display: flex;
  flex-direction: column;
  height: 1750rpx !important;
}

:deep(.wd-input-number) {
  width: 100%;

  .wd-input-number__inner {
    width: 100%;
  }

  .wd-input-number__action {
    display: none;
  }

  .wd-input-number__input-border {
    border: none;
  }
}

:deep(.match-checkbox) {
  display: flex;
  margin-bottom: 20rpx;

  .wd-checkbox__label {
    height: unset !important;
  }

  .wd-checkbox__btn-check {
    display: none;
  }
}

.rq-checkbox {
  --wot-checkbox-button-height: 100rpx;
  display: flex;
  height: 80rpx;
  margin-bottom: 20rpx;

  :deep(.wd-checkbox) {
    flex: 1;
    width: unset;
  }

  :deep(.wd-checkbox__txt) {
    max-width: unset !important;
  }
}

.dxq-checkbox {
  --wot-checkbox-button-bg: #fff;
  --wot-checkbox-margin: 0;
  --wot-checkbox-button-radius: 0;
  --wot-checkbox-button-font-size: 24rpx;
  display: flex;
  flex: 2;

  :deep(.wd-checkbox) {
    flex: 1;
    padding: 0 !important;
  }

  :deep(.wd-checkbox__label) {
    padding: 0 !important;
  }

  :deep(.wd-checkbox__txt) {
    max-width: unset !important;
  }

  :deep(.is-first-child) {
    border-left: 1rpx solid rgba(121, 121, 121, 0.1);
  }
}
</style>
