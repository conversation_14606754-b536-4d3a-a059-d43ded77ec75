<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    // navigationBarTitleText: '赛事列表',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="pb-40rpx">
    <wd-navbar :title="navTitle" left-arrow @click-left="handleClickLeft"></wd-navbar>
    <template v-if="!isDetail">
      <view>
        <wd-search
          placeholder="搜索球队名称"
          hide-cancel
          v-model="formData.teamName"
          custom-class="search"
          @change="debounceSearch"
        />
      </view>
      <wd-datetime-picker
        v-model="formData.date"
        custom-class="date"
        type="date"
        use-default-slot
        :min-date="Date.now()"
        @confirm="search"
      >
        <view
          class="flex justify-between items-center h-30px px-30rpx mx-30rpx border-normal rounded-12rpx"
        >
          <template v-if="isEmpty(formData.date)">
            <text class="text-#bfbfbf">请选择比赛日期</text>
          </template>
          <template v-else>
            <text>{{ formatDate(formData.date[0]) }}</text>
            <text class="px-20rpx">至</text>
            <text class="mr-auto">{{ formatDate(formData.date[1]) }}</text>
            <wd-icon name="clear" @click.stop="clearDate" />
          </template>
        </view>
      </wd-datetime-picker>
    </template>
    <scheme-list ref="schemeRef" :list="data" @detail="changeDetailMode" />
  </view>
</template>

<script setup lang="ts">
import { debounce, isEmpty, pickBy } from 'lodash-es'
import { IBroomerItem, getBroomerMatchSchemeByPage } from '@/api/match'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
import { formatDate } from '@/utils/format'
import schemeList from './components/schemeList.vue'
import { useSchemeStore } from '@/store/scheme'

const pageNo = ref(0)
const totalPage = ref(0)
const schemeRef = ref()
const isDetail = ref(false)
const navTitle = ref('赛事列表')

const { clearScheme } = useSchemeStore()
const data = ref<IBroomerItem[]>([])

const formData = ref({
  teamName: '',
  date: [],
})

async function getBroomerData(loadMore = false) {
  const { teamName, date } = formData.value
  let params: Partial<{ teamName: string; startDate: string; endDate: string }> = { teamName }
  if (!isEmpty(date)) {
    const startDate = formatDate(date[0])
    const endDate = formatDate(date[1])
    params = { ...params, startDate, endDate }
  }

  const { list, total } = await getBroomerMatchSchemeByPage(pageNo.value + 1, pickBy(params))
  totalPage.value = Math.ceil(total / DEFAULT_PAGE_SIZE)
  if (pageNo.value + 1 <= totalPage.value) pageNo.value = pageNo.value + 1
  if (loadMore) {
    data.value = [
      ...data.value,
      ...list.map(({ matchScheme, ...rest }) => ({
        ...rest,
        matchScheme: JSON.parse(matchScheme),
      })),
    ]
  } else {
    data.value = list.map(({ matchScheme, ...rest }) => ({
      ...rest,
      matchScheme: JSON.parse(matchScheme),
    }))
  }
}

function search() {
  pageNo.value = 0
  getBroomerData()
}

const debounceSearch = debounce(search, 600)

function clearDate() {
  formData.value.date = []
  search()
}

function changeDetailMode(detail: boolean, matchName: string) {
  isDetail.value = detail
  navTitle.value = detail ? matchName : '赛事列表'
}

function handleClickLeft() {
  if (isDetail.value) {
    schemeRef.value.exitDetailModel()
    changeDetailMode(false, '')
  } else {
    clearScheme()
    uni.navigateTo({ url: '/pages/article/relaese/index' })
  }
}

onReachBottom(() => {
  getBroomerData(true)
})

onLoad(() => {
  getBroomerData()
})
</script>

<style lang="scss" scoped>
.search {
  --wot-search-input-radius: 12rpx;
  --wot-search-side-padding: 30rpx;
}
</style>
