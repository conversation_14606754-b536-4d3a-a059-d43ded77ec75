<route lang="json5">
{
  style: {
    navigationBarTitleText: '方案设置',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view>
    <!-- <view class="pt-30rpx p-x-30rpx flex justify-between" v-if="article">
      <view class="flex flex-col justify-between mr-50rpx">
        <text class="text-32rpx text-black text-opacity-90 leading-40rpx">{{ article.title }}</text>
        <text class="leading-34rpx line-clamp-3 intro">
          {{ `简介：${article.intro.repeat(10)}` }}
        </text>
        <text
          class="w-270rpx h-50rpx mb-12rpx text-#D1302E text-26rpx bg-#D1302E bg-opacity-10 text-center leading-50rpx rounded-md"
        >
          长按识别二维码查看
        </text>
      </view>
      <image :src="article.shareQrCode" class="w-240rpx h-240rpx" />
    </view> -->
    <view
      class="top text-white relative"
      :style="{
        backgroundImage: article?.topBg?.trim()
          ? `url(${article.topBg})`
          : 'url(https://sacdn.850g.com/football/static/scheme/top_bg.png)',
      }"
    >
      <text class="text-40rpx">{{ article && article.title }}</text>
      <text class="leading-40rpx intro">{{ article.intro }}</text>
      <image :src="article.shareQrCode" class="w-300rpx h-300rpx rounded-xl" />
      <view class="flex justify-center mt-80rpx">
        <view class="flex items-center">
          <image :src="article.authorAvatar" class="w-60rpx h-60rpx rounded-full" />
          <text class="ml-20rpx text-32rpx">{{ article.authorName }}</text>
        </view>
        <view class="flex items-center ml-80rpx">
          <image
            src="https://sacdn.850g.com/football/static/gold2.svg"
            class="w-60rpx h-60rpx rounded-full"
          />
          <text class="ml-20rpx text-32rpx" v-if="article.price">{{ article.price }}</text>
          <text class="ml-20rpx text-32rpx" v-else>{{ '免费' }}</text>
        </view>
      </view>
      <view
        @click="handleShowBgChange()"
        class="changebg absolute bg-opacity-20 bottom-20rpx right-31rpx"
      >
        <image
          class="w-19rpx h-17rpx mr-5rpx"
          src="@/static/images/changebg.png"
          mode="scaleToFill"
        />
        <text class="text-20rpx">更换背景</text>
      </view>
    </view>
    <!-- 编辑与查看详情 -->
    <view class="flex justify-between mt-30rpx m-x-30rpx pb-30rpx opt-group">
      <wd-button class="edit-opt" :disabled="!canEdit" @click="goEdit">编辑</wd-button>
      <wd-button class="detail-opt" @click="gotoDetail">查看详情</wd-button>
    </view>
    <!-- 设置推送，公开，追加 -->
    <view class="setting">
      <!-- 左 -->
      <view class="flex flex-col gap-y-30rpx">
        <view class="flex items-center gap-x-5rpx" @click="pushArticle">
          <img class="w-50rpx h-50rpx" src="/static/setting/push.png" alt="" />
          <text class="text-#D1302E text-30rpx">设置推送</text>
        </view>
        <view class="flex items-center gap-x-5rpx" @click="goAddPaid">
          <img class="w-50rpx h-50rpx" src="/static/setting/append.png" alt="" />
          <text class="text-#D1302E text-30rpx">追加付费内容</text>
        </view>
        <view class="flex items-center gap-x-5rpx" @click="goBuyUserInfo(0)">
          <img class="w-35rpx h-35rpx mr-1 ml-1" src="/static/setting/watch.png" alt="" />
          <text class="text-#D1302E text-30rpx">观看人数</text>
        </view>
        <view class="flex items-center gap-x-5rpx" @click="changeStatus(article.status)">
          <img class="w-40rpx h-40rpx mr-1 ml-0.5" src="/static/setting/down.png" alt="" />
          <text class="text-#D1302E text-30rpx">
            {{ article.status == 1 ? '下架' : article.status == 2 ? '已封禁' : '上架' }}
          </text>
        </view>
        <view class="flex items-center gap-x-5rpx" @click="goShare()">
          <img class="w-35rpx h-35rpx mr-1 ml-1" src="/static/setting/fx.png" alt="" />
          <text class="text-#D1302E text-30rpx">分享文章</text>
        </view>
      </view>
      <!-- 右 -->
      <view class="flex flex-col gap-y-30rpx">
        <view class="flex items-center gap-x-5rpx" @click="publishArticle">
          <img class="w-50rpx h-50rpx" src="/static/setting/eye.png" alt="" />
          <text class="text-#D1302E text-30rpx">设置公开</text>
        </view>
        <view class="flex items-center gap-x-5rpx" @click="goAddFree">
          <img class="w-50rpx h-50rpx" src="/static/setting/append.png" alt="" />
          <text class="text-#D1302E text-30rpx">追加免费内容</text>
        </view>
        <view class="flex items-center gap-x-5rpx" @click="goBuyUserInfo(1)">
          <img class="w-40rpx h-40rpx mr-1 ml-0.5" src="/static/setting/buy.png" alt="" />
          <text class="text-#D1302E text-30rpx">购买人数</text>
        </view>
        <view class="flex items-center gap-x-5rpx" @click="changeTop(article.top)">
          <img class="w-40rpx h-40rpx mr-1 ml-0.5" src="/static/setting/top.png" alt="" />
          <text class="text-#D1302E text-30rpx">
            {{ article.top == 1 ? '取消置顶' : '置顶' }}
          </text>
        </view>
        <view class="flex items-center gap-x-5rpx" @click="deleteShow = true">
          <img class="w-35rpx h-35rpx mr-1 ml-1" src="/static/setting/delete.png" alt="" />
          <text class="text-#D1302E text-30rpx">删除方案</text>
        </view>
      </view>
    </view>

    <wd-popup
      v-model="deleteShow"
      custom-style="border-radius:16rpx;width: 90%;padding:30rpx;box-sizing: border-box;"
    >
      <view class="flex items-center gap-x-10rpx text-30rpx">
        <image class="w-34rpx h-34rpx" src="/static/setting/tips.png" mode="scaleToFill" />
        <text>是否删除方案</text>
      </view>
      <view class="mx-30rpx my-15rpx text-#999999 text-28rpx">确认要永久删除该方案吗？</view>
      <view class="flex justify-end gap-10rpx mt-60rpx">
        <wd-button plain hairline @click="deleteShow = false">取消</wd-button>
        <wd-button type="error" @click="handleDeleteArticle">确定</wd-button>
      </view>
    </wd-popup>

    <!-- 弹框 -->
    <wd-popup v-model="visible" position="bottom">
      <view class="pt-30rpx pb-100rpx p-x-30rpx">
        <!-- 请选择公开区间 -->
        <template v-if="modalType === MODAL_TYPE.PUBLISH">
          <view class="mb-10rpx text-32rpx text-black text-opacity-90 text-center">
            请选择公开区间
          </view>
          <wd-form
            ref="publishFormRef"
            :model="publishData"
            :rules="publishData.consumeStatus == 1 ? publishFormRules : null"
          >
            <view class="form-label required">是否消费</view>
            <wd-radio-group
              prop="consumeStatus"
              shape="dot"
              v-model="publishData.consumeStatus"
              inline
            >
              <wd-radio :value="0">全部</wd-radio>
              <wd-radio :value="1">是</wd-radio>
              <wd-radio :value="-1">否</wd-radio>
            </wd-radio-group>
            <template v-if="publishData.consumeStatus === 1">
              <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                最低消费次数（选填）:
              </view>
              <view class="flex items-center">
                <view class="flex-1">
                  <wd-input
                    prop="consumeMinNum"
                    class="form-input"
                    placeholder="请输入次数"
                    v-model="publishData.consumeMinNum"
                    :maxlength="7"
                  />
                </view>
                <!-- <text class="m-x-24rpx w-16rpx h-2rpx bg-black bg-opacity-50"></text>
                <view class="flex-1">
                  <wd-input
                    prop="consumeMaxNum"
                    class="form-input"
                    placeholder="请输入结束次数"
                    v-model="publishData.consumeMaxNum"
                  />
                </view> -->
              </view>
              <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                最低消费金额（选填）:
              </view>
              <view class="flex items-center">
                <view class="flex-1">
                  <wd-input
                    prop="consumeMinAmount"
                    custom-class="form-input"
                    placeholder="请输入起始金额"
                    v-model="publishData.consumeMinAmount"
                    :maxlength="7"
                  />
                </view>
                <!-- <text class="m-x-24rpx w-16rpx h-2rpx bg-black bg-opacity-50"></text>
                <view class="flex-1">
                  <wd-input
                    prop="consumeMaxAmount"
                    class="form-input"
                    placeholder="请输入结束金额"
                    v-model="publishData.consumeMaxAmount"
                  />
                </view> -->
              </view>
            </template>
            <view class="submit">
              <wd-button type="primary" custom-class="submit-btn" @click="handlePublicSubmit">
                公开
              </wd-button>
            </view>
          </wd-form>
        </template>
        <!-- 请选择推送区间 -->
        <template v-else>
          <scroll-view style="max-height: 1000rpx" scroll-y :show-scrollbar="false">
            <view class="mb-10rpx text-32rpx text-black text-opacity-90 text-center">
              请选择推送区间
            </view>
            <wd-form ref="pushFormRef" :model="pushData">
              <view
                v-for="(item, index) in pushData"
                :key="index"
                style="margin: 20rpx 0; border-bottom: 1px solid #eee"
              >
                <view v-if="pickTemplate">
                  <view class="flex justify-start items-center">
                    <view class="form-label required">选择模版</view>
                    <text
                      v-if="index == pushData.length - 1"
                      class="color-[rgb(255,0,0)] font-size-24rpx m-l-20rpx push-btn"
                      @click="addPushTemplate(index)"
                    >
                      添加
                    </text>
                    <text
                      v-if="index == pushData.length - 1 && index != 0"
                      class="color-[rgb(255,0,0)] font-size-24rpx m-l-20rpx push-btn"
                      @click="removePushTemplate(index)"
                    >
                      删除
                    </text>
                  </view>
                  <wd-select-picker
                    :columns="pushTemplateList"
                    value-key="id"
                    label-key="name"
                    v-model="item.templateId"
                    type="radio"
                  />
                </view>
                <view class="form-label required">推送条件</view>
                <wd-radio-group
                  :prop="`consumeStatus`"
                  shape="dot"
                  v-model="item.consumeStatus"
                  inline
                >
                  <wd-radio :value="0">全部</wd-radio>
                  <wd-radio :value="1">消费条件</wd-radio>
                  <wd-radio :value="2">已购本方案</wd-radio>
                </wd-radio-group>
                <template v-if="item.consumeStatus === 1">
                  <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                    最低消费次数（选填）:
                  </view>
                  <view class="flex items-center">
                    <view class="flex-1">
                      <wd-input
                        prop="consumeMinNum"
                        class="form-input"
                        placeholder="请输入起始次数"
                        v-model="item.consumeMinNum"
                      />
                    </view>
                    <!-- <text class="m-x-24rpx w-16rpx h-2rpx bg-black bg-opacity-50"></text>
                <view class="flex-1">
                  <wd-input
                    prop="consumeMaxNum"
                    class="form-input"
                    placeholder="请输入结束次数"
                    v-model="pushData.consumeMaxNum"
                  />
                </view> -->
                  </view>
                  <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                    最低消费金额（选填）:
                  </view>
                  <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                    消费金额（选填）:
                  </view>
                  <view class="flex items-center">
                    <view class="flex-1">
                      <wd-input
                        prop="consumeMinAmount"
                        class="form-input"
                        placeholder="请输入金额"
                        v-model="item.consumeMinAmount"
                      />
                    </view>
                    <!-- <text class="m-x-24rpx w-16rpx h-2rpx bg-black bg-opacity-50"></text>
                <view class="flex-1">
                  <wd-input
                    prop="consumeMaxAmount"
                    class="form-input"
                    placeholder="请输入结束金额"
                    v-model="pushData.consumeMaxAmount"
                  />
                </view> -->
                  </view>
                </template>
                <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                  推送时间（当天只可选择3个时间）
                </view>
                <view v-for="(_, i) in item.pushTimeList" :key="i" class="flex items-center">
                  <wd-datetime-picker
                    v-model="item.pushTimeList[i]"
                    :minDate="new Date()"
                    custom-class="flex-1"
                  />
                  <wd-button type="icon" icon="add" class="add-btn" @click="addPushTime(index)" />
                  <wd-button
                    type="icon"
                    icon="remove"
                    class="remove-btn"
                    @click="removePushTime(index, i)"
                  />
                </view>
              </view>
              <view class="submit">
                <wd-button type="primary" custom-class="submit-btn" @click="handleSbmitPush">
                  推送
                </wd-button>
              </view>
            </wd-form>
          </scroll-view>
        </template>
      </view>
    </wd-popup>
  </view>
  <back :goHome="true" />
  <articleShare ref="shareRef" />

  <!-- 更换背景 -->
  <wd-popup
    v-model="bgChangeShow"
    position="bottom"
    closable
    custom-style="padding: 45rpx;border-top-left-radius:16rpx;border-top-right-radius:16rpx;"
  >
    <view class="text-32rpx font-bold">选择更换背景</view>
    <view class="">
      <scroll-view
        scroll-x
        :show-scrollbar="false"
        class="whitespace-nowrap py-20rpx gap-30rpx px-10rpx"
      >
        <view v-for="(img, index) in bgList" :key="index" class="relative inline-block mx-10rpx">
          <image
            :src="img.url"
            class="w-200rpx h-200rpx rounded-lg"
            :class="{ 'border-6rpx border-solid border-#FF0000': article.topBg === img.url }"
            @click="article.topBg = img.url"
            @error="showNameMap[index] = true"
          />
          <view
            v-if="showNameMap[index]"
            class="absolute inset-0 bg-gray-100/80 flex items-center justify-center text-24rpx p-10rpx break-all"
          >
            {{ img.name }}
          </view>
        </view>
      </scroll-view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
// import { useRoute } from 'vue-router'
import { isEmpty } from 'lodash-es'
import { format } from '@/utils/format'
import back from '@/components/back/index.vue'
import articleShare from '@/components/articleShare/article-share.vue'
import { useMessage } from 'wot-design-uni'
import { NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION } from '@/utils/regex'
import {
  IArticleDetail,
  IArticlePushConfig,
  getArticleDetail,
  getArticlePushConfig,
  updateArticleConsumeInfo,
  updateArticlePushConfig,
  changeTopStatus,
  changeArticleStatus,
} from '@/api/article'
import dayjs from 'dayjs'
import { MAX_INTEGER_VALUE } from '@/utils/validator'
import { MODAL_TYPE } from '@/utils/enum'
import { getPushTemplate, articleDelete } from '@/api/mpAccount'
import { useUserStore } from '@/store/user'

type IPublishData = Omit<IArticlePushConfig, 'pushTimeList'>

const modalType = ref(MODAL_TYPE.PUBLISH)
const shareRef = ref()
const articleId = ref<null | string>(null)
// const route = useRoute()
const article = ref<IArticleDetail>({} as IArticleDetail)
const visible = ref(false)

const publishFormRef = ref()
const pushFormRef = ref()
const comfirmMessage = useMessage()
const publishData = ref<IPublishData>({} as IPublishData)
const pushData = ref<IArticlePushConfig[]>([
  { pushTimeList: [], templateId: null },
] as IArticlePushConfig[])

const deleteShow = ref(false)
const handleDeleteArticle = () => {
  if (!articleId.value) return
  articleDelete(Number(articleId.value)).then((res) => {
    if (res) {
      deleteShow.value = false
      uni.showToast({ title: '删除成功', icon: 'none' })
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    }
  })
}

const userStore = useUserStore()

const currentAccountId = computed(() => userStore.userInfo.captivePushAccount)

const pushTemplateList = ref([])

const canEdit = computed(() => {
  if (!article.value) return false
  return article.value.canEdit
})

const bgChangeShow = ref(false)
const showBgList = ref(false)
const showNameMap = ref({})
const defaultTopBg = ref('https://sacdn.850g.com/football/static/scheme/top_bg.png')
const bgList = ref([
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg.png', name: '默认背景' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg1.png', name: '背景1' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg2.png', name: '背景2' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg3.png', name: '背景3' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg4.png', name: '背景4' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg5.png', name: '背景5' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg6.png', name: '背景6' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg7.png', name: '背景7' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg8.png', name: '背景8' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg9.png', name: '背景9' },
])
const handleShowBgChange = () => {
  bgChangeShow.value = true
}

const publishFormRules = reactive({
  // consumeMinNum: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMaxNum: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMinAmount: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMaxAmount: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
})

const pushFormRules = reactive({
  // consumeMinNum: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMaxNum: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMinAmount: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMaxAmount: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
})

const handleConfirm = ({ value, selectedItems }) => {
  console.log('value', value, selectedItems)
}

function publishArticle() {
  if (modalType.value != MODAL_TYPE.PUBLISH) modalType.value = MODAL_TYPE.PUBLISH
  visible.value = true
}

function pushArticle() {
  if (modalType.value != MODAL_TYPE.PUSH) modalType.value = MODAL_TYPE.PUSH
  visible.value = true
}

function gotoDetail() {
  if (!articleId.value) return
  uni.navigateTo({
    url: `/pages/detail/index?id=${articleId.value}`,
  })
}

function goEdit() {
  if (!articleId.value) return
  uni.navigateTo({ url: `/pages/article/relaese/index?id=${articleId.value}` })

  // if (article.value.matchIds) {
  //   uni.navigateTo({
  //     url: `/pages/article/relaese/index?id=${articleId.value}`,
  //   })
  // } else {
  //   uni.navigateTo({
  //     url: `/pages/article/relaese/index_old?id=${articleId.value}`,
  //   })
  // }
}
function goAddPaid() {
  if (!articleId.value) return
  uni.navigateTo({
    url: `/pages/article/append/index?id=${articleId.value}&type=1&title=${article.value.title}`,
  })
}

function goAddFree() {
  if (!articleId.value) return
  uni.navigateTo({
    url: `/pages/article/append/index?id=${articleId.value}&type=0&title=${article.value.title}`,
  })
}

function goShare() {
  shareRef.value.base64Data = ''
  shareRef.value.showDialog(article.value)
}

const goBuyUserInfo = (type) => {
  if (!articleId.value) return
  uni.navigateTo({
    url: `/pages/detail/user/index?articleId=${articleId.value}&type=${type}`,
  })
}

const changeTop = async (topStatus) => {
  if (!articleId.value) return
  if (article.value.status !== 2) {
    const data = {
      id: articleId.value,
      topStatus: topStatus === 1 ? 0 : 1,
    }

    const res = await changeTopStatus(data)

    uni.showToast({
      title: data.topStatus === 1 ? '置顶成功' : '取消置顶成功',
      icon: 'none',
    })
    article.value.top = data.topStatus
  } else {
    uni.showToast({
      title: '该方案已封禁,无法进行置顶操作',
      icon: 'none',
    })
  }
}

const changeStatus = (status) => {
  if (!articleId.value) return
  if (article.value.status !== 2) {
    const data = {
      id: articleId.value,
      status: status === 0 ? 1 : 0,
    }
    const confirm = async () => {
      const res = await changeArticleStatus(data)
      uni.showToast({
        title: data.status === 1 ? '上架成功' : '下架成功',
        icon: 'none',
      })
      article.value.status = data.status
    }

    if (data.status === 1) {
      confirm()
    } else {
      comfirmMessage
        .confirm({
          title: '下架',
          msg: '确认要下架该方案吗？',
        })
        .then(() => {
          confirm()
        })
        .catch(() => {
          console.log('取消')
        })
    }
  } else {
    uni.showToast({
      title: '该方案已封禁,无法进行上下架操作',
      icon: 'none',
    })
  }
}

function addDefaultTime() {
  const now = new Date()
  return new Date(format(dayjs(now).add(5, 'minute'), 'YYYY-MM-DD HH:mm'))
}

function addPushTime(index) {
  if (pushData.value[index].pushTimeList.length < 3) {
    pushData.value[index].pushTimeList.push(addDefaultTime())
  }
}

function removePushTime(index: number, i: number) {
  if (pushData.value[index].pushTimeList.length > 1) {
    pushData.value[index].pushTimeList.splice(i, 1)
  }
}

const addPushTemplate = (index) => {
  pushData.value.push({
    articleId: article.value.id,
    pushTimeList: [addDefaultTime()],
    consumeMaxAmount: null,
    consumeMaxNum: null,
    consumeMinAmount: 0,
    consumeMinNum: 0,
    consumeStatus: 0,
    templateId: null,
  })
}

const removePushTemplate = (index) => {
  pushData.value.splice(index, 1)
}

const pickTemplate = computed(() => {
  if (pushTemplateList.value.length > 0) {
    return true
  }

  return false
})

/* 提交推送 */
function handleSbmitPush() {
  if (!pushFormRef.value) return
  pushFormRef.value.validate().then(({ valid }) => {
    if (!valid) return
    const id = articleId.value
    // 提交推送
    if (pickTemplate.value) {
      for (let i = 0; i < pushData.value.length; i++) {
        const { templateId, ...rest } = pushData.value[i]
        if (!templateId) {
          uni.showToast({ title: '请选择推送模版', icon: 'none' })
          return
        }
      }
    } else {
      for (let i = 0; i < pushData.value.length; i++) {
        const { templateId, ...rest } = pushData.value[i]
        if (templateId) {
          pushData.value[i].templateId = null
        }
      }
    }

    // 拷贝 pushData.value
    const req = []
    for (let i = 0; i < pushData.value.length; i++) {
      const { pushTimeList, ...rest } = pushData.value[i]
      const p = {
        articleId: id,
        ...rest,
        pushTimeList: pushTimeList.map(
          (t) => new Date(dayjs(t as string).format('YYYY-MM-DD HH:mm')),
        ),
      }
      req.push(p)
    }

    updateArticlePushConfig(req).then((s) => {
      if (s) {
        uni.showToast({ title: '提交成功', icon: 'none' })
        visible.value = false
        getArticlePushConfig(parseInt(id as string)).then((pushConfig) => {
          for (const singleConfig in pushConfig) {
            const { pushTimeList, ...restPushConfig } = pushConfig[singleConfig]
            const p = isEmpty(pushTimeList)
              ? [addDefaultTime()]
              : pushTimeList.map((t) => new Date(format(t, 'YYYY-MM-DD HH:mm')))
            pushConfig[singleConfig] = {
              pushTimeList: p,
              ...restPushConfig,
            }
          }

          pushData.value = pushConfig
        })
      }
    })
  })
}

/* 公开 */
function handlePublicSubmit() {
  if (!publishFormRef.value) return
  publishFormRef.value.validate().then(({ valid }) => {
    if (!valid) return
    // const {
    //   query: { id },
    // } = route
    const id = articleId.value
    // 提交公开
    updateArticleConsumeInfo(publishData.value).then((s) => {
      if (s) {
        uni.showToast({ title: '提交成功', icon: 'none' })
        visible.value = false
        getArticleDetail(parseInt(id as string)).then((articleDetail) => {
          const {
            consumeStatus,
            consumeMinNum,
            // consumeMaxNum,
            consumeMinAmount,
            // consumeMaxAmount,
          } = articleDetail

          publishData.value = {
            articleId: parseInt(id as string),
            consumeStatus,
            consumeMinNum,
            // consumeMaxNum,
            consumeMinAmount,
            // consumeMaxAmount,
          }
          article.value = articleDetail
        })
      }
    })
  })
}

onLoad(async ({ id }) => {
  articleId.value = id
  uni.showLoading({ title: '加载中...' })
  const [articleDetail, pushConfig] = await Promise.all([
    getArticleDetail(id),
    getArticlePushConfig(id),
  ])

  const { consumeStatus, consumeMinNum, consumeMinAmount } = articleDetail

  publishData.value = {
    articleId: parseInt(id),
    consumeStatus,
    consumeMinNum,
    // consumeMaxNum,
    consumeMinAmount,
    // consumeMaxAmount,
  }

  article.value = articleDetail

  for (const singleConfig in pushConfig) {
    const { pushTimeList, ...restPushConfig } = pushConfig[singleConfig]
    const p = isEmpty(pushTimeList)
      ? [addDefaultTime()]
      : pushTimeList.map((t) => new Date(format(t, 'YYYY-MM-DD HH:mm')))
    pushConfig[singleConfig] = {
      pushTimeList: p,
      ...restPushConfig,
    }
  }
  console.log('pushConfig', pushConfig)
  pushData.value = pushConfig

  const pushTemplateRes = await getPushTemplate(currentAccountId.value)
  pushTemplateList.value = pushTemplateRes

  uni.hideLoading()
})
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 690rpx;
  padding: 50rpx 30rpx 0;
  background-image: url('https://sacdn.850g.com/football/static/scheme/top_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .changebg {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 130rpx;
    height: 34rpx;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1rpx solid rgb(177, 175, 175);
    border-radius: 8rpx;
  }
}

.intro {
  display: -webkit-box;
  width: 100%;
  margin: 30rpx 0 50rpx;
  overflow: hidden;
  font-size: 32rpx;
  line-height: 40rpx;
  color: white;
  text-align: center;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.opt-group {
  [class$='-opt'] {
    width: 330rpx;
    height: 80rpx !important;
    margin: unset;
    font-size: 30rpx !important;
    border-radius: 12rpx !important;
  }

  .detail-opt {
    color: #d1302e;
    background-color: #fff;
    border: 1px solid #d1302e;
  }
}

.push-btn {
  padding: 6rpx 25rpx;
  border: 1px solid red;
  border-radius: 12rpx;
}

.setting {
  display: flex;
  justify-content: space-between;
  padding: 34rpx 78rpx 38rpx;
  margin: 0 30rpx;
  border: 1rpx solid rgba(121, 121, 121, 0.2);
  border-style: solid none;

  > view {
    display: flex;
    flex-direction: column;
    row-gap: 30rpx;
  }
}

.form-label {
  margin-bottom: 10rpx;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.9);

  &.required {
    &::after {
      display: inline-block;
      color: #d1302e;
      content: '*';
    }
  }
}

:deep() {
  .submit {
    display: flex;
    justify-content: center;

    .submit-btn {
      width: 305rpx;
      height: 80rpx;
      margin: 30px auto 0;
      border-radius: 12rpx;
    }
  }
}

:deep() {
  .add-btn,
  .remove-btn {
    width: 40rpx !important;
    height: 40rpx !important;
    border: 1px solid rgba($color: #000000, $alpha: 0.3);
  }

  .add-btn {
    margin: 0 20rpx;
  }
}
</style>
