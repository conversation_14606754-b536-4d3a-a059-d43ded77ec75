<route lang="json5">
{
  style: {
    navigationBarTitleText: '关注列表',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <view class="py-2">
      <wd-input
        class="px-2 py-2 rounded-[20rpx]"
        v-model="searchText"
        prefix-icon="search"
        clearable
        no-border
        @clear="getData"
        @confirm="getData"
        placeholder="输入关键字查询"
      />
    </view>
    <view class="jump">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view
        class="jp-item"
        v-for="(item, index) in dataList"
        @click="goAuthorPage(item.authorId)"
        :key="index"
        v-else
      >
        <view class="flex justify-center items-center">
          <image
            class="avatar"
            :src="item.authorAvatar || 'https://sacdn.850g.com/football/static/avatar.svg'"
          />
          <view class="justify-center items-center">
            <wd-row>
              <text class="nick">{{ item.authorName }}</text>
            </wd-row>
            <wd-row>
              <text class="time">
                关注时间:{{ dayjs(item.attentionTime).format('YYYY-MM-DD') }}
              </text>
            </wd-row>
          </view>
        </view>
        <view flex justify-center items-center>
          <text class="publish" @click="cancelAttentionFn(item.authorId)">取消关注</text>
        </view>
      </view>
    </view>
  </view>
  <back />
</template>
<script lang="ts" setup>
import { getAttentionList, cancelAttention } from '@/service/userService'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'

const dataList = ref([])
const searchText = ref('')

const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  getData()
})

const getData = async () => {
  const data = await getAttentionList(searchText.value)
  dataList.value = data
  if (isRefresh.value) {
    //提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

const cancelAttentionFn = async (authorId: number) => {
  await cancelAttention(authorId)
  getData()
}

const goAuthorPage = (authorId: number) => {
  uni.navigateTo({
    url: `/pages/author/info/index?authorId=${authorId}&ts=${Date.now()}`,
  })
}

onMounted(() => {
  getData()
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-icon-arrow-right) {
  display: none !important;
}

.jump {
  min-height: 80vh;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    border-bottom: 1rpx solid rgb(236, 234, 234);

    &:active {
      background-color: rgb(246, 246, 246);
    }

    .avatar {
      width: 100rpx;
      height: 100rpx;
      margin: 20rpx;
      border-radius: 50%;
    }

    .nick {
      font-size: 26rpx;
      line-height: 50rpx;
    }

    .time {
      font-size: 26rpx;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.5);
    }

    .publish {
      display: flex;
      gap: 10rpx;
      align-items: center;
      justify-content: center;
      width: 100rpx;
      padding: 10rpx;
      margin: 20rpx;
      font-size: 24rpx;
      font-weight: 600;
      color: #d1302e;
      border: 1rpx solid #d1302e;
      border-radius: 8rpx;
    }
  }
}
</style>
