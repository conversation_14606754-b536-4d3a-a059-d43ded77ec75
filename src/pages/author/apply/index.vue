<route lang="json5">
{
  style: {
    navigationBarTitleText: '申请作者',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="pt-30rpx px-30rpx pb-100rpx">
    <wd-form :model="formData" :rules="rules" ref="formRef">
      <view class="flex flex-col">
        <view class="mb-30rpx text-32rpx text-black text-opacity-90">基本资料</view>
        <view class="flex flex-col" style="flex-direction: row">
          <text
            class="mb-20rpx pl-30rpx text-30rpx text-black text-opacity-90 w-[160rpx] mr-[30rpx]"
          >
            头像
          </text>

          <!-- <view class="upload-wrap mr-10rpx" v-show="formData.imgUrl.length === 0">
          <text class="upload-text">默认头像</text>
          <image class="authoravatar" :src="userInfo.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg'" />
        </view> -->
          <view class="apply-upload">
            <wd-upload
              :header="uploadHearder"
              :limit="1"
              image-mode="aspectFill"
              :action="action"
              :file-list="formData.imgUrl"
              @change="({ fileList }) => changeUploadImg(fileList, 'imgUrl')"
            >
              <template #default>
                <view class="upload-wrap">
                  <text class="upload-text">修改默认头像</text>
                  <image
                    class="authoravatar"
                    :src="userInfo.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg'"
                  />
                </view>

                <!-- <view class="upload">
                <wd-icon name="camera" size="68rpx" color="rgba(0,0,0,0.3)" />
                <text class="text-26rpx text-black text-opacity-30">上传头像</text>
              </view> -->
              </template>
            </wd-upload>
          </view>
        </view>
        <wd-input
          label="昵称"
          custom-class="sy-input"
          label-width="160rpx"
          placeholder="请输入昵称"
          prop="nickname"
          v-model="formData.nickname"
        />
        <wd-input
          label="联系电话"
          custom-class="sy-input"
          label-width="160rpx"
          placeholder="请输入联系电话"
          prop="phone"
          v-model="formData.phone"
        />
        <view class="pt-30rpx pl-30rpx text-26rpx text-black text-opacity-90">
          <text>个人简介</text>
          <text class="text-black text-opacity-30">（为了更好的吸引用户购买方案）</text>
        </view>
        <wd-textarea
          placeholder="请输入介绍信息(最多30个字符)"
          :maxlength="30"
          show-word-limit
          prop="desc"
          v-model="formData.desc"
          custom-class="sy-textarea"
        />
        <text class="mt-40rpx mb-50rpx text-32rpx text-black text-opacity-90">银行卡资料</text>
        <wd-input
          label="银行卡"
          type="number"
          custom-class="sy-input"
          label-width="160rpx"
          placeholder="请输入提现卡号"
          prop="bankNo"
          v-model="formData.bankNo"
        />
        <wd-input
          label="开户行"
          custom-class="sy-input"
          label-width="160rpx"
          placeholder="请输入开户行"
          prop="bankName"
          v-model="formData.bankName"
        />
        <wd-input
          label="开户人姓名"
          custom-class="sy-input"
          label-width="160rpx"
          placeholder="请输入开户人姓名"
          prop="bankRealName"
          v-model="formData.bankRealName"
        />
        <view class="flex justify-between items-center mt-40rpx mb-50rpx">
          <text class="text-32rpx text-black text-opacity-90">实名资料</text>
          <!-- <view class="flex items-center gap-x-5rpx h-40rpx px-10rpx rounded-8rpx bg-#D1302E text-white"
            @click="identifyCard">
            <wd-icon name="camera" size="26rpx" />
            <text class="text-24rpx">证件识别</text>
          </view> -->
        </view>
        <wd-input
          label="真实姓名"
          custom-class="sy-input"
          label-width="160rpx"
          placeholder="请输入真实姓名"
          prop="name"
          v-model="formData.name"
        />
        <wd-input
          label="身份证号"
          custom-class="sy-input"
          label-width="160rpx"
          placeholder="请输入身份证号"
          prop="idCard"
          v-model="formData.idCard"
        />
        <text class="mt-30rpx mb-20rpx pl-30rpx text-30rpx text-black text-opacity-90">
          上传身份证
        </text>
        <view class="flex gap-x-20rpx px-30rpx">
          <wd-upload
            :header="uploadHearder"
            :limit="1"
            image-mode="aspectFill"
            :action="action"
            :file-list="formData.idCardFront"
            @change="({ fileList }) => changeUploadImg(fileList, 'idCardFront')"
          >
            <template #default>
              <view class="upload">
                <wd-icon name="camera" size="68rpx" color="rgba(0,0,0,0.3)" />
                <text class="text-26rpx text-black text-opacity-30">身份证正面</text>
              </view>
            </template>
          </wd-upload>
          <wd-upload
            :header="uploadHearder"
            :limit="1"
            image-mode="aspectFill"
            :action="action"
            :file-list="formData.idCardBack"
            @change="({ fileList }) => changeUploadImg(fileList, 'idCardBack')"
          >
            <template #default>
              <view class="upload">
                <wd-icon name="camera" size="68rpx" color="rgba(0,0,0,0.3)" />
                <text class="text-26rpx text-black text-opacity-30">身份证反面</text>
              </view>
            </template>
          </wd-upload>
        </view>
        <text class="mt-40rpx mb-50rpx text-32rpx text-black text-opacity-90">专家经历</text>
        <text class="mb-20rpx pl-30rpx text-26rpx text-black text-opacity-90">专家简历描述</text>
        <wd-textarea
          placeholder="包括其他平台名称、写作经历"
          :maxlength="1000"
          show-word-limit
          prop="proDesc"
          v-model="formData.proDesc"
          custom-class="sy-textarea"
        />
      </view>
      <view class="flex mt-30rpx mb-20rpx pl-30rpx text-26rpx">
        <text class="text-black text-opacity-90">截图证明</text>
        <text class="text-black text-opacity-30">（最多上传5张）</text>
      </view>
      <wd-upload
        :header="uploadHearder"
        :limit="5"
        image-mode="aspectFill"
        :action="action"
        :file-list="formData.proImgUrls"
        @change="({ fileList }) => changeUploadImg(fileList, 'proImgUrls')"
      />
    </wd-form>
    <view class="flex items-center mt-30rpx mb-40rpx">
      <wd-checkbox v-model="admit" shape="dot" />
      <view class="flex items-center text-28rpx">
        <text class="text-black text-opacity-50" @click="changeAdmit">我已经阅读并同意</text>
        <text class="text-#D1302E" @click.stop="viewProtocal">《作者入驻平台协议》</text>
      </view>
    </view>
    <wd-button block :round="false" @click="submit">提交申请</wd-button>
  </view>
</template>

<script setup lang="ts">
import { IAuthorAuditParams, identifyIDCard, identifyIDCardBack, submitAuthor } from '@/api/author'
import { useUserStore } from '@/store'
import {
  PHONE_REG,
  ID_CARD_REG,
  CHINESE_NAME,
  BANK_CARD_REG,
  CHINESE_CHARACTER,
} from '@/utils/regex'
import { isEmpty } from 'lodash-es'
import { storeToRefs } from 'pinia'
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const formData = ref({
  imgUrl: [] as { [key: string]: any; url: string }[], // 头像
  // imgUrl: [{url: 'https://sacdn.850g.com/test/pictrue//isPuTuNs5GNYep0ECix4aPZfBodEfpua_1746069259529.png'}] as { [key: string]: any; url: string }[], // 头像
  idCardFront: [] as { [key: string]: any; url: string }[], // 身份证正面
  idCardBack: [] as { [key: string]: any; url: string }[], // 身份证反面
  proImgUrls: [] as { [key: string]: any; url: string }[], // 专家截图证明
  nickname: userInfo.value.nickname,
  phone: '',
  desc: '',
  bankNo: '', // 银行卡号
  bankName: '',
  bankRealName: '',
  name: '',
  idCard: '',
  proDesc: '',
})

const rules = reactive({
  // nickname: [{ required: true, message: '请输入昵称' }],
  phone: [
    { required: true, messagge: '请输入电话号码' },
    { pattern: PHONE_REG, message: '请输入正确的电话号码' },
  ],
  // desc: [{ required: true, message: '请输入个人简介' }],
  // bankNo: [
  //   { required: true, message: '请输入银行卡号' },
  //   { pattern: BANK_CARD_REG, message: '请输入正确的银行卡号' },
  // ],
  // bankName: [
  //   { required: true, message: '请填写开户行信息' },
  //   {
  //     pattern: CHINESE_CHARACTER,
  //     message: '请输入正确的开户行信息',
  //   },
  // ],
  // bankRealName: [{ required: true, message: '请填写开户人姓名' }],
  // name: [
  //   { required: true, message: '请输入真实姓名' },
  //   { pattern: CHINESE_NAME, message: '请输入正确的姓名' },
  // ],
  // idCard: [
  //   { required: true, message: '身份证号码不能为空' },
  //   { pattern: ID_CARD_REG, message: '请输入正确的身份证号' },
  // ],
})

const admit = ref(false)
const loading = ref(false)

const action = computed(() => {
  return import.meta.env.VITE_UPLOAD_BASEURL
})

const uploadHearder = computed(() => {
  return {
    Authorization: `Bearer ${userInfo.value.token}`,
  }
})

const formRef = ref()

async function identifyCard() {
  if (isEmpty(formData.value.idCardFront)) {
    uni.showToast({ icon: 'none', title: '请上传身份证正面照' })
    return
  }

  try {
    uni.showLoading()
    const [{ url: image }] = formData.value.idCardFront
    const { name, num: idCard, success } = await identifyIDCard(image)
    if (success) {
      formData.value = { ...formData.value, name, idCard }
    }
  } catch {
    formData.value['idCardFront'] = []
  } finally {
    uni.hideLoading()
  }
}

async function identifyCardBack() {
  if (isEmpty(formData.value.idCardBack)) {
    uni.showToast({ icon: 'none', title: '请上传身份证反面照' })
    return
  }

  try {
    uni.showLoading()
    const [{ url: image }] = formData.value.idCardBack
    // const { name, num: idCard, success } = await identifyIDCardBack(image)
    await identifyIDCardBack(image)
  } catch (e) {
    console.log(e)
    formData.value['idCardBack'] = []
  } finally {
    uni.hideLoading()
  }
}

function changeUploadImg(fl: any[], name: 'imgUrl' | 'idCardFront' | 'idCardBack' | 'proImgUrls') {
  formData.value[name] = fl.map((data) => {
    const { response } = data
    const result = JSON.parse(response)
    return { ...data, url: result.data }
  })

  if (name === 'idCardFront' && !isEmpty(formData.value.idCardFront)) {
    identifyCard()
  } else if (name === 'idCardBack' && !isEmpty(formData.value.idCardBack)) {
    identifyCardBack()
  }
}

function changeAdmit() {
  admit.value = !admit.value
}

function viewProtocal() {
  uni.navigateTo({ url: '/pages/article/apply/authProtocal' })
}

async function submit() {
  console.log('aaaaaaaaaaaaaaaaaaaaaaaa formData.value: ', formData.value)
  loading.value = true

  if (!admit.value) {
    uni.showToast({ title: '请同意协议', icon: 'none' })
    return
  }

  // 电话号码校验
  if (isEmpty(formData.value.phone)) {
    uni.showToast({ title: '请输入电话号码', icon: 'none' })
    return
  }

  // const { valid } = await formRef.value.validate()
  // if (!valid) return

  // 上传图片校验
  // if (isEmpty(formData.value.imgUrl)) {
  //   uni.showToast({ title: '请上传头像', icon: 'none' })
  //   return
  // }

  // if (isEmpty(formData.value.idCardFront)) {
  //   uni.showToast({ title: '请上传身份证正面', icon: 'none' })
  //   return
  // }

  // if (isEmpty(formData.value.idCardBack)) {
  //   uni.showToast({ title: '请上传身份证反面', icon: 'none' })
  //   return
  // }

  // if (isEmpty(formData.value.proImgUrls)) {
  //   uni.showToast({ title: '请上传专家截图证明', icon: 'none' })
  //   return
  // }

  const { imgUrl, idCardFront, idCardBack, proImgUrls, proDesc, ...rest } = formData.value
  console.log('cccccccccccccccccc formData.value: ', formData.value)

  let data: IAuthorAuditParams = {
    ...rest,
    imgUrl: imgUrl.length ? imgUrl[0].url : userInfo.value.avatarUrl,
    idCardFront: idCardFront.length ? idCardFront[0].url : null,
    idCardBack: idCardBack.length ? idCardBack[0].url : null,
    proImgUrls: proImgUrls.length
      ? proImgUrls.reduce(
          (prev, { url }, index) =>
            index === proImgUrls.length - 1 ? `${prev}${url}` : `${prev}${url},`,
          '',
        )
      : null,
  }

  data = proDesc ? { ...data, proDesc } : data
  await submitAuthor(data)
  loading.value = false
  uni.switchTab({ url: '/pages/myInfo/index' })
}
</script>

<style lang="scss" scoped>
$zone-multiplier: 3;
.sy-input,
.sy-textarea {
  border-bottom: 1rpx solid rgba(121, 121, 121, 0.1);
}

.upload {
  display: flex;
  flex-direction: column;
  row-gap: 5rpx;
  align-items: center;
  justify-content: center;
  width: 150rpx;
  height: 150rpx;
  background-color: rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;
}
.apply-upload {
  width: 150rpx;
  height: 150rpx;
  :deep(.wd-upload__preview) {
    margin: 0 !important;
  }
  // :deep(.wd-upload__close){
  //   display: none !important;
  // }
}
.upload-wrap {
  position: relative;
  .authoravatar {
    width: 150rpx;
    height: 150rpx;
    overflow: hidden;
    border-radius: 10rpx;
  }
  .upload-text {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 50rpx;
    font-size: 22rpx;
    line-height: 50rpx;
    color: #fff;
    text-align: center;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0 0 10rpx 10rpx;
  }
}
</style>
