<route lang="json5">
{
  style: {
    navigationBarTitleText: '作者信息',
  },
}
</route>
<template>
  <view v-if="pushVisible && authorData.authorId !== userStore.userInfo?.id">
    <subAuthorMp
      :author-id="authorData.authorId"
      :avatar-url="authorData.authorAvatar"
      :username="authorData.authorName"
    />
  </view>
  <view v-else-if="finishData" class="overflow-hidden bg-[#f4f8fa] min-h-screen">
    <view class="author-info">
      <view class="jp-item relative">
        <view class="flex justify-center items-center">
          <image
            class="avatar"
            :src="authorData.authorAvatar || 'https://sacdn.850g.com/football/static/avatar.svg'"
          />
          <view class="justify-center items-center">
            <view class="flex">
              <text class="nick">{{ authorData.authorName }}</text>
              <template v-if="authorData.accomplishment">
                <view
                  v-if="authorData.winCount > 2 && authorData.hitRate >= 50"
                  class="flex items-center h-40rpx mr-0rpx border-1rpx border-solid border-#D1302E rounded-12rpx"
                >
                  <text
                    class="flex justify-center items-center px-11rpx text-28rpx text-white bg-#D1302E rounded-12rpx"
                  >
                    {{ authorData.winCount }}
                  </text>
                  <text class="ml-5rpx mr-7rpx text-24rpx text-#D1302E">连中</text>
                </view>
                <text
                  v-if="authorData.recentWinCount"
                  class="flex justify-center items-center h-40rpx ml-10rpx px-11rpx text-24rpx text-#D1302E border-1rpx border-solid border-#D1302E rounded-12rpx"
                >
                  {{ authorData.recentWinCount }}
                </text>
              </template>
            </view>
            <wd-row>
              <text class="time">ID: {{ authorData.authorId }}</text>
            </wd-row>
          </view>
        </view>
        <view>
          <view
            flex
            justify-center
            items-center
            v-if="authorData.authorId !== userStore.userInfo?.id"
          >
            <text
              class="publish"
              @click="cancelAttentionFn(authorData.authorId)"
              v-if="authorData.attentionStatus == 1"
            >
              取消关注
            </text>
            <text class="publish" @click="comfirmAttentionFn(authorData.authorId)" v-else>关注+</text>
          </view>
          <view class="text-right mr-20rpx">
            <image @click="wxNumberShow = !wxNumberShow" src="https://sacdn.850g.com/football/static/wx-icon.png" class="w-50rpx h-50rpx rounded-full"></image>
          </view>
        </view>
        <view v-if="wxNumberShow" class="wx-tips absolute left-0 bottom-[-120rpx] z-999 w-full h-120rpx mt-10rpx flex items-center justify-between px-50rpx box-border">
          <view class="flex items-center">
            <image src="https://sacdn.850g.com/football/static/wx-icon.png" class="w-50rpx h-50rpx"></image>
            <view class="ml-10rpx text-white text-26rpx">添加客服微信, 免费解锁方案</view>
          </view>
          <view @click="copyWxNumberClick" class="bg-#d52c2c text-white  rounded-24rpx text-26rpx py-10rpx px-15rpx">复制微信号</view>
        </view>
      </view>
      <view class="p-20rpx mb-20rpx color-[#8b8b8b]" v-if="false">
        <wd-row>
          <wd-col :span="8">
            <view class="text-left">
              <text class="fw-bold m-5rpx color-black">{{ authorData.attention }}</text>
              关注
            </view>
          </wd-col>
          <wd-col :span="8">
            <view class="text-center">
              <text class="fw-bold m-5rpx color-black">{{ authorData.fans }}</text>
              粉丝
            </view>
          </wd-col>
          <wd-col :span="8">
            <view class="text-end">
              入驻
              <text class="fw-bold m-5rpx color-black">{{ authorData.registerCount }}</text>
              天
            </view>
          </wd-col>
        </wd-row>
      </view>
    </view>
    <view class="text-center" v-if="authorData.privilegeSetStatus === 1">
      <image
        src="https://sacdn.850g.com/football/config/privilege/privilege_buy.png "
        class="w-[690rpx] h-[165rpx]"
        @click="goPrivilege"
      />
    </view>
    <wd-tabs v-model="articleTab" custom-class="articleTab" @change="handleTabChange">
      <wd-tab title="在售方案">
        <view class="article-box">
          <view v-if="!onSaleList.length">
            <wd-status-tip image="search" tip="当前搜索无结果" />
          </view>
          <view
            v-else
            v-for="(item, index) in onSaleList"
            :key="index"
            class="article"
            @click="goDetail(item.id)"
          >
            <view class="left">
              <view class="title">{{ item.title }}</view>
              <view>
                <text class="price">￥{{ item.price }}</text>
                <text class="time">{{ item.createTime }}</text>
              </view>
            </view>
            <view class="right">
              <view class="text-right">
                <wd-icon name="arrow-right" size="22px" color="rgb(150,150,150)"></wd-icon>
              </view>
              <image
                v-if="item.refundType === 1"
                src="https://sacdn.850g.com/football/static/refund.png"
                class="w-[140rpx] h-[45rpx]"
              />
            </view>
          </view>
        </view>
      </wd-tab>
      <wd-tab title="在售套餐">
        <author-user-package :package="packageList" :curAuthorId="authorData.value?.authorId" />
      </wd-tab>
      <wd-tab title="已结束方案">
        <view class="article-box">
          <view v-if="!endList.length">
            <wd-status-tip image="search" tip="当前搜索无结果" />
          </view>
          <view
            v-else
            v-for="(item, index) in endList"
            :key="index"
            class="article flex justify-between items-center"
            @click="goDetail(item.id)"
          >
            <view class="left w-[550rpx]">
              <view class="flex">
                <view class="flex overflow-ellipsis ellipsis whitespace-nowrap">
                  <text class="title">{{ item.title }}</text>
                </view>
                <view>
                  <text :class="winClass(item.win)" v-if="item.win !== 0">
                    {{ item.winName }}
                  </text>
                </view>
              </view>
              <view>
                <text class="price" v-if="item.price">￥{{ item.price }}</text>
                <text class="price" v-else>免费</text>
                <text class="time">{{ item.createTime }}</text>
              </view>
            </view>
            <view class="right">
              <wd-icon name="arrow-right" size="22px" color="rgb(150,150,150)"></wd-icon>
            </view>
          </view>
        </view>
      </wd-tab>
    </wd-tabs>
    <!-- 显示关注公众号二维码 -->
    <wd-popup v-model="mpQrcodeVisible" :close-on-click-modal="false">
      <view class="mt-[40rpx] text-center">
        <image :src="mpQrcodeInfo?.appQrCode" class="w-[400rpx] h-[400rpx]" />
      </view>
      <view class="m-[20rpx]">
        <wd-button type="info">长按识别关注公众号为你领航不迷路</wd-button>
      </view>
    </wd-popup>

    <!-- 接受推送消息 -->
    <mp-modal
      :code="mpAppInfo?.appQrCode"
      :type="mpAppInfo?.type"
      ref="mpRef"
      @after-close="closeQrCode"
    />
    <!-- <wd-popup v-model="mpVisible" closable :close-on-click-modal="false">
    <view class="mt-[40rpx] text-center">
      <image :src="mpAppInfo?.appQrCode" class="w-[400rpx] h-[400rpx]" />
    </view>
    <view class="m-[20rpx]">
      <wd-button type="info" v-if="mpAppInfo?.type === 2">长按识别添加助理接收推送消息</wd-button>
      <wd-button type="info" v-else>长按识别关注公众号接收推送消息</wd-button>
    </view>
  </wd-popup> -->
    <wd-fab
      :expandable="false"
      position="right-bottom"
      v-if="!mpVisible && pushVisible && authorData.authorId !== userStore.userInfo?.id"
      class="mt-[-340rpx] ml-[35rpx]"
      @click="showQrcode"
    >
      <template #trigger>
        <image
          @click="showQrcode"
          src="https://sacdn.850g.com/football/static/button/revice.gif"
          class="w-[180rpx] h-[180rpx]"
        />
      </template>
    </wd-fab>
    <wd-fab
      :expandable="false"
      position="right-bottom"
      class="mt-[-260rpx] ml-[-10rpx]"
      @click="goShare"
      v-if="!mpVisible && authorData.authorId === userStore.userInfo?.id"
    >
      <template #trigger>
        <view @click="goShare" class="revice flex justify-center items-center">
          <image
            src="https://sacdn.850g.com/football/static/button/share.png"
            class="w-[90rpx] h-[90rpx]"
          />
        </view>
      </template>
    </wd-fab>
    <authorShare ref="shareRef" />
    <!-- <back v-if="!mpVisible" /> -->
    <wd-fab
      :expandable="false"
      position="right-bottom"
      v-if="!mpVisible && pushVisible && authorData.authorId !== userStore.userInfo?.id"
      class="mt-[-340rpx] ml-[35rpx]"
      @click="showQrcode"
    >
      <template #trigger>
        <image
          @click="showQrcode"
          src="/static/images/button/revice.gif"
          class="w-[180rpx] h-[180rpx]"
        />
      </template>
    </wd-fab>
    <wd-fab
      :expandable="false"
      position="right-bottom"
      class="mt-[-260rpx] ml-[-10rpx]"
      @click="goShare"
      v-if="!mpVisible && authorData.authorId === userStore.userInfo?.id"
    >
      <template #trigger>
        <view @click="goShare" class="revice flex justify-center items-center">
          <image src="/static/images/button/share.png" class="w-[90rpx] h-[90rpx]" />
        </view>
      </template>
    </wd-fab>
    <authorShare ref="shareRef" />
<!--    <qqMail v-if="authorData?.wxQrCodeUrl" :qrCode="authorData?.wxQrCodeUrl"/>-->
    <subMp
      ref="subMpRef"
      :author-id="authorData.authorId"
      v-if="attentionVisible && authorData.authorId !== userStore.userInfo?.id"
    />
    <!-- <back v-if="!mpVisible" /> -->
  </view>
</template>
<script lang="ts" setup>
import { getCurrentInstance } from "vue";
import { getArticleInfo } from "@/api/author";
import { addAttention, cancelAttention, getAppQrcode, getUserInfo } from "@/service/userService";
import { canUserShowPush, getArticleListById, getPackageListByAuthorId, showAttention } from "@/api/article";
import { useScanStore, useUserStore } from "@/store";
import mpModal from "@/components/mpModal/index.vue";
import authorShare from "@/components/authorShare/index.vue";
import subAuthorMp from "@/components/subAuthorMp/index.vue";
import subMp from "@/components/subMp/index.vue";
import authorUserPackage from "@/pages/index/components/authorUserPackage/index.vue";
import qqMail from '@/components/qqmail/index.vue'
import { IAuthor } from "@/api/user";

const {
  // @ts-ignore
  proxy: { $onLaunched },
} = getCurrentInstance()
const userStore = useUserStore()
const scanStore = useScanStore()
const authorData = ref({
  authorId: 0,
  accomplishment: 0,
  authorName: '作者昵称',
  authorAvatar: '',
  fans: 0,
  attention: 0,
  registerCount: 0,
  attentionStatus: 0,
  privilegeSetStatus: 0,
  hitRate: 0,
  winCount: 0,
  recentWinCount: '',
  wxId: '',
})
const subMpRef = ref()
const finishData = ref(false)
const mpRef = ref<typeof mpModal>()
const mpAppInfo = ref()
const mpVisible = ref(false)
const pushVisible = ref(false)
const attentionVisible = ref(false)
const mpQrcodeInfo = ref()
const mpQrcodeVisible = computed(() => {
  return false
  // return userStore?.userInfo?.pushStatus == 0
})
const shareRef = ref()
const wxNumberShow = ref(false)

function goShare() {
  shareRef.value.showDialog(authorData.value.authorId)
}
const showQrcode = () => {
  mpRef.value && mpRef.value.open()
  getAppQrcode(1, authorData.value.authorId, null).then((data) => {
    mpAppInfo.value = data
  })
  mpVisible.value = true
}
function closeQrCode() {
  mpVisible.value = false
}
const checkAttentionShow = async () => {
  const result = await showAttention(authorData.value.authorId)
  attentionVisible.value = result
}

const showPush = async () => {
  const result = await canUserShowPush(authorData.value.authorId)
  pushVisible.value = result
  // if (pushVisible.value) {
  //   showQrcode()
  // }
}

const packageList = ref([])
const getPackageList = async () => {
  const res = await getPackageListByAuthorId(authorData.value.authorId)
  packageList.value = res
}

function handleTabChange({ name }: { name: number }) {
  // if (name === 1) {
  //   goPrivilege()
  // }
}

const winClass = computed(() => {
  return (win: number) => {
    switch (win) {
      case 1:
        return 'win_red'
      case 2:
      case 7:
        return 'win_black'
      case 4:
      case 5:
      case 6:
      case 8:
        return 'win_yan'
      case 3:
        return 'win_blue'
      default:
        return 'win_now'
    }
  }
})

const articleTab = ref(0)

const articleList = ref([])

const onSaleList = computed(() => {
  return articleList.value.filter((item) => {
    return item.win === 0
  })
})

const endList = computed(() => {
  return articleList.value.filter((item) => {
    return item.win !== 0
  })
})

const goDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/detail/index?id=${id}`,
  })
}

const goPrivilege = () => {
  uni.navigateTo({
    url: `/pages/author/privilege/index?authorId=${authorData.value.authorId}`,
  })
}

onPullDownRefresh(async () => {
  await getAuthorInfo()
  await getArticleList()
  uni.showToast({ title: '刷新成功', icon: 'none' })
  uni.stopPullDownRefresh()
})

const getAuthorInfo = async () => {
  const res: any = await getArticleInfo(authorData.value.authorId)
  authorData.value = res
}

const copyWxNumberClick = () => {
  uni.setClipboardData({
    data: authorData.value.wxId,
    success: () => {
      uni.showToast({ title: '微信号已复制', icon: 'success' })
      wxNumberShow.value = !wxNumberShow.value
    },
  })
}

const cancelAttentionFn = async (authorId: number) => {
  await cancelAttention(authorId)
  uni.showToast({ title: '已取消关注', icon: 'none' })
  getAuthorInfo()
}

const getArticleList = async () => {
  articleList.value = await getArticleListById(authorData.value.authorId)
}

const comfirmAttentionFn = async (authorId: number) => {
  await addAttention(authorId)
  // uni.showToast({ title: '关注成功', icon: 'none' })
  getAuthorInfo()
}

onLoad(async (query) => {
  authorData.value.authorId = query.authorId
  if (!userStore.isLogined) {
    await $onLaunched
  }
  if (query.auto == 1 && userStore.isLogined) {
    await addAttention(authorData.value.authorId)
  }
  if (query.scan == 1) {
    scanStore.setScanAuthorId(authorData.value.authorId)
  }
  if (query.scan == 0) {
    scanStore.setScanAuthorId(null)
  }
  const userInfoResult = await getUserInfo()
  userStore.setUserInfo(userInfoResult)
  if (mpQrcodeVisible.value) {
    mpQrcodeInfo.value = await getAppQrcode(0, authorData.value.authorId, null)
  }
})

onMounted(async () => {
  await getAuthorInfo()
  await showPush()
  await checkAttentionShow()
  await getPackageList()
  finishData.value = true
  getArticleList()
})
</script>
<style lang="scss" scoped>
:deep(.wd-tabs__line) {
  background-color: #d1302e !important;
}

.win_red {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #d1302e;
  border-radius: 8rpx;
}

.win_black {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8rpx;
}

.win_yan {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #ed8702;
  border-radius: 8rpx;
}

.win_blue {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background-color: #70b603;
  border-radius: 8rpx;
}

.author-info {
  margin: 0 20rpx;
  background-color: white;
  border-radius: 10rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    padding: 10rpx 0rpx;

    &:active {
      background-color: rgb(246, 246, 246);
    }

    .avatar {
      width: 100rpx;
      height: 100rpx;
      margin: 20rpx;
      border-radius: 50%;
    }

    .nick {
      margin-right: 20rpx;
      font-size: 26rpx;
      line-height: 50rpx;
    }

    .time {
      font-size: 26rpx;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.5);
    }

    .publish {
      display: flex;
      gap: 10rpx;
      align-items: center;
      justify-content: center;
      width: 130rpx;
      padding: 4rpx 0rpx;
      margin: 20rpx;
      font-size: 24rpx;
      font-weight: 600;
      color: #d1302e;
      border: 1rpx solid #d1302e;
      border-radius: 8rpx;
    }
  }
}

.article-box {
  box-sizing: border-box;
  margin: 20rpx;
  background-color: white;

  .article {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 150rpx;
    margin: 0 20rpx;
    border-bottom: 1rpx solid rgb(235, 235, 235);

    &:active {
      background-color: rgb(246, 246, 246);
    }

    .left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 100%;

      .title {
        max-width: 600rpx;
        margin-bottom: 10rpx;
        overflow: hidden;
        font-size: 32rpx;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        color: rgba(0, 0, 0, 0.9);
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .price {
        font-size: 26rpx;
        font-weight: 600;
        color: #d1302e;
      }

      .time {
        margin-left: 20rpx;
        font-size: 26rpx;
        color: rgb(200, 200, 200);
      }
    }
  }
}
.wx-tips{
  background-image: url("https://sacdn.850g.com/football/static/wx-bg-icon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
