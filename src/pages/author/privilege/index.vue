<route lang="json5">
{
  style: {
    navigationBarTitleText: '特权购买',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen pb-[140rpx]">
    <image
      src="https://sacdn.850g.com/football/config/privilege/privilege_banaer.png"
      class="w-full h-[430rpx]"
    />
    <view
      class="filter-bar bg-white mx-[20rpx] mt-[20rpx] rounded-[12rpx] flex items-center justify-between p-[20rpx]"
    >
      <!-- 包时/包次切换 -->
      <view class="tab-container flex bg-[#f5f5f5] rounded-[8rpx] overflow-hidden">
        <view
          class="tab px-[30rpx] py-[12rpx] text-[28rpx]"
          :class="{ 'bg-[#D1302E] text-white font-bold': type === 0 }"
          @click="handleTypeChange(0)"
        >
          包时特权
        </view>
        <view
          class="tab px-[30rpx] py-[12rpx] text-[28rpx]"
          :class="{ 'bg-[#D1302E] text-white font-bold': type === 1 }"
          @click="handleTypeChange(1)"
        >
          包次特权
        </view>
      </view>

      <view class="filter flex items-center bg-[#f5f5f5]">
        <wd-picker
          class="type-select"
          v-model="selectMatch"
          :columns="[matchTypes]"
          label-key="name"
          value-key="id"
        ></wd-picker>
      </view>
    </view>
    <view
      v-for="item in showItems || []"
      :class="chooseId == item.id ? 'item-active' : 'item'"
      @click="chooseId = item.id"
    >
      <view class="title">
        {{ item.days }}{{ item.type === 1 ? '次' : '天'
        }}{{ item.matchType !== 0 ? getMatchName(item.matchType) : '' }}免费看
      </view>
      <view class="price">￥{{ item.price }}</view>
      <view class="price-day">
        价格低至{{ (item.price / item.days).toFixed(2) }}元/{{ item.type === 1 ? '次' : '天' }}
      </view>
    </view>
    <view class="m-[20rpx]">
      <view class="desc-title">购买说明</view>
      <view class="desc-content">
        <wd-row>1.购买的特权只针对单个作者有效</wd-row>
        <wd-row>2.特权生效期间免费查看该作者的方案</wd-row>
        <wd-row>
          3.特权为虚拟产品且即时生效，一经购买无法退款，请慎重决定，如您在使用过程遇到任何问题请即时联系我们的客服处理
        </wd-row>
      </view>
    </view>
    <view
      style="position: fixed; bottom: 0; padding-top: 20rpx; background-color: #fff"
      class="w-full"
    >
      <view class="flex pl-[10rpx] items-center">
        <wd-checkbox v-model="checkVal" checked-color="#d1302e"></wd-checkbox>
        <text @click="showAgreement" class="font-size-[26rpx] text-[#666]">
          请仔细阅读并勾选
          <text class="text-[#d1302e]">《用户购买协议》</text>
        </text>
        <view
          class="flex justify-center items-center ml-[60rpx] w-[160rpx] h-[60rpx] text-white rounded-lg bg-[#D1302E]"
          style="font-size: 32rpx"
          @click="showPayModal"
        >
          立刻购买
        </view>
      </view>
      <view
        class="flex justify-between items-center h-[100rpx] pl-[30rpx] pr-[40rpx] bg-white"
      ></view>
    </view>
    <!-- 支付弹框 -->
    <wd-popup v-model="visible" position="bottom" :close-on-click-modal="false">
      <view class="flex flex-col relative pt-[50rpx] p-x-[50rpx] pb-[64rpx] modal">
        <!-- 关闭按钮 -->
        <wd-icon
          name="close"
          size="30rpx"
          class="absolute top-[20rpx] right-[50rpx]"
          @click="hidePayModal"
        />
        <!-- 价格 -->
        <view
          class="flex justify-center items-baseline pb-[30rpx] font-normal before:text-[40rpx]"
          style="font-size: 64rpx; color: rgba(0, 0, 0, 0.9)"
        >
          {{ choosePrivilege.price }}鱼币
        </view>
        <view
          class="flex justify-center items-baseline pb-[30rpx] font-normal before:text-[40rpx]"
          style="
            font-size: 32rpx;
            color: rgba(0, 0, 0, 0.9);
            border-bottom: 1px solid rgba(121, 121, 121, 0.2);
          "
        >
          当前账户鱼币：
          <text class="text-[#D1302E] font-bold">{{ userStore.userInfo.gold || 0 }}</text>
        </view>
        <view
          class="flex justify-between mt-[40rpx] mb-[25rpx] font-normal"
          style="font-size: 30rpx"
        >
          <text
            style="color: rgba(0, 0, 0, 0.9)"
            class="w-full overflow-ellipsis ellipsis whitespace-nowrap text-center"
          >
            {{ choosePrivilege.days }} {{ choosePrivilege.type === 1 ? '次' : '天' }}特权购买
          </text>
          <text style="color: rgba(0, 0, 0, 0.9)"></text>
        </view>
        <!-- 鱼币支付 -->
        <view
          v-if="isDirectPay"
          @click="submitOrder(PAY_TYPE.CURRENCY)"
          class="flex justify-center items-center w-full h-100rpx rounded-xl bg-#D1302E text-32rpx text-white"
        >
          鱼币支付（{{ choosePrivilege.price }}鱼币）
        </view>
        <!-- 微信支付 -->
        <view
          class="flex justify-between items-center"
          v-else
          @click="submitOrder(PAY_TYPE.WECHAT)"
        >
          <image class="w-[40rpx] h-[40rpx]" src="https://sacdn.850g.com/football/static/wx.svg" />
          <text class="ml-20rpx mr-auto">微信支付</text>
          <view
            class="flex justify-center items-center w-300rpx h-100rpx rounded-xl bg-#D1302E text-32rpx text-white"
          >
            充值并支付{{ choosePrivilege.price }}鱼币
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
  <wd-overlay :show="loading" class="flex justify-center items-center">
    <wd-loading color="#d1302e" />
  </wd-overlay>
  <back />
  <buyAgreement ref="buyAgreementRef" @pass="passAgreement" />
</template>
<script setup lang="ts">
import back from '@/components/back/index.vue'
import { generateOrder } from '@/api/privilegeOrder'
import { requestGiftRetio, getUserInfo, getMatchTypes } from '@/service/userService'
import { getAuthorPrivilegeDetail } from '@/api/author'
import { ORDER_STATUS, PAY_TYPE } from '@/utils/enum'
import { onBridgeReady } from '@/utils/wxPay'
import buyAgreement from '@/components/buyAgreement/index.vue'

import { useUserStore } from '@/store'
import dayjs from 'dayjs'
import { cleanUrl } from '@/utils/sqbPay'
const userStore = useUserStore()
const loading = ref(false)
const authorId = ref()
const visible = ref(false)
const dataInfo = ref<any>({})
const chooseId = ref()
const checkVal = ref(false)
const buyAgreementRef = ref()
const giftRetio = ref(0)
const type = ref(0)
const tabs = ref([
  {
    type: 0,
    name: '包时特权',
  },
  {
    type: 1,
    name: '包次特权',
  },
])
// 新增状态
const showFilterPicker = ref(false)
const selectedFilter = ref('')
const matchTypes = ref([])
const selectMatch = ref(0)

const getMatchName = (type) => {
  const match = matchTypes.value.find((item) => item.id === type)
  return match ? match.name : ''
}
const showAllItems = ref()

const handleTypeChange = (newType: number) => {
  type.value = newType
  selectMatch.value = 0 // 重置 selectMatch 为 0
}
const showItems = computed(() => {
  const showItems = dataInfo.value.privilegeItems?.filter((item) => item.type === type.value)
  if (!chooseId.value && Array.isArray(showItems) && showItems.length > 0) {
    chooseId.value = showItems[0].id
  }
  if (selectMatch.value !== 0) {
    const showItems = dataInfo.value.privilegeItems?.filter(
      (item) => item.type === type.value && item.matchType === selectMatch.value,
    )
    return showItems
  }
  return showItems
})
const showAgreement = () => {
  buyAgreementRef.value.showDialog()
}
const getGiftRetio = async () => {
  const data = await requestGiftRetio()
  giftRetio.value = data
}

const passAgreement = () => {
  checkVal.value = true
}
const choosePrivilege = computed(() => {
  return dataInfo.value.privilegeItems?.find((item) => item.id === chooseId.value)
})

const isDirectPay = computed(() => {
  if (!choosePrivilege.value.price) return true
  const remain = userStore.userInfo.gold || 0
  return !(choosePrivilege.value.price > remain)
})
const getData = () => {
  getAuthorPrivilegeDetail(authorId.value).then((data) => {
    dataInfo.value = data
    if (
      dataInfo.value.privilegeItems &&
      dataInfo.value.privilegeItems.length > 0 &&
      chooseId.value
    ) {
      type.value = dataInfo.value.privilegeItems.find((item) => item.id == chooseId.value).type
    }
  })
}
const goRecharge = () => {
  uni.navigateTo({
    url: `/pages/recharge/index`,
  })
}
function hidePayModal() {
  visible.value = false
}

function handleTabChange({ name }: { name: number }) {
  const first = dataInfo.value.privilegeItems.find((e) => e.type === name)
  if (first) chooseId.value = first.id
}

function showPayModal() {
  if (!checkVal.value) {
    showAgreement()
    return
  }

  if (chooseId.value) visible.value = true
}

/* 支付订单 */
async function submitOrder(type: PAY_TYPE) {
  // if (isNil(payType.value)) {
  //   uni.showToast({
  //     title: '请选择支付方式',
  //   })
  // }

  if (loading.value) return // 上一笔订单正在支付中
  loading.value = true

  try {
    const redirectUrl = cleanUrl(window.location.href)
    const data = await generateOrder(authorId.value, chooseId.value, type, redirectUrl)
    if (!data) return
    const { status, payUrl } = data
    if (type === PAY_TYPE.CURRENCY) {
      // 如果是现金支付
      if (status === ORDER_STATUS.SUCCESS) {
        uni.showToast({
          icon: 'success',
          title: '订单支付成功',
        })
        hidePayModal()
        await updateUserInfo()
      } else if (status === ORDER_STATUS.FAIL) {
        uni.showToast({
          icon: 'fail',
          title: '订单支付失败',
        })
      }
      getData()
    } else {
      if (status === ORDER_STATUS.PENDING) {
        // location.href = payUrl
        // 微信支付参数
        const wxJsapiParams = data.wxJsapiParams
        onBridgeReady(
          wxJsapiParams,
          () => {
            console.log('支付成功')
            setTimeout(() => {
              getData()
              hidePayModal()
            }, 3000)
          },
          () => {
            console.log('支付失败')
          },
        )
        // window.location.href = payUrl
      }
    }
  } finally {
    loading.value = false
  }
}

const updateUserInfo = async () => {
  const data = await getUserInfo()
  userStore.setUserInfo(data)
}

const getMatchType = async () => {
  matchTypes.value = await getMatchTypes()
}

onLoad(async (query) => {
  authorId.value = Number(query.authorId)
  chooseId.value = Number(query.privilegeId)
})
onMounted(() => {
  getMatchType()
  updateUserInfo()
  getData()
  getGiftRetio()
})
</script>
<style lang="scss" scoped>
.item {
  padding-bottom: 30rpx;
  margin: 20rpx;
  background: #fff;
  border: 1px solid rgba(121, 121, 121, 0.2);
  border-radius: 12px;

  .title {
    width: 264rpx;
    height: 62rpx;
    margin: auto;
    line-height: 62rpx;
    color: #fff;
    text-align: center;
    background: #d1302e;
    border-radius: 0px 0px 12px 12px;
  }

  .price {
    margin: 20rpx 0;
    font-size: 58rpx;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.9);
    text-align: center;
  }

  .price-day {
    width: 300rpx;
    padding: 10rpx;
    margin: auto;
    font-size: 30rpx;
    line-height: 30rpx;
    color: rgba(0, 0, 0, 0.4);
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.4);
    border-radius: 8rpx;
  }
}

.item-active {
  padding-bottom: 30rpx;
  margin: 20rpx;
  background: rgba(209, 48, 46, 0.08);
  border: 1px solid #d1302e;
  border-radius: 12px;

  .title {
    width: 264rpx;
    height: 62rpx;
    margin: auto;
    line-height: 62rpx;
    color: #fff;
    text-align: center;
    background: #d1302e;
    border-radius: 0px 0px 12px 12px;
  }

  .price {
    margin: 20rpx 0;
    font-size: 58rpx;
    font-weight: 600;
    color: #d1302e;
    text-align: center;
  }

  .price-day {
    width: 300rpx;
    padding: 10rpx;
    margin: auto;
    font-size: 30rpx;
    line-height: 30rpx;
    color: #d1302e;
    text-align: center;
    background: rgba(209, 48, 46, 0.2);
    border: 1px solid #d1302e;
    border-radius: 8rpx;
  }
}

.desc-title {
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
}

.desc-content {
  margin-top: 10rpx;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.5);
}
/* 新增的筛选栏样式 */
.filter-bar {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.filter {
  margin-left: 20rpx;
  color: #333;
}
/* 调整tabs样式 */
:deep(.wd-tabs__wrap) {
  padding: 0 !important;
  background: transparent !important;
}

:deep(.wd-tab) {
  padding: 12rpx 30rpx !important;
  color: #666 !important;
}

.type-select {
  flex: 1;
  background: #f5f5f5;
  border-radius: 8rpx;
}

:deep(.wd-tab--active) {
  font-weight: bold !important;
  color: #fff !important;
  background: #07c160 !important;
  border-radius: 8rpx;
}
</style>
