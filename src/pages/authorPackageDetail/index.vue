<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '套餐详情',
  },
}
</route>

<template>
  <view class="page-container">
    <!-- 头部区域 -->
    <view class="header">
      <view class="author-banner">
        <view class="author-info">
          <view
            class="author-avatar"
            :style="
              authorInfo?.authorAvatar ? `background-image: url(${authorInfo.authorAvatar})` : ''
            "
          ></view>
          <view class="author-detail">
            <view class="author-name">{{ authorInfo?.authorName || '加载中...' }}</view>
            <view
              class="tags-container"
              v-if="authorInfo && (authorInfo.winCount >= 3 || authorInfo.recentWinCount)"
            >
              <view class="tag red-tag" v-if="authorInfo?.winCount >= 3">
                <view class="tag-text">{{ authorInfo.winCount }}连红</view>
              </view>
              <view class="tag orange-tag" v-if="authorInfo?.recentWinCount">
                <view class="tag-text">{{ authorInfo.recentWinCount }}</view>
              </view>
            </view>
          </view>
          <view class="follow-btn">
            <view class="follow-text">{{ isFollowed ? '已关注' : '关注' }}</view>
          </view>
        </view>
        <view class="author-desc">
          {{
            authorInfo?.intro ||
            '简介:串串捕手，专业串子推手!基础d串+搏击串+超级串，总有一款适合你!'
          }}
        </view>
      </view>
    </view>

    <view v-if="privilegeInfo?.hasBuy && privilegeInfo?.showPicUrl != '<p></p>' && privilegeInfo?.showPicUrl != '<p><br></p>'" class="p-20rpx bg-#fafafa rounded-12rpx overflow-hidden">
      <view class="p-[10rpx] bg-[#59C76AFF]  bg-opacity-20"
            style="border-top-left-radius: 12rpx;border-top-right-radius: 12rpx">
        <text class="ml-20rpx">套餐生效中</text>
      </view>
      <view
        class="p-y-[30rpx] bg-white p-x-[10rpx]"
        style="border: 1rpx solid rgba(121, 121, 121, 0.2);border-bottom-left-radius: 12rpx;border-bottom-right-radius: 12rpx"
        v-html="privilegeInfo?.showPicUrl"
      />
    </view>

    <!-- 内容区域 -->
    <view class="content" v-if="privilegeInfo?.type === 4">
      <scroll-view
        :show-scrollbar="false"
        scroll-y
        class="content-scroll"
        :style="{ maxHeight: `calc(${contentHeight}px - 38rpx)` }"
      >
        <view class="catalog-card">
          <view class="catalog-header">
            <view class="catalog-indicator"></view>
            <view class="catalog-title-container">
              <view class="catalog-title">目录</view>
              <view class="catalog-subtitle">
                共{{ articleTotal }}篇方案，每{{ privilegeInfo?.date || 0 }}天更新{{
                  privilegeInfo?.num || 0
                }}篇方案
              </view>
            </view>
          </view>

          <!-- 文章列表项 -->
          <view class="article-list">
            <view
              class="article-item"
              v-for="(article, index) in articleList"
              :key="article.id"
              @click="navigateToArticleDetail(article.id)"
            >
              <view class="article-title">
                {{ article.title }}
              </view>
              <!-- 显示标签，如果有的话 -->
              <view class="match-tag" v-if="article.schemePlay">
                <view class="tag-item">
                  <view class="tag-text">{{ getSchemeTypeLabel(article.schemePlay) }}</view>
                </view>
                <!-- 当有比赛信息时显示第一场比赛 -->
                <view
                  class="match-item"
                  v-if="article.articleMatchInfo && article.articleMatchInfo.length > 0"
                >
                  <view class="match-detail">
                    {{ formatMatchTime(article.articleMatchInfo[0].matchTime) }}
                    [{{ article.articleMatchInfo[0].competitionName }}]
                    {{ article.articleMatchInfo[0].homeTeamName }} vs
                    {{ article.articleMatchInfo[0].awayTeamName }}
                  </view>
                </view>
              </view>
              <view class="article-info">
                <view class="article-meta">
                  <view class="publish-time">{{ formatPublishTime(article.createTime) }}</view>
                  <view class="purchase-price">
                    <view class="purchase-count">
                      {{ article.count + (article.initBuyCount || 0) }}人购买
                    </view>
                    <image
                      class="point-icon"
                      src="https://lanhu-oss-2537-2.lanhuapp.com/SketchPnge708ff7085953964b96e9974937294581114d42741f482a74105be07bdbeaab4"
                    />
                    <view class="price">{{ article.price }}</view>
                  </view>
                </view>
              </view>
              <view class="divider"></view>
            </view>
            <!-- 加载更多按钮 -->
            <view
              class="load-more"
              v-if="articleList.length < articleTotal"
              @click="loadMoreArticles"
            >
              <view :class="{ 'loading-text': loading }">
                {{ loading ? '加载中...' : '加载更多' }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <view v-else class="m-10rpx shadow-md px-20rpx py-30rpx bg-white rounded">
      <view
        class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
        style="border-bottom: 1rpx solid #f3f3f3"
      >
        <view>套餐名称</view>
        <view class="text-#999999">{{ privilegeInfo?.privilegeName }}</view>
      </view>
      <view
        class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
        style="border-bottom: 1rpx solid #f3f3f3"
      >
        <view>套餐类型</view>
        <view class="text-#999999">{{ privilegeInfo?.type === 1 ? '包次' : '包天' }}套餐</view>
      </view>
      <view
        class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
        style="border-bottom: 1rpx solid #f3f3f3"
      >
        <view>{{ privilegeInfo?.type === '1' ? '次数' : '天数' }}</view>
        <view class="text-[#bc473b]">
          {{ privilegeInfo?.days }}{{ privilegeInfo?.type === '1' ? '次' : '天' }}
        </view>
      </view>
      <view
        class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
        style="border-bottom: 1rpx solid #f3f3f3"
      >
        <view>比赛类型</view>
        <view class="text-#999999">{{ matchTypeFormat(privilegeInfo?.matchType) }}</view>
      </view>
      <view
        class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
        style="border-bottom: 1rpx solid #f3f3f3"
      >
        <view>套餐价格</view>
        <view class="text-#999999">{{ privilegeInfo.price }}元</view>
      </view>
      <view class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx">
        <view>套餐介绍</view>
      </view>
      <view class="py-30rpx px-20rpx bg-#f3f3f3 rounded-12rpx text-#999 text-26rpx min-h-[150rpx]">
        {{ privilegeInfo?.content }}
      </view>
    </view>

    <!-- 底部订阅按钮 - 只有在未购买状态下显示 -->
    <view
      class="subscription-bar"
      :style="{ paddingBottom: `calc(${bottomHeight}px + 20rpx)` }"
      @click="subscribePackage"
    >
      <view class="subscription-content">
        <image class="vip-icon" src="@/static/images/vip.png" />
        <template v-if="privilegeInfo">
          <view v-if="privilegeInfo.articlePrice" class="subscription-text">
            立即订阅套餐，最多可省{{ maxSavingAmount }}元
          </view>
          <view v-else class="subscription-text">立即订阅套餐</view>
        </template>
      </view>
    </view>

    <!-- 支付弹窗 -->
    <wd-popup v-model="visible" position="bottom" :close-on-click-modal="false">
      <view class="flex flex-col relative pt-[50rpx] p-x-[50rpx] pb-[64rpx] modal">
        <!-- 关闭按钮 -->
        <wd-icon
          name="close"
          size="30rpx"
          class="absolute top-[20rpx] right-[50rpx]"
          @click="hidePayModal"
        />
        <!-- 价格 -->
        <view
          class="flex justify-center items-baseline pb-[30rpx] font-normal before:text-[40rpx]"
          style="font-size: 64rpx; color: rgba(0, 0, 0, 0.9)"
        >
          {{ selectedPackage?.price || privilegeInfo?.price }}鱼币
        </view>
        <view
          class="flex justify-center items-baseline pb-[30rpx] font-normal before:text-[40rpx]"
          style="
            font-size: 32rpx;
            color: rgba(0, 0, 0, 0.9);
            border-bottom: 1px solid rgba(121, 121, 121, 0.2);
          "
        >
          当前账户鱼币：
          <text class="text-[#D1302E] font-bold">{{ userStore.userInfo.gold || 0 }}</text>
        </view>
        <view
          class="flex justify-between mt-[40rpx] mb-[25rpx] font-normal"
          style="font-size: 30rpx"
        >
          <text
            style="color: rgba(0, 0, 0, 0.9)"
            class="w-full overflow-ellipsis ellipsis whitespace-nowrap text-center"
          >
            {{ selectedPackage?.name || privilegeInfo?.privilegeName }}
          </text>
          <text style="color: rgba(0, 0, 0, 0.9)"></text>
        </view>
        <!-- 鱼币支付 -->
        <view
          v-if="isDirectPay"
          @click="submitOrder(PAY_TYPE.CURRENCY)"
          class="flex justify-center items-center w-full h-100rpx rounded-xl bg-#D1302E text-32rpx text-white"
        >
          鱼币支付（{{ selectedPackage?.price || privilegeInfo?.price }}鱼币）
        </view>
        <!-- 微信支付 -->
        <view
          class="flex justify-between items-center"
          v-else
          @click="submitOrder(PAY_TYPE.WECHAT)"
        >
          <image class="w-[40rpx] h-[40rpx]" src="https://sacdn.850g.com/football/static/wx.svg" />
          <text class="ml-20rpx mr-auto">微信支付</text>
          <view
            class="flex justify-center items-center w-300rpx h-100rpx rounded-xl bg-#D1302E text-32rpx text-white"
          >
            充值并支付{{ selectedPackage?.price || privilegeInfo?.price }}鱼币
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 子套餐选择弹窗 -->
    <wd-popup v-model="showChildPackages" position="bottom" :close-on-click-modal="true">
      <view class="flex flex-col relative pt-[50rpx] p-x-[50rpx] pb-[64rpx] modal">
        <!-- 关闭按钮 -->
        <wd-icon
          name="close"
          size="30rpx"
          class="absolute top-[20rpx] right-[50rpx]"
          @click="hideChildPackagesModal"
        />
        <!-- 标题 -->
        <view class="flex justify-center font-medium text-[36rpx] mb-[30rpx]">请选择套餐</view>
        <!-- 套餐列表 -->
        <view class="package-list">
          <view
            v-for="(item, index) in privilegeInfo?.children"
            :key="index"
            class="package-item"
            @click="selectChildPackage(item)"
          >
            <view class="package-duration">{{ item.days }}天</view>
            <view class="package-price">{{ item.price }}鱼币</view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- Wot Design Loading -->
    <wd-overlay :show="initialLoading" class="flex justify-center items-center">
      <wd-loading color="#d1302e" />
    </wd-overlay>
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import { ref, computed, onMounted } from 'vue'
import {
  getAuthorInfo,
  followAuthor,
  unfollowAuthor,
  IAuthorInfo,
  getPrivilegeInfo,
  IPrivilegeInfo,
  getPrivilegeArticleList,
  IPrivilegeArticleInfo,
  IPrivilegeArticleListResponse,
  IArticleMatchInfo,
  IPrivilegeChild,
} from '@/api/author'
import { generateOrder } from '@/api/privilegeOrder'
import { ORDER_STATUS, PAY_TYPE, SCHEME_TYPE } from '@/utils/enum'
import { onBridgeReady } from '@/utils/wxPay'
import { cleanUrl } from '@/utils/sqbPay'
import { useUserStore } from '@/store'

const userStore = useUserStore()

// 作者信息
const authorInfo = ref<IAuthorInfo | null>(null)
// 是否已关注作者
const isFollowed = ref(false)
// 作者ID
const authorId = ref<number | null>(null)
// 专辑ID
const packageId = ref<number | null>(null)
// 加载状态
const loading = ref(false)
// 初始加载状态
const initialLoading = ref(true)
// 支付弹窗显示
const visible = ref(false)
// 子套餐选择弹窗
const showChildPackages = ref(false)
// 已购买状态
const isPurchased = ref(false)
// 套餐详情
const privilegeInfo = ref<IPrivilegeInfo | null>(null)
// 选中的子套餐
interface SelectedPackage extends IPrivilegeChild {
  name: string
}
const selectedPackage = ref<SelectedPackage | null>(null)
// 套餐文章列表
const articleList = ref<IPrivilegeArticleInfo[]>([])
// 文章总数
const articleTotal = ref(0)
// 当前页码
const currentPage = ref(1)
// 每页大小
const pageSize = ref(10)

// 内容区域高度
const contentHeight = ref(0)

const sysInfo = uni.getWindowInfo()
// 屏幕底部安全距离
const bottomHeight = ref(sysInfo.safeAreaInsets?.bottom || 0)

// 计算最多可省多少钱
const maxSavingAmount = computed(() => {
  if (!privilegeInfo.value) return 0

  const { num, date, price, articlePrice } = privilegeInfo.value
  // 计算公式: num * articlePrice * date - price
  return num * articlePrice * date - price
})

const matchTypeFormat = (val) => {
  // 0.全部 1.14场 2.任9 3.单关 4.二串一 5.多串一

  if (val === 0) {
    return '全部'
  } else if (val === 1) {
    return '14场'
  } else if (val === 2) {
    return '任9'
  } else if (val === 3) {
    return '单关'
  } else if (val === 4) {
    return '二串一'
  } else if (val === 5) {
    return '多串一'
  }
}

// 页面加载时获取作者信息
onLoad((options) => {
  if (options.curAuthorId) {
    authorId.value = Number(options.curAuthorId)
    fetchAuthorInfo(authorId.value)
  }
  if (options.packageId) {
    packageId.value = Number(options.packageId)
    fetchPrivilegeInfo(packageId.value)
  }
})

// 获取作者信息
const fetchAuthorInfo = async (id: number) => {
  console.log(id, 'id')

  try {
    loading.value = true
    const res = await getAuthorInfo(id)
    console.log(res, 'res')

    authorInfo.value = res
    isFollowed.value = res.attentionStatus === 1

    // 如果是独立的作者请求（没有套餐信息），则在作者信息加载完成后关闭loading
    if (!privilegeInfo.value) {
      initialLoading.value = false
    }
  } catch (error) {
    console.error('获取作者信息失败:', error)
    uni.showToast({
      title: '获取作者信息失败',
      icon: 'none',
    })

    // 出现错误时也关闭loading
    if (!privilegeInfo.value) {
      initialLoading.value = false
    }
  } finally {
    loading.value = false
  }
}

// 获取套餐详情
const fetchPrivilegeInfo = async (id: number) => {
  try {
    loading.value = true
    const res = await getPrivilegeInfo(id)
    privilegeInfo.value = res

    // 检查套餐是否已购买，使用hasBuy字段（1表示已购买）
    isPurchased.value = res.hasBuy === 1

    // 如果套餐接口返回了作者信息但页面没有作者信息，则使用套餐中的作者信息
    if (!authorInfo.value && res.authorId) {
      authorId.value = res.authorId
      // 可能需要再次请求作者详细信息
      fetchAuthorInfo(res.authorId)
    }

    // 获取套餐文章列表
    await fetchArticleList()

    // 初始化数据加载完毕，关闭loading
    initialLoading.value = false
  } catch (error) {
    console.error('获取套餐详情失败:', error)
    uni.showToast({
      title: '获取套餐详情失败',
      icon: 'none',
    })

    // 出现错误时也关闭loading
    initialLoading.value = false
  } finally {
    loading.value = false
  }
}

// 获取套餐文章列表
const fetchArticleList = async (reset = true) => {
  if (!packageId.value) return

  try {
    loading.value = true

    // 如果是重置，则重置页码
    if (reset) {
      currentPage.value = 1
      articleList.value = []
    }

    const params = {
      pageNo: currentPage.value,
      pageSize: pageSize.value,
      privilegeId: packageId.value,
      // 如果有作者ID，也传递
      ...(authorId.value ? { authorId: authorId.value } : {}),
    }

    const res = await getPrivilegeArticleList(params)

    if (reset) {
      articleList.value = res.list
    } else {
      articleList.value = [...articleList.value, ...res.list]
    }

    articleTotal.value = res.total
  } catch (error) {
    console.error('获取套餐文章列表失败:', error)
    uni.showToast({
      title: '获取文章列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 关注/取消关注作者
const handleFollowAuthor = async () => {
  if (!authorId.value) return

  try {
    loading.value = true
    if (isFollowed.value) {
      // 取消关注
      await unfollowAuthor(authorId.value)
      uni.showToast({
        title: '已取消关注',
        icon: 'none',
      })
    } else {
      // 关注
      await followAuthor(authorId.value)
      uni.showToast({
        title: '关注成功',
        icon: 'none',
      })
    }
    // 切换关注状态
    isFollowed.value = !isFollowed.value
  } catch (error) {
    console.error('操作失败:', error)
    uni.showToast({
      title: isFollowed.value ? '取消关注失败' : '关注失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 加载更多文章
const loadMoreArticles = async () => {
  if (loading.value || articleList.value.length >= articleTotal.value) return

  currentPage.value += 1
  await fetchArticleList(false)
}

// 格式化发布时间
const formatPublishTime = (timeString: string) => {
  if (!timeString) return '刚刚发布'

  // 将时间字符串转为Date对象
  const publishTime = new Date(timeString)

  // 简单处理，只显示"发布于xx天前"或"发布于xx小时前"
  const now = new Date()
  const diff = now.getTime() - publishTime.getTime()

  const days = Math.floor(diff / (24 * 60 * 60 * 1000))
  if (days > 0) {
    return `发布于${days}天前`
  }

  const hours = Math.floor(diff / (60 * 60 * 1000))
  if (hours > 0) {
    return `发布于${hours}小时前`
  }

  return '刚刚发布'
}

// 格式化比赛时间
const formatMatchTime = (timestamp: number) => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 查看文章详情
const navigateToArticleDetail = (articleId: number) => {
  uni.navigateTo({
    url: `/pages/detail/index?id=${articleId}`,
  })
}

// 订阅专辑 - 拉起支付弹窗
const subscribePackage = () => {
  // 如果套餐信息不存在或已购买，则不执行任何操作
  // if (!privilegeInfo.value || isPurchased.value) return

  // 如果是组合套餐(type=3)并且有子套餐，则显示子套餐选择弹窗
  if (
    privilegeInfo.value.type === 4 &&
    privilegeInfo.value.children &&
    privilegeInfo.value.children.length > 0
  ) {
    showChildPackages.value = true
  } else {
    // 直接显示支付弹窗
    visible.value = true
  }
}

// 隐藏子套餐选择弹窗
const hideChildPackagesModal = () => {
  showChildPackages.value = false
  selectedPackage.value = null
}

// 选择子套餐
const selectChildPackage = (item: IPrivilegeChild) => {
  selectedPackage.value = {
    ...item,
    name: `${item.days}天套餐`,
  }
  showChildPackages.value = false
  visible.value = true
}

// 隐藏支付弹窗
const hidePayModal = () => {
  visible.value = false
  // 如果关闭支付弹窗，也清空已选择的子套餐
  selectedPackage.value = null
}

// 判断是否可以直接用鱼币支付
const isDirectPay = computed(() => {
  if (!privilegeInfo.value && !selectedPackage.value) return true
  const remain = userStore.userInfo.gold || 0
  const price = selectedPackage.value?.price || privilegeInfo.value?.price || 0
  return !(price > remain)
})

// 提交订单
const submitOrder = async (payType: number) => {
  console.log(payType, 'payType')

  if (!authorId.value || loading.value) return

  // 获取要购买的套餐ID
  const packageIdToUse = selectedPackage.value ? selectedPackage.value.id : packageId.value

  if (!packageIdToUse) {
    uni.showToast({
      icon: 'none',
      title: '套餐信息错误',
    })
    return
  }

  loading.value = true

  try {
    const redirectUrl = cleanUrl(window.location.href)
    console.log('提交订单:', {
      authorId: authorId.value,
      packageId: packageIdToUse,
      payType,
      selectedPackage: selectedPackage.value,
    })
    const data = await generateOrder(authorId.value, packageIdToUse, payType, redirectUrl)

    if (!data) return

    const { status, payUrl } = data

    if (payType === PAY_TYPE.CURRENCY) {
      // 如果是鱼币支付
      if (status === ORDER_STATUS.SUCCESS) {
        uni.showToast({
          icon: 'success',
          title: '订单支付成功',
        })
        await updateUserInfo()
        // 设置为已购买状态
        isPurchased.value = true
      } else if (status === ORDER_STATUS.FAIL) {
        // 如果支付失败但有余额不足提示
        uni.showToast({
          icon: 'none',
          title: '订单支付失败',
        })

        // 如果是鱼币不足，提示用户使用微信支付
        if (data.msg && data.msg.includes('余额不足')) {
          setTimeout(() => {
            uni.showModal({
              title: '提示',
              content: '鱼币余额不足，是否使用微信支付？',
              success: (res) => {
                if (res.confirm) {
                  // 用户点击确定，使用微信支付
                  submitOrder(PAY_TYPE.WECHAT)
                }
              },
            })
          }, 1500)
        }
      }
    } else if (payType === PAY_TYPE.WECHAT) {
      if (status === ORDER_STATUS.PENDING) {
        // 微信支付参数
        const wxJsapiParams = data.wxJsapiParams
        onBridgeReady(
          wxJsapiParams,
          () => {
            console.log('支付成功')
            uni.showToast({
              icon: 'success',
              title: '订单支付成功',
            })
            // 设置为已购买状态
            isPurchased.value = true
          },
          () => {
            console.log('支付失败')
            uni.showToast({
              icon: 'none',
              title: '支付已取消',
            })
          },
        )
      }
    }

    // 无论支付成功或失败，都关闭支付弹窗
    visible.value = false
    // 清空选中的子套餐
    selectedPackage.value = null
    fetchPrivilegeInfo(packageId.value) // 重新获取套餐信息
  } catch (error) {
    console.error('支付失败:', error)
    uni.showToast({
      icon: 'none',
      title: '支付异常，请稍后再试',
    })
  } finally {
    loading.value = false
  }
}

// 更新用户信息（更新鱼币余额）
const updateUserInfo = async () => {
  const userData = await userStore.getUserInfo()
  console.log('更新用户信息成功', userData)
}

// 计算内容区域高度
const calcContentHeight = () => {
  const windowHeight = sysInfo.windowHeight || 0
  const statusBarHeight = sysInfo.statusBarHeight || 0

  const query = uni.createSelectorQuery()

  // 获取头部区域高度
  query.select('.header').boundingClientRect()

  // 获取底部订阅栏高度
  query.select('.subscription-bar').boundingClientRect()

  // 获取整个页面容器高度
  query.select('.page-container').boundingClientRect()

  // 执行查询
  query.exec((res) => {
    if (res && res.length >= 3) {
      const headerHeight = res[0]?.height || 0
      const subscriptionBarHeight = res[1]?.height || 0
      const containerHeight = res[2]?.height || 0

      // 计算内容区域高度 = 容器高度 - 头部高度 - 底部订阅栏高度
      contentHeight.value = containerHeight - headerHeight - subscriptionBarHeight

      console.log('高度计算', {
        headerHeight,
        subscriptionBarHeight,
        bottomHeight,
        statusBarHeight,
        containerHeight,
        contentHeight: contentHeight.value,
      })
    }
  })
}

// 页面加载时计算高度
onMounted(() => {
  // 延迟计算高度，确保DOM已经渲染
  setTimeout(() => {
    calcContentHeight()
  }, 100)

  // 窗口大小变化时重新计算
  uni.onWindowResize(() => {
    calcContentHeight()
  })
})

// 获取彩票类型标签
const getSchemeTypeLabel = (sp: number) => {
  switch (sp) {
    case SCHEME_TYPE.MATCH_LOTTERY:
      return '14场'
    case SCHEME_TYPE.ANY_NINE:
      return '任九'
    case SCHEME_TYPE.SINGLE_GAME_BET:
    case SCHEME_TYPE.TWO_SELECTIONS_PARLAY:
    case SCHEME_TYPE.MULTIPLE_SELECTIONS_PARLAY:
      return '足球'
    case SCHEME_TYPE.TEAM_PARLAY:
    case SCHEME_TYPE.SINGLE:
    case SCHEME_TYPE.MULTI_TEAM_PARLAY:
      return '竞足'
    default:
      return '竞彩'
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  position: relative;
  width: 750rpx;
  height: calc(100vh - 200rpx);
  background-color: #ffffff;
  overflow: hidden;
  overflow-y: auto;

  .header {
    position: relative;
    width: 100%;

    .author-banner {
      height: 230rpx;
      padding: 35rpx 20rpx 0 23rpx;
      background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/a76dfba3464c4107abec66790df127cf_mergeImage.png);
      background-size: 100% 100%;

      .author-info {
        display: flex;
        align-items: flex-start;

        .author-avatar {
          width: 120rpx;
          height: 120rpx;
          background-position: center;
          background-size: cover;
          border: 3rpx solid #ffffff;
          border-radius: 60rpx;
        }

        .author-detail {
          margin: 8rpx 0 16rpx 21rpx;

          .author-name {
            margin-right: 33rpx;
            font-family: PingFangSC-Semibold;
            font-size: 30rpx;
            font-weight: 600;
            line-height: 42rpx;
            color: #ffffff;
            text-align: left;
            white-space: nowrap;
          }

          .tags-container {
            display: flex;
            justify-content: space-between;
            width: 222rpx;
            margin: 20rpx 0 0 1rpx;

            .tag {
              padding: 1rpx 9rpx;
              background-color: #ffb206;
              border-radius: 6rpx;

              .tag-text {
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: PingFangSC-Medium;
                font-size: 24rpx;
                font-weight: 500;
                color: #ffffff;
              }
            }

            .red-tag {
              background-color: #ffb206;
            }

            .orange-tag {
              margin-left: 10rpx;
              background-color: #ff8c89;
            }
          }
        }

        .follow-btn {
          position: relative;
          padding: 6rpx 21rpx 5rpx 21rpx;
          margin: 9rpx 0 63rpx 223rpx;

          &::after {
            position: absolute;
            top: 0;
            left: 0;
            width: 120rpx;
            height: 48rpx;
            content: '';
            border: 1rpx solid #ffffff;
            border-radius: 25rpx;
          }

          .follow-text {
            font-family: PingFangSC-Medium;
            font-size: 26rpx;
            font-weight: 500;
            line-height: 37rpx;
            color: #ffffff;
            text-align: left;
            white-space: nowrap;
          }
        }
      }

      .author-desc {
        width: 687rpx;
        height: 99rpx;
        margin: 17rpx 6rpx 0 14rpx;
        font-family: PingFangSC-Medium;
        font-size: 24rpx;
        font-weight: 500;
        line-height: 33rpx;
        color: #ffffff;
        text-align: left;
      }
    }
  }

  .content {
    box-sizing: border-box;
    width: 100%;
    padding: 19rpx 0;
    background-color: #fafafa;

    .content-scroll {
      width: 100%;
      overflow: hidden;
    }

    .catalog-card {
      position: relative;
      box-sizing: border-box;
      padding: 24rpx 15rpx 2rpx 15rpx;
      margin: 0 20rpx;
      background-color: #ffffff;
      border-radius: 16rpx;
      box-shadow: 0rpx 2rpx 4rpx 0rpx rgba(231, 231, 231, 1);

      .catalog-header {
        display: flex;
        margin-bottom: 25rpx;

        .catalog-indicator {
          width: 4rpx;
          height: 30rpx;
          margin: 9rpx 10rpx 0 0;
          background-color: #cf302c;
        }

        .catalog-title-container {
          display: flex;
          flex-direction: column;

          .catalog-title {
            font-family: PingFangSC-Semibold;
            font-size: 32rpx;
            font-weight: 600;
            line-height: 45rpx;
            color: #333333;
            text-align: left;
            white-space: nowrap;
          }

          .catalog-subtitle {
            margin-top: 13rpx;
            font-family: PingFangSC-Medium;
            font-size: 24rpx;
            font-weight: 500;
            line-height: 33rpx;
            color: #999999;
            text-align: left;
            white-space: nowrap;
          }
        }
      }

      .article-list {
        .article-item {
          box-sizing: border-box;
          padding: 0 15rpx;
          margin-bottom: 20rpx;

          .article-title {
            width: 100%;
            margin-bottom: 10rpx;
            font-family: PingFangSC-Medium;
            font-size: 30rpx;
            font-weight: 500;
            color: #333333;
            text-align: left;
          }

          .match-tag {
            display: flex;
            align-items: center;
            padding: 13rpx 0 13rpx 9rpx;
            margin: 10rpx 4rpx 13rpx 4rpx;
            background-color: #f3f4f5;
            border-radius: 6rpx;

            .tag-item {
              display: inline-block;
              min-width: 50rpx;
              padding: 0 5rpx;
              text-align: center;
              border: 1rpx solid #cf302c;
              border-radius: 16rpx;

              .tag-text {
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: PingFangSC-Medium;
                font-size: 22rpx;
                font-weight: 500;
                color: #cf302c;
              }
            }

            .match-item {
              margin-left: 10rpx;

              .match-detail {
                font-family: PingFangSC-Medium;
                font-size: 24rpx;
                font-weight: 500;
                color: #999999;
                // text-align: left;
                // display: inline-block;
              }
            }
          }

          .article-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 13rpx 0;

            .article-meta {
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 100%;

              .publish-time {
                font-family: PingFangSC-Medium;
                font-size: 22rpx;
                font-weight: 500;
                color: #999999;
              }

              .purchase-price {
                display: flex;
                align-items: center;

                .purchase-count {
                  font-family: PingFangSC-Medium;
                  font-size: 22rpx;
                  font-weight: 500;
                  color: #999999;
                }

                .price {
                  font-family: PingFangSC-Semibold;
                  font-size: 28rpx;
                  font-weight: 600;
                  color: #cf302c;
                }

                .point-icon {
                  width: 28rpx;
                  height: 28rpx;
                  margin-left: 10rpx;
                }
              }
            }
          }

          .divider {
            width: 672rpx;
            height: 2rpx;
            margin: 15rpx 0 20rpx 0;
            background-color: #f3f3f3;
          }

          .match-list {
            .match-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 5rpx 0;
              margin: 12rpx 0;

              .match-detail {
                margin-left: 92rpx;
                font-family: PingFangSC-Medium;
                font-size: 22rpx;
                font-weight: 500;
                line-height: 30rpx;
                color: #999999;
                text-align: left;
              }

              .point-icon {
                width: 28rpx;
                height: 28rpx;
                margin-right: 10rpx;
              }
            }
          }
        }
      }
    }
  }

  .subscription-bar {
    position: fixed;
    bottom: 0rpx;
    left: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 20rpx 0 0 0;
    background-color: #ffffff;
    transform: translateX(-50%);

    .subscription-content {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 640rpx;
      padding: 22rpx 83rpx 20rpx 84rpx;
      background: linear-gradient(180deg, #f2cc81 0%, #e4b566 100%);
      border-radius: 41rpx;

      .vip-icon {
        width: 53rpx;
        height: 26rpx;
        margin-right: 10rpx;
      }

      .subscription-text {
        font-family: PingFangSC-Semibold;
        font-size: 28rpx;
        font-weight: 600;
        color: #844e02;
        text-align: left;
        white-space: nowrap;
        // line-height: 40rpx;
      }
    }
  }

  // 加载更多按钮样式
  .load-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    margin: 20rpx 0;
    font-size: 28rpx;
    color: #666;
    background-color: #f5f5f5;
    border-radius: 10rpx;

    .loading-text {
      color: #999;
    }

    &:active {
      opacity: 0.8;
    }
  }

  // 子套餐弹窗样式
  .package-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 10rpx 0;

    .package-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 300rpx;
      height: 180rpx;
      margin-bottom: 30rpx;
      background-color: #f7f7f7;
      border: 2rpx solid #eaeaea;
      border-radius: 16rpx;

      &:active {
        background-color: #f0f0f0;
        border-color: #cf302c;
      }

      .package-duration {
        margin-bottom: 15rpx;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .package-price {
        font-size: 28rpx;
        font-weight: 500;
        color: #cf302c;
      }
    }
  }
}
</style>
