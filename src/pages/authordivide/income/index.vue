<route lang="json5">
{
  style: {
    navigationBarTitleText: '收入明细',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <!-- <view class="flex justify-between pt-[40rpx]" style="background-color: white">
      <view>
        <view class="justify-center font">总消费人数</view>
        <view class="justify-center font1">{{ info?.totalConsumer }}</view>
      </view>
      <view>
        <view class="justify-center font" style="width: 200rpx">今日消费人数</view>
        <view class="justify-center font1">{{ info?.todayConsumer }}</view>
      </view>
      <view>
        <view class="justify-center font">总收入</view>
        <view class="justify-center font1">{{ info?.totalIncome }}</view>
      </view>
      <view>
        <view class="justify-center font">今日收入</view>
        <view class="justify-center font1">{{ info?.todayIncome }}</view>
      </view>
    </view> -->
    <view class="py-2 calendar-container">
      <wd-calendar
        type="daterange"
        v-model="curDate"
        allow-same-day
        @confirm="changeDate"
        :required="false"
        placeholder="请选择日期"
      />
      <wd-icon
        v-if="curDate.length > 0"
        name="close"
        size="12"
        custom-class="clear-button"
        @click="clearSelectedDate"
      ></wd-icon>
    </view>
    <view class="jump">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view class="jp-item" v-for="(item, index) in dataList" :key="index" v-else>
        <view class="px-2 w-full">
          <view class="flex justify-between w-full">
            <text class="title w-[550rpx] overflow-ellipsis ellipsis whitespace-nowrap">
              {{ item.title }}
            </text>
            <text class="amount-add" v-if="item.authorDivide > 0">+{{ item.authorDivide }}</text>
            <text class="amount-sub" v-else>{{ item.authorDivide }}</text>
          </view>
          <view class="flex justify-between">
            <text class="buyer">{{ item.customerName ? `购买人：${item.customerName}` : '' }}</text>
            <text class="time">{{ dayjs(item.createTime).format('M月D日 HH:mm') }}</text>
          </view>
        </view>
      </view>
      <wd-loadmore
        custom-class="loadmore"
        :state="loadmoreState"
        @reload="loadmore"
        v-if="dataList.length < total"
      />
    </view>
  </view>
  <back />
</template>
<script lang="ts" setup>
// import { getBalanceLogsData, getIncomeInfo } from '@/service/userService'
import { getIncomeOrderLogsData } from '@/service/userService'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'
const dataList = ref([])
const curDate = ref<number[]>([])
const loadmoreState = ref('finished')
const total = ref(0)
const params = reactive({
  pageNo: 1,
  pageSzie: 10,
  date: '',
  endDate: '',
})
const changeDate = () => {
  params.pageNo = 1
  dataList.value = []
  getData()
}
// const info = ref({
//   totalIncome: 0,
//   totalConsumer: 0,
//   todayConsumer: 0,
//   todayIncome: 0,
// })
const clearSelectedDate = () => {
  curDate.value = []
  dataList.value = []
  getData()
}
const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  params.pageNo = 1
  getData()
})
const getData = async () => {
  if (curDate.value.length > 0) {
    params.date = dayjs(curDate.value[0]).format('YYYY-MM-DD')
    params.endDate = dayjs(curDate.value[1]).format('YYYY-MM-DD')
  } else {
    params.date = ''
    params.endDate = ''
  }

  const data = await getIncomeOrderLogsData(params)

  total.value = data.total
  loadmoreState.value = 'finished'
  if (isRefresh.value) {
    dataList.value = data.list
    //提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  } else {
    if (Array.isArray(data.list)) {
      dataList.value = [...dataList.value, ...data.list]
    }
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

const loadmore = () => {
  setTimeout(() => {
    params.pageNo = params.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}
// const getInfo = async () => {
//   const data = await getIncomeInfo()
//   info.value.totalIncome = data.totalIncome
//   info.value.totalConsumer = data.totalConsumer
//   info.value.todayConsumer = data.todayConsumer
//   info.value.todayIncome = data.todayIncome
// }

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onMounted(() => {
  // getInfo()
  getData()
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-icon-arrow-right) {
  display: none !important;
}

.jump {
  min-height: 80vh;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 5rpx;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid rgb(236, 234, 234);
    border-radius: 10rpx;

    .title {
      font-size: 28rpx;
      font-weight: 600;
      color: #555;
    }

    .amount {
      font-size: 28rpx;
      font-weight: 600;

      &-add {
        color: #f0883a;
      }

      &-sub {
        color: #34d19d;
      }
    }

    .time {
      font-size: 24rpx;
      line-height: 50rpx;
      color: #999;
    }

    .buyer {
      font-size: 24rpx;
      line-height: 50rpx;
      color: #999;
    }
  }
}

.font {
  flex: none;
  flex-grow: 0;
  align-self: stretch;
  order: 0;
  width: 170rpx;
  height: 42rpx;
  font-family: 'PingFang SC';
  font-size: 30rpx;
  line-height: 30rpx;
  color: rgba(0, 0, 0, 0.5);
  text-align: center;
}

.font1 {
  flex: none;
  flex-grow: 0;
  align-self: stretch;
  order: 1;
  width: 170rpx;
  height: 56rpx;
  font-family: 'PingFang SC';
  font-size: 45rpx;
  line-height: 56rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
  margin-top: 15rpx;
}

.calendar-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

:deep() {
  .clear-button {
    position: absolute;
    top: 50%;
    right: 10px;
    cursor: pointer;
    transform: translateY(-50%);
  }
}
</style>
