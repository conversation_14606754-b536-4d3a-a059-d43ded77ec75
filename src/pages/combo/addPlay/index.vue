<route lang="json5">
{
  style: {
    navigationBarTitleText: '添加方案',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="add-plan-wrap p-24rpx">
    <view
      class="mb-[24rpx] rounded-[10rpx] overflow-hidden shadow-[5rpx_5rpx_10rpx_0px_rgba(0,0,0,0.05)]"
    >
      <wd-calendar v-model="selectDate" type="date" label="日期选择" @confirm="handleDateConfirm" />
    </view>
    <!-- 列表 -->
    <view
      class="add-plan-list rounded-[10rpx] padding-[20rpx] mb-[24rpx] shadow-[5rpx_5rpx_10rpx_0px_rgba(0,0,0,0.05)]"
    >
      <wd-checkbox-group v-model="articleIds">
        <view class="list-item" v-for="item of articleList" :key="item.id">
          <!-- <image
            src="https://img11.360buyimg.com/imagetools/jfs/t1/143248/37/5695/265818/5f3a8546E98d998a4/745897ca9c9e474b.jpg"
            width="40"
            height="40"
            class="item-img"
          /> -->
          <view class="item-cont">
            <view
              class="font-size-[28rpx] text-black overflow-hidden text-ellipsis whitespace-nowrap"
            >
              {{ item.title }}
            </view>
            <view class="font-size-[24rpx] text-gray">
              {{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}
            </view>
          </view>
          <wd-checkbox :modelValue="item.id" class="item-checkbox"></wd-checkbox>
        </view>
      </wd-checkbox-group>
    </view>

    <view class="fixed bottom-[24rpx] inset-x-0 flex justify-center">
      <wd-button size="large" block @click="handleSubmit" :disabled="articleIds.length === 0">
        确定添加
        <text v-show="articleIds.length">({{ articleIds.length }})</text>
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { isEmpty } from 'lodash-es'
import { format } from '@/utils/format'
import { useMessage } from 'wot-design-uni'
import { formatDate } from '@/utils/format'
import dayjs from 'dayjs'
import func from 'vue-temp/vue-editor-bridge'
import { privilegeArticleList, addPrivilegeArticle } from '@/api/combo'
const minDate = computed(() => dayjs().startOf('year').valueOf())
// const minDate = computed(() => (dayjs().subtract(1, 'year')).format('YYYY-MM-DD'))
const privilegeId = ref<null | string>(null)
const articleIds = ref<string[]>([])
const articleList = ref<string[]>([])
const comfirmMessage = useMessage()
const selectDate = ref<number>(null)
async function handleDateConfirm({ value }) {
  let date = dayjs(value).format('YYYY-MM-DD')
  console.log('date: ', date)
  const testArticleList = await privilegeArticleList(privilegeId.value, date)
  articleList.value = testArticleList
}
const value = ref<string>('')
const handleSubmit = async () => {
  const confirm = async () => {
    const res = await addPrivilegeArticle({
      articleIds: articleIds.value,
      privilegeId: privilegeId.value,
    })
    uni.showToast({
      title: '操作成功',
      icon: 'none',
    })
    setTimeout(() => {
      uni.navigateTo({
        url: `/pages/combo/comboSet/index?id=${privilegeId.value}`,
      })
    }, 1000)
    // uni.navigateBack()
  }

  comfirmMessage
    .confirm({
      title: '确定添加',
      msg: `确认要添加这${articleIds.value.length}条方案吗？`,
    })
    .then(() => {
      confirm()
    })
    .catch(() => {
      console.log('取消')
    })
}

onLoad(async ({ id }) => {
  privilegeId.value = id
  uni.showLoading({ title: '加载中...' })
  const [testArticleList] = await Promise.all([privilegeArticleList(id)])
  articleList.value = testArticleList
  uni.hideLoading()
})
</script>

<style scope lang="scss">
.add-plan-wrap {
  background: #f5f5f5;
}
.add-plan-list {
  padding: 16rpx;
  background: #fff;
  .list-item {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 20rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    border-bottom: dashed 1px #e9e9e9;
    &:last-child {
      margin-bottom: 0;
      border-bottom: 0;
    }
    .item-img {
      display: inline-block;
      width: 80rpx;
      height: 80rpx;
      margin-right: 24rpx;
      border-radius: 10rpx;
    }
    .item-cont {
      display: block;
      width: 90%;
    }
    .item-checkbox {
      display: block;
      width: 10%;
      min-height: 80rpx;
      margin: 0 !important;
      line-height: 80rpx;
      text-align: right;
    }
  }
}
.datetime-picker-center {
  :deep(.wd-picker__field) {
    text-align: center;
  }
}
</style>
