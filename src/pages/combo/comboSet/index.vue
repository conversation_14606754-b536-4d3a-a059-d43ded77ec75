<route lang="json5">
{
  style: {
    navigationBarTitleText: '套餐设置',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="combo-wrap">
    <view class="top text-white relative" :style="{ backgroundImage: `url(${defaultTopBg})` }">
      <text class="text-40rpx">{{ article && article.privilegeName }}</text>
      <text class="leading-40rpx intro text-center">{{ article.content }}</text>
      <image :src="article.qrcode" class="w-300rpx h-300rpx rounded-xl" />
      <view class="flex justify-center mt-80rpx">
        <view class="flex items-center">
          <image :src="article.avatarUrl" class="w-60rpx h-60rpx rounded-full" />
          <text class="ml-20rpx text-32rpx">{{ article.authorName }}</text>
        </view>
        <view class="flex items-center ml-80rpx" v-if="article.type !== 4">
          <image
            src="https://sacdn.850g.com/football/static/gold2.svg"
            class="w-60rpx h-60rpx rounded-full"
          />
          <text class="ml-20rpx text-32rpx" v-if="article.price">{{ article.price }}</text>
          <!-- <text class="ml-20rpx text-32rpx" v-else>{{ '免费' }}</text> -->
        </view>
        <view class="flex items-center ml-80rpx" v-else>
          <image
            src="https://sacdn.850g.com/football/static/gold2.svg"
            class="w-60rpx h-60rpx rounded-full"
          />
          <text class="ml-20rpx text-32rpx">{{ article.children[0].price }}</text>
        </view>
      </view>
      <view
        @click="handleShowBgChange()"
        class="changebg absolute bg-opacity-20 bottom-20rpx right-31rpx"
      >
        <image
          class="w-19rpx h-17rpx mr-5rpx"
          src="@/static/images/changebg.png"
          mode="scaleToFill"
        />
        <text class="text-20rpx">更换背景</text>
      </view>
    </view>

    <view class="p-24rpx">
      <!-- 编辑与套餐详情 -->
      <view class="flex justify-between pt-10rpx pb-30rpx opt-group">
        <wd-button class="edit-opt" @click="goEdit">编辑</wd-button>
        <wd-button class="detail-opt" @click="gotoPackageDetail">套餐详情</wd-button>
      </view>
      <!-- 设置推送，添加，置顶 -->
      <view class="combo-icons rounded-[10rpx] mb-24rpx">
        <wd-grid :column="4">
          <wd-grid-item icon="setting1" text="设置推送" @click="pushArticle" />
          <wd-grid-item icon="folder-add" text="添加方案" @click="gotoAddPlay" />
          <wd-grid-item
            :icon="article.top == 1 ? 'download1' : 'upload'"
            :text="article.top == 1 ? '取消置顶' : '置顶套餐'"
            @click="changeTop(article.top)"
          />
          <wd-grid-item icon="delete1" text="删除套餐" @click="handleDelPrivilegeSet" />
          <wd-grid-item icon="scan1" text="折扣二维码" @click="handleShowZhekouModal" />
          <wd-grid-item
            :icon="article.isBuy == 1 ? 'shop' : 'stop-circle'"
            :text="article.isBuy == 1 ? '开启售卖' : '停止售卖'"
            @click="handleChangeStatus(article.isBuy)"
          />
          <wd-grid-item icon="share" text="分享套餐" @click="goShare" />
          <wd-grid-item icon="browse" text="设置公开" @click="publishArticle" />
        </wd-grid>
      </view>

      <wd-row class="bg-white p-24rpx rounded-[10rpx] font-size-[24rpx] mb-24rpx">
        <wd-col :span="12">
          <view class="text-gray">
            <text class="text-red">{{ statistics.countNum || 0 }}</text>
            人查看 | 售出
            <text class="text-red">{{ statistics.buyCount || 0 }}</text>
            份
          </view>
        </wd-col>
        <wd-col :span="8">
          <text class="text-gray">总收益:</text>
          <text class="text-red">{{ statistics.income || 0 }}</text>
          <text class="text-gray">元</text>
        </wd-col>
        <wd-col :span="4" class="text-right">
          <wd-checkbox v-model="yigou" shape="square" @change="handlePurchasedClick(1)">
            <text class="text-orange">已购</text>
          </wd-checkbox>
        </wd-col>
      </wd-row>

      <!-- 列表 -->
      <wd-status-tip image="content" tip="暂无数据" v-show="!yonghuList.length" />
      <view class="combo-list rounded-[10rpx] padding-[20rpx]" v-show="yonghuList.length > 0">
        <view class="list-item" v-for="item of yonghuList" :key="item.id">
          <image :src="item.avatarUrl" width="40" height="40" class="item-img" />
          <view class="item-cont">
            <view class="font-size-[28rpx] text-black">{{ item.nickname }}</view>
            <view class="font-size-[24rpx] text-gray">
              <text>{{ item.isBuy === 0 ? '查看时间: ' : '购买时间: ' }}</text>
              <text>{{ item.createTime }}</text>
            </view>
            <view v-if="item.isBuy === 1" class="font-size-[24rpx] text-gray">
              <text>{{ item.type === 1 ? '剩余次数: ' : '到期时间: ' }}</text>
              <text class="text-red">
                {{ item.type === 1 ? item.remainCount + '次' : item.expireTime }}
              </text>
            </view>
          </view>
          <view class="item-status font-size-[28rpx] text-gray" v-show="!item.isBuy">未购买</view>
          <view class="item-action font-size-[28rpx] text-gray" v-show="item.isBuy">
            <text class="text-warning">¥{{ item.price || 0 }}</text>
            <!-- 只有未退款时才显示退款按钮 -->
            <view
              class="item-action-btn"
              @click="handleShowTuikuanModal(item)"
              v-if="!item.isRefund"
            >
              退款
            </view>
            <!-- 如果已退款则显示已退款状态 -->
            <view class="item-action-status" v-else>已退款</view>
          </view>
        </view>
      </view>
      <!-- 分页 -->
      <view>
        <wd-pagination
          v-model="pageNo"
          :total="totalPage"
          :page-size="DEFAULT_PAGE_SIZE"
          @change="handlePageChange"
          show-icon
          hide-if-one-page
          v-show="totalPage > 10"
        />
      </view>
    </view>
  </view>
  <back :goHome="true" />

  <!-- 更换背景 -->
  <wd-popup
    v-model="bgChangeShow"
    position="bottom"
    closable
    custom-style="padding: 45rpx;border-top-left-radius:16rpx;border-top-right-radius:16rpx;"
  >
    <view class="text-32rpx font-bold">选择更换背景</view>
    <view class="">
      <scroll-view
        :show-scrollbar="false"
        scroll-x
        class="whitespace-nowrap py-20rpx gap-30rpx px-10rpx"
      >
        <view v-for="(img, index) in bgList" :key="index" class="relative inline-block mx-10rpx">
          <image
            :src="img.url"
            class="w-200rpx h-200rpx rounded-lg"
            :class="{ 'border-6rpx border-solid border-#FF0000': defaultTopBg === img.url }"
            @click="defaultTopBg = img.url"
            @error="showNameMap[index] = true"
          />
          <view
            v-if="showNameMap[index]"
            class="absolute inset-0 bg-gray-100/80 flex items-center justify-center text-24rpx p-10rpx break-all"
          >
            {{ img.name }}
          </view>
        </view>
      </scroll-view>
    </view>
  </wd-popup>

  <!-- 生成折扣二维码 -->
  <wd-popup
    v-model="showZhekouModal"
    custom-style="border-top-left-radius:32rpx;border-top-right-radius:32rpx;"
    position="bottom"
    closable
    :close-on-click-modal="false"
    @close="handleCloseTuisongModal"
    class="popup-wrap"
  >
    <view class="popup-title">生成折扣二维码</view>
    <view class="popup-cont">
      <wd-form ref="zhekouFormRef" :model="zhekouForm">
        <view class="font-size-[28rpx]">
          <text class="text-red pr-6rpx">*</text>
          套餐天数
        </view>
        <wd-input
          label=""
          label-width="100px"
          prop="days"
          clearable
          v-model="zhekouForm.days"
          placeholder="请输入套餐天数"
          type="number"
          :rules="[
            { required: true, message: '请输入套餐天数' },
            { pattern: /^\d+$/, message: '请输入正确的套餐天数' },
          ]"
        />
        <view class="font-size-[28rpx] mt-24rpx">
          <text class="text-red pr-6rpx">*</text>
          套餐价格
        </view>
        <wd-input
          label=""
          label-width="100px"
          prop="price"
          clearable
          v-model="zhekouForm.price"
          placeholder="请输入套餐价格"
          type="number"
          :rules="[
            { required: true, message: '请输入套餐价格' },
            { pattern: /^-?\d*\.?\d+$/, message: '请输入正确的套餐价格' },
          ]"
        />
        <view class="font-size-[28rpx] mt-24rpx">
          <text class="text-red pr-6rpx">*</text>
          <text class="pr-10rpx">可购买次数</text>
          <text @click="closeOutside">
            <wd-popover content="链接被购买几次后失效" @change="handleChange" placement="top">
              <wd-icon name="warning" size="16px" />
            </wd-popover>
          </text>
        </view>
        <wd-input
          label=""
          label-width="100px"
          prop="buyCount"
          clearable
          v-model="zhekouForm.buyCount"
          placeholder="请输入可购买次数"
          type="number"
          :rules="[
            { required: true, message: '请输入可购买次数' },
            { pattern: /^\d+$/, message: '请输入正确的可购买次数' },
          ]"
        />
        <!-- <view class="footer mt-24rpx flex items-center">
          <wd-button type="primary" block size="medium" @click="handlePrivilegeSetCreate">
            生成二维码
          </wd-button>
        </view> -->
        <view class="mt-50rpx flex justify-between pt-10rpx pb-30rpx opt-group">
          <wd-button
            classPrefix="fish"
            :icon="qrCodeIcon"
            class="edit-opt"
            @click="handlePrivilegeSetCreate"
          >
            生成二维码
          </wd-button>
          <wd-button
            classPrefix="fish"
            :icon="recordIcon"
            class="record-opt"
            @click="gotoPackageRecord"
          >
            查看记录
          </wd-button>
        </view>
      </wd-form>
    </view>
  </wd-popup>

  <!-- 折扣二维码 -->
  <wd-popup
    v-model="showZhekouQrcodeModal"
    custom-style="border-radius:16rpx;width:80%;"
    closable
    class="qr-popup"
    :close-on-click-modal="false"
  >
    <view class="popup-title">折扣二维码</view>
    <view v-if="base64Data" class="w-80% h-450rpx mx-auto pb-20rpx">
      <image class="w-full h-full" :src="base64Data" mode="scaleToFill" />
    </view>

    <view class="text-center text-22rpx pb-30rpx text-#999">
      <text>长按二维码保存或分享</text>
    </view>

    <!-- <view class="qr-code-btns">
      <wd-button classPrefix="fish" :icon="saveImgIcon" class="record-opt" @click="handleSaveQrCodeClick(zhekouQrcode)">
        保存图片
      </wd-button>
    </view> -->
  </wd-popup>

  <view
    id="saveDiscountPhonto"
    class="position-absolute z-[-1] top-0 left-0 w-full bg-white p-20rpx"
    style="max-width: 600rpx"
  >
    <view
      class="w-80% h-450rpx mx-auto mt-40rpx mb-40rpx border border-solid rounded-[10rpx] border-#999 overflow-hidden"
    >
      <image :src="zhekouQrcode" class="w-full h-full" />
    </view>

    <view class="flex px-20rpx w-80% mx-auto">
      <view class="flex items-center w-[50%]">
        <image class="w-28rpx h-28rpx rounded-xl mr-10rpx" src="@/static/images/discount.png" />
        <text class="font-size-[26rpx] font-semibold">折扣价:</text>
        <text class="font-size-[30rpx] font-bold text-red">{{ zhekouForm.price }}</text>
      </view>
      <view class="w-[50%] text-right">
        <text class="font-size-[26rpx] font-semibold">套餐天数:</text>
        <text class="font-size-[30rpx] font-bold text-red">{{ zhekouForm.days }}天</text>
      </view>
    </view>
  </view>

  <!-- 设置公开/推送弹框 -->
  <wd-popup v-model="visible" position="bottom">
    <view class="pt-30rpx pb-100rpx p-x-30rpx">
      <!-- 请选择公开区间 -->
      <template v-if="modalType === MODAL_TYPE.PUBLISH">
        <view class="mb-10rpx text-32rpx text-black text-opacity-90 text-center">
          请选择公开区间
        </view>
        <wd-form
          ref="publishFormRef"
          :model="publishData"
          :rules="publishData.consumeStatus == 1 ? publishFormRules : null"
        >
          <view class="form-label required">是否消费</view>
          <wd-radio-group
            prop="consumeStatus"
            shape="dot"
            v-model="publishData.consumeStatus"
            inline
          >
            <wd-radio :value="0">全部</wd-radio>
            <wd-radio :value="1">是</wd-radio>
            <wd-radio :value="-1">否</wd-radio>
          </wd-radio-group>
          <template v-if="publishData.consumeStatus === 1">
            <view class="mt-30rpx text-30rpx text-black text-opacity-90">
              最低消费次数（选填）:
            </view>
            <view class="flex items-center">
              <view class="flex-1">
                <wd-input
                  prop="consumeMinNum"
                  class="form-input"
                  placeholder="请输入次数"
                  v-model="publishData.consumeMinNum"
                  :maxlength="7"
                />
              </view>
            </view>
            <view class="mt-30rpx text-30rpx text-black text-opacity-90">
              最低消费金额（选填）:
            </view>
            <view class="flex items-center">
              <view class="flex-1">
                <wd-input
                  prop="consumeMinAmount"
                  custom-class="form-input"
                  placeholder="请输入起始金额"
                  v-model="publishData.consumeMinAmount"
                  :maxlength="7"
                />
              </view>
            </view>
          </template>
          <view class="submit">
            <wd-button type="primary" custom-class="submit-btn" @click="handlePublicSubmit">
              公开
            </wd-button>
          </view>
        </wd-form>
      </template>
      <!-- 请选择推送区间 -->
      <template v-else>
        <view class="popup-title">请选择推送区间</view>
        <scroll-view :show-scrollbar="false" style="max-height: 1000rpx" scroll-y>
          <wd-form ref="pushFormRef" :model="pushData">
            <view
              v-for="(item, index) in pushData"
              :key="index"
              style="margin: 20rpx 0; border-bottom: 1px solid #eee"
              class="popup-cont"
            >
              <view v-if="pickTemplate">
                <view class="flex justify-start items-center">
                  <view class="form-label required">选择模版</view>
                  <text
                    v-if="index == pushData.length - 1"
                    class="color-[rgb(255,0,0)] font-size-24rpx m-l-20rpx push-btn"
                    @click="addPushTemplate(index)"
                  >
                    添加
                  </text>
                  <text
                    v-if="index == pushData.length - 1 && index != 0"
                    class="color-[rgb(255,0,0)] font-size-24rpx m-l-20rpx push-btn"
                    @click="removePushTemplate(index)"
                  >
                    删除
                  </text>
                </view>
                <wd-select-picker
                  :columns="pushTemplateList"
                  value-key="id"
                  label-key="name"
                  v-model="item.templateId"
                  type="radio"
                />
              </view>
              <view class="font-size-[28rpx] pb-[24rpx]">
                <text class="text-red pr-6rpx">*</text>
                推送条件
              </view>
              <wd-radio-group
                :prop="`consumeStatus`"
                shape="dot"
                v-model="item.consumeStatus"
                inline
                class="mb-40rpx pl-[24rpx]"
              >
                <wd-radio :value="0">全部</wd-radio>
                <wd-radio :value="1">购买条件</wd-radio>
                <wd-radio :value="2">已购本方案</wd-radio>
              </wd-radio-group>
              <template v-if="item.consumeStatus === 1">
                <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                  最低消费次数（选填）:
                </view>
                <view class="flex items-center">
                  <view class="flex-1">
                    <wd-input
                      prop="consumeMinNum"
                      class="form-input"
                      placeholder="请输入起始次数"
                      v-model="item.consumeMinNum"
                    />
                  </view>
                </view>
                <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                  最低消费金额（选填）:
                </view>
                <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                  消费金额（选填）:
                </view>
                <view class="flex items-center">
                  <view class="flex-1">
                    <wd-input
                      prop="consumeMinAmount"
                      class="form-input"
                      placeholder="请输入金额"
                      v-model="item.consumeMinAmount"
                    />
                  </view>
                </view>
              </template>
              <view class="font-size-[28rpx] pb-[24rpx] mt-[48rpx]">
                <text class="text-red pr-6rpx">*</text>
                <text>推送时间（当天只可选择3个时间）</text>
                <text
                  class="text-white bg-[#d1302e] rounded-[10rpx] px-[10rpx] py-[4rpx] font-size-24rpx"
                  @click="addPushTime(index)"
                  :style="item.pushTimeList.length >= 3 ? 'opacity: .5' : ''"
                >
                  添加时间
                </text>
              </view>
              <view v-for="(_, i) in item.pushTimeList" :key="i" class="flex items-center">
                <wd-datetime-picker
                  v-model="item.pushTimeList[i]"
                  :minDate="new Date()"
                  custom-class="flex-1"
                />
                <wd-button
                  type="icon"
                  icon="remove"
                  class="remove-btn"
                  @click="removePushTime(index, i)"
                  v-show="item.pushTimeList.length > 1"
                />
              </view>
            </view>
            <view class="submit pb-[36rpx]">
              <wd-button type="primary" custom-class="submit-btn" @click="handleSbmitPush">
                推送
              </wd-button>
            </view>
          </wd-form>
        </scroll-view>
      </template>
    </view>
  </wd-popup>

  <!-- 退款 -->
  <wd-popup
    v-model="isShowTuikuanModal"
    position="bottom"
    closable
    :close-on-click-modal="false"
    @close="handleCloseTuikuanModal"
    class="popup-wrap"
  >
    <view class="popup-title">请选择退款选项</view>
    <view class="popup-cont">
      <view class="pl-[24rpx] pr-[24rpx]">
        <view class="mb-[40rpx]">
          <wd-radio-group
            v-model="tuikuanType"
            shape="dot"
            size="large"
            inline
            @change="channgeTuikuanType"
            class="mb-[24rpx]"
          >
            <wd-radio :value="1" class="mr-[24rpx]">全额退款</wd-radio>
            <!-- 隐藏部分退款选项 -->
            <wd-radio v-if="isCanPartRefund == 1" :value="2">部分退款</wd-radio>
          </wd-radio-group>
        </view>
        <!-- 只有在选择部分退款时才显示输入框 -->
        <wd-input
          v-model="tuikuanJine"
          placeholder="请输入要退款的金额"
          type="number"
          custom-class="border-input"
          v-show="tuikuanType == 2"
        />
        <view class="tuikuan-footer">
          <view class="tuikuan-left">
            <text class="text-[#333]">退款金额：</text>
            <text class="text-red">¥{{ tuikuanJine }}</text>
          </view>
          <view class="tuikuan-right">
            <view class="tuikuan-btn" @click="handleTuikuan">确认退款</view>
          </view>
        </view>
      </view>
    </view>
  </wd-popup>

  <comboShare ref="shareRef" />
</template>

<script lang="ts" setup>
import { isEmpty, values } from 'lodash-es'
import { format } from '@/utils/format'
import back from '@/components/back/index.vue'
import { useMessage, useQueue } from 'wot-design-uni'
import {
  IArticleDetail,
  IArticlePushConfig,
  getArticleDetail,
  getArticlePushConfig,
  updateArticleConsumeInfo,
  updateArticlePushConfig,
  changeTopStatus,
  changeArticleStatus,
  ZheKou,
} from '@/api/article'
import {
  getMemberPrivilege,
  getPrivilegePushConfig,
  updatePrivilegePushConfigs,
  privilegeUpdateConsumeInfo,
  changePrivilegeStatus,
  delPrivilegeSet,
  privilegeSetCreate,
  refundPrivilegeOrder,
  refundPrivilegeOrderPart,
} from '@/api/combo'
import {
  privilegeTopStatus,
  getPublishPrivilegeList,
  getPrivilegePvLog,
  getPrivilegeStatistics,
} from '@/api/member'
import dayjs from 'dayjs'
import { MAX_INTEGER_VALUE } from '@/utils/validator'
import { MODAL_TYPE } from '@/utils/enum'
import { getPushTemplate } from '@/api/mpAccount'
import { useUserStore } from '@/store/user'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
import comboShare from '@/components/comboShare/combo-share-new.vue'
import qrCodeIcon from '@/static/images/qr-code.png'
import recordIcon from '@/static/images/record.png'
import shareImgIcon from '@/static/images/shareImg.png'
import saveImgIcon from '@/static/images/saveImg.png'
import html2canvas from 'html2canvas'

const { closeOutside } = useQueue()
function handleChange({ show }) {
  console.log(show)
}

type IPublishData = Omit<IArticlePushConfig, 'pushTimeList'>

const modalType = ref(MODAL_TYPE.PUBLISH)
const shareRef = ref()
const privilegeId = ref<null | string>(null)
// const route = useRoute()
const article = ref<IArticleDetail>({} as IArticleDetail)
const visible = ref(false)

const yigou = ref(false)
const publishFormRef = ref()
const pushFormRef = ref()
const comfirmMessage = useMessage()
const publishData = ref<IPublishData>({} as IPublishData)
const pushData = ref<IArticlePushConfig[]>([
  { pushTimeList: [], templateId: null },
] as IArticlePushConfig[])

const userStore = useUserStore()

const currentAccountId = computed(() => userStore.userInfo.captivePushAccount)

const pushTemplateList = ref([])

const canEdit = computed(() => {
  if (!article.value) return false
  return article.value.canEdit
})

const bgChangeShow = ref(false)
const showBgList = ref(false)
const showNameMap = ref({})
const defaultTopBg = ref('https://sacdn.850g.com/football/static/scheme/top_bg.png')
const bgList = ref([
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg.png', name: '默认背景' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg1.png', name: '背景1' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg2.png', name: '背景2' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg3.png', name: '背景3' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg4.png', name: '背景4' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg5.png', name: '背景5' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg6.png', name: '背景6' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg7.png', name: '背景7' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg8.png', name: '背景8' },
  { url: 'https://sacdn.850g.com/football/static/scheme/top_bg9.png', name: '背景9' },
])
const handleShowBgChange = () => {
  bgChangeShow.value = true
}

const publishFormRules = reactive({
  // consumeMinNum: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMaxNum: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMinAmount: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMaxAmount: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
})

const pushFormRules = reactive({
  // consumeMinNum: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMaxNum: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMinAmount: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
  // consumeMaxAmount: [NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION, MAX_INTEGER_VALUE],
})

const handleConfirm = ({ value, selectedItems }) => {
  console.log('value', value, selectedItems)
}

// 设置公开
function publishArticle() {
  if (modalType.value != MODAL_TYPE.PUBLISH) modalType.value = MODAL_TYPE.PUBLISH
  visible.value = true
}

// 设置推送
function pushArticle() {
  if (modalType.value != MODAL_TYPE.PUSH) modalType.value = MODAL_TYPE.PUSH
  visible.value = true
}

function gotoDetail() {
  if (!privilegeId.value) return
  uni.navigateTo({
    url: `/pages/detail/index?id=${privilegeId.value}`,
  })
}

function goEdit() {
  if (!privilegeId.value) return
  uni.navigateTo({
    url: `/pages/index/adthorAddPackage?id=${privilegeId.value}`,
  })

  // if (article.value.matchIds) {
  //   uni.navigateTo({
  //     url: `/pages/article/relaese/index?id=${privilegeId.value}`,
  //   })
  // } else {
  //   uni.navigateTo({
  //     url: `/pages/article/relaese/index_old?id=${privilegeId.value}`,
  //   })
  // }
}
function goAddPaid() {
  if (!privilegeId.value) return
  uni.navigateTo({
    url: `/pages/privilege/append/index?id=${privilegeId.value}&type=1&title=${article.value.title}`,
  })
}

function goAddFree() {
  if (!privilegeId.value) return
  uni.navigateTo({
    url: `/pages/privilege/append/index?id=${privilegeId.value}&type=0&title=${article.value.title}`,
  })
}

function goShare() {
  shareRef.value.base64Data = ''
  shareRef.value.showDialog(article.value, defaultTopBg)
}

const goBuyUserInfo = (type) => {
  if (!privilegeId.value) return
  uni.navigateTo({
    url: `/pages/detail/user/index?privilegeId=${privilegeId.value}&type=${type}`,
  })
}

const changeTop = async (topStatus) => {
  if (!privilegeId.value) return
  if (article.value.status !== 2) {
    const data = {
      id: privilegeId.value,
      topStatus: topStatus === 1 ? 0 : 1,
    }

    const res = await privilegeTopStatus(data)

    uni.showToast({
      title: data.topStatus === 1 ? '置顶成功' : '取消置顶成功',
      icon: 'none',
    })
    article.value.top = data.topStatus
  } else {
    uni.showToast({
      title: '该方案已封禁,无法进行置顶操作',
      icon: 'none',
    })
  }
}

const changeStatus = (status) => {
  if (!privilegeId.value) return
  if (article.value.status !== 2) {
    const data = {
      id: privilegeId.value,
      status: status === 0 ? 1 : 0,
    }
    const confirm = async () => {
      const res = await changeArticleStatus(data)
      uni.showToast({
        title: data.status === 1 ? '上架成功' : '下架成功',
        icon: 'none',
      })
      article.value.status = data.status
    }

    if (data.status === 1) {
      confirm()
    } else {
      comfirmMessage
        .confirm({
          title: '下架',
          msg: '确认要下架该方案吗？',
        })
        .then(() => {
          confirm()
        })
        .catch(() => {
          console.log('取消')
        })
    }
  } else {
    uni.showToast({
      title: '该方案已封禁,无法进行上下架操作',
      icon: 'none',
    })
  }
}

function addDefaultTime() {
  const now = new Date()
  return new Date(format(dayjs(now).add(5, 'minute'), 'YYYY-MM-DD HH:mm'))
}

function addPushTime(index) {
  if (pushData.value[index].pushTimeList.length < 3) {
    pushData.value[index].pushTimeList.push(addDefaultTime())
  }
}

function removePushTime(index: number, i: number) {
  if (pushData.value[index].pushTimeList.length > 1) {
    pushData.value[index].pushTimeList.splice(i, 1)
  }
}

const addPushTemplate = (index) => {
  pushData.value.push({
    privilegeId: article.value.id,
    pushTimeList: [addDefaultTime()],
    consumeMaxAmount: null,
    consumeMaxNum: null,
    consumeMinAmount: 0,
    consumeMinNum: 0,
    consumeStatus: 0,
    templateId: null,
  })
}

const removePushTemplate = (index) => {
  pushData.value.splice(index, 1)
}

const pickTemplate = computed(() => {
  if (currentAccountId.value && pushTemplateList.value.length > 0) {
    return true
  }

  return false
})

/* 提交推送 */
function handleSbmitPush() {
  if (!pushFormRef.value) return
  pushFormRef.value.validate().then(({ valid }) => {
    if (!valid) return
    const id = privilegeId.value
    // 提交推送
    if (pickTemplate.value) {
      for (let i = 0; i < pushData.value.length; i++) {
        const { templateId, ...rest } = pushData.value[i]
        if (!templateId) {
          uni.showToast({ title: '请选择推送模版', icon: 'none' })
          return
        }
      }
    } else {
      for (let i = 0; i < pushData.value.length; i++) {
        const { templateId, ...rest } = pushData.value[i]
        if (templateId) {
          pushData.value[i].templateId = null
        }
      }
    }

    // 拷贝 pushData.value
    const req = []
    for (let i = 0; i < pushData.value.length; i++) {
      const { pushTimeList, ...rest } = pushData.value[i]
      const p = {
        privilegeId: id,
        ...rest,
        pushTimeList: pushTimeList.map(
          (t) => new Date(dayjs(t as string).format('YYYY-MM-DD HH:mm')),
        ),
      }
      req.push(p)
    }

    updatePrivilegePushConfigs(req).then((s) => {
      if (s) {
        uni.showToast({ title: '提交成功', icon: 'none' })
        visible.value = false
        getPrivilegePushConfig(parseInt(id as string)).then((pushConfig) => {
          for (const singleConfig in pushConfig) {
            const { pushTimeList, ...restPushConfig } = pushConfig[singleConfig]
            const p = isEmpty(pushTimeList)
              ? [addDefaultTime()]
              : pushTimeList.map((t) => new Date(format(t, 'YYYY-MM-DD HH:mm')))
            pushConfig[singleConfig] = {
              pushTimeList: p,
              ...restPushConfig,
            }
          }

          pushData.value = pushConfig
        })
      }
    })
  })
}

/* 公开 */
function handlePublicSubmit() {
  if (!publishFormRef.value) return
  publishFormRef.value.validate().then(({ valid }) => {
    if (!valid) return
    // const {
    //   query: { id },
    // } = route
    const id = privilegeId.value
    // 提交公开
    privilegeUpdateConsumeInfo(publishData.value).then((s) => {
      if (s) {
        uni.showToast({ title: '提交成功', icon: 'none' })
        visible.value = false
        getArticleDetail(parseInt(id as string)).then((article) => {
          const {
            consumeStatus,
            consumeMinNum,
            // consumeMaxNum,
            consumeMinAmount,
            // consumeMaxAmount,
          } = article

          publishData.value = {
            privilegeId: parseInt(id as string),
            consumeStatus,
            consumeMinNum,
            // consumeMaxNum,
            consumeMinAmount,
            // consumeMaxAmount,
          }
          article.value = article
        })
      }
    })
  })
}

// 折扣
const showZhekouModal = ref(false)
const zhekouFormRef = ref()
const zhekouForm = ref({
  days: 0,
  price: 0,
  buyCount: 0,
})
const zhekouQrcode = ref('')
const showZhekouQrcodeModal = ref(false)
const handlePrivilegeSetCreate = () => {
  zhekouFormRef.value
    .validate()
    .then(async ({ valid, errors }) => {
      if (valid) {
        console.log('校验成功 zhekouForm.value:', zhekouForm.value)
        privilegeSetCreate({ parentId: privilegeId.value, ...zhekouForm.value }).then((resp) => {
          if (resp) {
            uni.showToast({ title: '提交成功', icon: 'none' })
            handleCloseZhekouModal()
            console.log('ccccccccccccccccc resp:', resp)
            zhekouQrcode.value = resp
            base64Data.value = ''
            uni.showToast({
              title: '正在生成图片...',
              duration: 1000,
              icon: 'loading',
            })
            setTimeout(() => {
              handleCreateImg()
            }, 1000)

            // #ifdef H5
            // const link = document.createElement('a')
            // link.href = resp
            // link.download = `image_${Date.now()}.png`
            // link.click()
            // copyBase64ToClipboard(resp)
          }
        })
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}

const gotoPackageRecord = () => {
  uni.navigateTo({
    url: `/pages/combo/packageRecord/index?privilegeId=${privilegeId.value}`,
  })
}

function handleShowZhekouModal() {
  showZhekouModal.value = true
}
function handleCloseZhekouModal() {
  showZhekouModal.value = false
}

let base64Data = ref('')
const handleCreateImg = async () => {
  const scoreimg = document.getElementById('saveDiscountPhonto')
  // 使用 html2canvas 将 DOM 元素转换为图片
  const canvas = await html2canvas(scoreimg, {
    scale: window.devicePixelRatio || 2,
    useCORS: true, // 处理跨域问题
    allowTaint: true,
    logging: false,
    backgroundColor: '#FFFFFF',
    width: scoreimg.offsetWidth,
    height: scoreimg.offsetHeight,
    windowWidth: document.documentElement.offsetWidth,
    windowHeight: document.documentElement.offsetHeight,
    onclone: (document) => {
      // 确保克隆的DOM元素样式完全加载
      const clonedElement = document.getElementById('saveDiscountPhonto')
      if (clonedElement) {
        clonedElement.style.position = 'relative'
        clonedElement.style.width = '100%'
        clonedElement.style.maxWidth = '600rpx'
        clonedElement.style.visibility = 'visible'
        clonedElement.style.zIndex = '1'
      }
    },
  })
  // 将 canvas 转换为 Base64 数据
  base64Data.value = canvas.toDataURL('image/png')
  showZhekouQrcodeModal.value = true
}
// const handleSaveQrCodeClick = async () => {
//   console.log('保存图片')
//   try {
//     // 创建一个临时链接
//     const link = document.createElement('a')
//     link.href = base64Data.value
//     link.download = `discount_qrcode_${Date.now()}.png`
//     document.body.appendChild(link)
//     link.click()
//     document.body.removeChild(link)

//     uni.showToast({
//       title: '图片已保存',
//       icon: 'success',
//       duration: 2000,
//     })
//   } catch (error) {
//     console.error('保存图片失败:', error)
//     uni.showToast({
//       title: '保存失败',
//       icon: 'none',
//       duration: 2000,
//     })
//   }
// }

// 推送
const showTuisongModal = ref(false)
function handleShowTuisongModal() {
  showTuisongModal.value = true
}
function handleCloseTuisongModal() {
  showTuisongModal.value = false
}
const tuisongtiaojian = ref()
const pushTimeList = ref([new Date()])
// function addPushTime() {
//   if (pushTimeList.value.length < 3) {
//     pushTimeList.value.push(addDefaultTime())
//   }
// }
// function removePushTime(index: number, i: number) {
//   if (pushTimeList.value.length > 1) {
//     pushTimeList.value.splice(i, 1)
//   }
// }

function gotoAddPlay() {
  if (!privilegeId.value) return
  uni.navigateTo({
    url: `/pages/combo/addPlay/index?id=${privilegeId.value}`,
  })
}

function handleDelPrivilegeSet() {
  const confirm = async () => {
    const res = await delPrivilegeSet(privilegeId.value)
    // console.log('res: ', res)
    uni.showToast({
      title: '操作成功',
      icon: 'none',
    })
    article.value.status = data.status
    uni.navigateBack()
  }

  comfirmMessage
    .confirm({
      title: '删除套餐',
      msg: '确认要删除该套餐吗？',
    })
    .then(() => {
      confirm()
    })
    .catch(() => {
      console.log('取消')
    })
}

function handleChangeStatus(isBuy) {
  const confirm = async () => {
    const res = await changePrivilegeStatus(privilegeId.value)
    console.log('res: ', res)
    uni.showToast({
      title: '操作成功',
      icon: 'none',
    })
    article.value.isBuy = isBuy === 1 ? 0 : 1
  }

  comfirmMessage
    .confirm({
      title: isBuy ? '开启售卖' : '停止售卖',
      msg: `确认要${isBuy ? '开启售卖' : '停止售卖'}该套餐吗？`,
    })
    .then(() => {
      confirm()
    })
    .catch(() => {
      console.log('取消')
    })
}

const yonghuList = ref([])
const pageNo = ref(1)
const totalPage = ref(0)
const pageSize = DEFAULT_PAGE_SIZE
const statistics = ref({
  buyCount: 0,
  countNum: 0,
  income: 0,
})
const getList = async (id) => {
  const [testPvLog, testStatistics] = await Promise.all([
    getPrivilegePvLog(id, pageNo.value, DEFAULT_PAGE_SIZE, yigou.value ? 1 : null),
    getPrivilegeStatistics(id, pageNo.value, DEFAULT_PAGE_SIZE, yigou.value ? 1 : null),
  ])
  yonghuList.value = testPvLog.list
  totalPage.value = testPvLog.total
  statistics.value = testStatistics
}
const handlePurchasedClick = (op: number) => {
  pageNo.value = op
  getList(privilegeId.value)
}
function handlePageChange(value: any) {
  pageNo.value = value.value
  getList(privilegeId.value)
}

// 去往套餐详情
const authorId = ref()
function gotoPackageDetail() {
  uni.navigateTo({
    url: `/pages/index/package?id=${privilegeId.value}&authorId=${article.value.authorId}`,
  })
}

// 退款
const isShowTuikuanModal = ref(false)
const tuikuanType = ref(1)
const tuikuanJine = ref(0)
const currentOrderId = ref(0)
const isCanPartRefund = ref(0)

const handleShowTuikuanModal = async (item: any) => {
  console.log(item, 'item')

  isShowTuikuanModal.value = true
  currentOrderId.value = item.orderId || 0
  // 默认设置为全额退款，金额为订单金额
  tuikuanType.value = 1
  tuikuanJine.value = item.price || 0
  isCanPartRefund.value = item.isCanPartRefund || 0
}
const handleCloseTuikuanModal = () => {
  isShowTuikuanModal.value = false
  currentOrderId.value = 0
  tuikuanJine.value = 0
}
const channgeTuikuanType = () => {
  // 如果是全额退款，则使用原始订单金额
  if (tuikuanType.value === 1) {
    const currentItem = yonghuList.value.find((item) => item.orderId === currentOrderId.value)
    if (currentItem) {
      tuikuanJine.value = currentItem.price || 0
    }
  } else {
    // 如果是部分退款，清空金额让用户输入
    tuikuanJine.value = 0
  }
}
const handleTuikuan = async () => {
  console.log(currentOrderId.value, 'currentOrderId.value')

  if (!currentOrderId.value) {
    uni.showToast({
      title: '订单信息有误',
      icon: 'none',
    })
    return
  }

  if (tuikuanType.value == 2 && tuikuanJine.value <= 0) {
    uni.showToast({
      title: '退款金额必须大于0',
      icon: 'none',
    })
    return
  }

  // 目前仅实现全额退款功能，隐藏部分退款选项
  // if (tuikuanType.value !== 1) {
  //   uni.showToast({
  //     title: '暂不支持部分退款',
  //     icon: 'none',
  //   })
  //   return
  // }

  try {
    let result = null
    if (tuikuanType.value === 1) {
      result = await refundPrivilegeOrder(currentOrderId.value)
    } else {
      result = await refundPrivilegeOrderPart(currentOrderId.value, tuikuanJine.value)
    }
    if (result) {
      uni.showToast({
        title: '退款成功',
        icon: 'success',
      })
      isShowTuikuanModal.value = false
      // 重新获取列表
      getList(privilegeId.value)
    } else {
      uni.showToast({
        title: '退款失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('退款出错:', error)
    uni.showToast({
      title: '退款操作失败',
      icon: 'none',
    })
  }
}

onLoad(async ({ id }) => {
  //
  getList(id)
  privilegeId.value = id
  uni.showLoading({ title: '加载中...' })
  const [articleDetail, pushConfig] = await Promise.all([
    getMemberPrivilege(id),
    getPrivilegePushConfig(id),
  ])

  const { consumeStatus, consumeMinNum, consumeMinAmount, totalArticle } = articleDetail
  publishData.value = {
    privilegeId: parseInt(id),
    consumeStatus,
    consumeMinNum,
    // consumeMaxNum,
    consumeMinAmount,
    // consumeMaxAmount,
  }

  article.value = articleDetail

  for (const singleConfig in pushConfig) {
    const { pushTimeList, ...restPushConfig } = pushConfig[singleConfig]
    const p = isEmpty(pushTimeList)
      ? [addDefaultTime()]
      : pushTimeList.map((t) => new Date(format(t, 'YYYY-MM-DD HH:mm')))
    pushConfig[singleConfig] = {
      pushTimeList: p,
      ...restPushConfig,
    }
  }
  console.log('pushConfig', pushConfig)
  pushData.value = pushConfig

  const pushTemplateRes = await getPushTemplate(currentAccountId.value)
  pushTemplateList.value = pushTemplateRes

  uni.hideLoading()
})

onShow(async () => {
  console.log('onShow：', article.value.id)
  if (article.value.id) {
    const [articleDetail] = await Promise.all([getMemberPrivilege(article.value.id)])
    article.value = articleDetail
  }
})
</script>

<style scoped lang="scss">
.top {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 690rpx;
  padding: 50rpx 30rpx 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.intro {
  display: -webkit-box;
  width: 100%;
  margin: 30rpx 0 50rpx;
  overflow: hidden;
  font-size: 32rpx;
  line-height: 40rpx;
  color: white;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.opt-group {
  [class$='-opt'] {
    width: 330rpx;
    height: 80rpx !important;
    margin: unset;
    font-size: 30rpx !important;
    border-radius: 12rpx !important;
  }

  .detail-opt {
    color: #d1302e;
    background-color: #fff;
    border: 1px solid #d1302e;
  }

  .record-opt {
    color: #999999;
    background-color: #fff;
    border: 1px solid #dddddd;
  }

  .edit-opt,
  .record-opt {
    :deep(.wd-icon__image) {
      margin-top: -60rpx;
    }
  }
}

.push-btn {
  padding: 6rpx 25rpx;
  border: 1px solid red;
  border-radius: 12rpx;
}

.setting {
  display: flex;
  justify-content: space-between;
  padding: 34rpx 78rpx 38rpx;
  margin: 0 30rpx;
  border: 1rpx solid rgba(121, 121, 121, 0.2);
  border-style: solid none;

  > view {
    display: flex;
    flex-direction: column;
    row-gap: 30rpx;
  }
}

.form-label {
  margin-bottom: 10rpx;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.9);

  &.required {
    &::after {
      display: inline-block;
      color: #d1302e;
      content: '*';
    }
  }
}

:deep() {
  .submit {
    display: flex;
    justify-content: center;

    .submit-btn {
      width: 305rpx;
      height: 80rpx;
      margin: 30px auto 0;
      border-radius: 12rpx;
    }
  }
}

:deep() {
  .add-btn,
  .remove-btn {
    width: 40rpx !important;
    height: 40rpx !important;
    border: 1px solid rgba($color: #000000, $alpha: 0.3);
  }

  .add-btn {
    margin: 0 20rpx;
  }
}

.combo-wrap {
  background: #f5f5f5;

  .changebg {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 130rpx;
    height: 34rpx;
    background-color: rgba(0, 0, 0, 0.2);
    border: 1rpx solid rgb(177, 175, 175);
    border-radius: 8rpx;
  }
}

.combo-icons {
  overflow: hidden;
  background: #fff;
}

.combo-list {
  padding: 16rpx;
  background: #fff;

  .list-item {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 20rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    border-bottom: dashed 1px #e9e9e9;

    &:last-child {
      border-bottom: 0;
    }

    .item-img {
      display: inline-block;
      width: 80rpx;
      height: 80rpx;
      margin-right: 24rpx;
      border-radius: 10rpx;
    }

    .item-cont {
      display: block;
      width: 70%;
    }

    .item-status {
      display: block;
      width: calc(30% - 80rpx);
    }

    .item-action {
      display: block;
      width: calc(30% - 80rpx);
      text-align: center;
    }

    .item-action-btn,
    .item-action-status {
      width: 80%;
      margin: 0 auto;
      color: #d2302e;
      text-align: center;
      border: solid 1px #d2302e;
      border-radius: 6rpx;
    }

    .item-action-status {
      color: #999;
      background-color: #f8f8f8;
      border-color: #ddd;
    }
  }
}

.popup-wrap {
  .popup-title {
    padding: 24rpx 36rpx;
    font-size: 36rpx;
    font-weight: bold;
    // text-align: center;
  }

  .popup-cont {
    padding: 20rpx;

    :deep(.uni-input-input) {
      padding: 0 20rpx;
    }

    :deep(.uni-input-placeholder) {
      padding: 0 20rpx;
    }

    :deep(.wd-radio) {
      margin-right: 48rpx;
    }
  }
}

.qr-popup {
  width: 80%;

  .popup-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 248rpx;
    height: 61rpx;
    margin: 0 auto;
    font-size: 32rpx;
    font-weight: 600;
    color: #fff;
    background-color: #d2302e;
    border-radius: 0rpx 0rpx 30.5rpx 30.5rpx;
  }

  .qr-code-btns {
    box-sizing: border-box;
    display: flex;
    gap: 20rpx;
    padding: 40rpx 30rpx;

    .record-opt,
    .edit-opt {
      flex: 1 1 0;
      min-width: auto;
      height: 64rpx;
      margin: 0 auto;
      color: #d2302e;
      text-align: center;
      background-color: #ffecee;
      border: solid 1px #d2302e;
      border-radius: 6rpx;

      &.edit-opt {
        color: #fff;
        background-color: #d2302e;
        border: solid 1px #d2302e;
      }
    }

    :deep(.wd-icon__image) {
      width: 25rpx !important;
      height: 25rpx !important;
      margin-top: -60rpx;
    }
  }
}

.border-input {
  padding: 10rpx 18rpx;

  margin-bottom: 24rpx;
  background: #f9f9f9;
  border: solid 1px #ddd !important;
  border-radius: 10rpx;

  &::after {
    display: none;
  }

  :deep(.wd-input__inner) {
    font-size: 32rpx;
  }
}

.tuikuan-footer {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;

  .tuikuan-left {
    display: inline-block;
    width: 50%;
    height: 80rpx;
    margin-right: 24rpx;
    line-height: 72rpx;
    border-radius: 10rpx;
  }

  .tuikuan-right {
    display: block;
    width: 50%;
  }

  .tuikuan-btn {
    width: 100%;
    height: 72rpx;
    line-height: 72rpx;
    color: #fff;
    text-align: center;
    background: #d2302e;
    border-radius: 8rpx;
  }
}
</style>
