<route lang="json5">
  {
    style: {
      navigationBarTitleText: '套餐记录',
      enablePullDownRefresh: true,
    },
  }
  </route>
<template>
  <view class="bg-#f4f8fa min-h-screen p-20rpx">
    <!-- 套餐列表 -->
    <view class="flex flex-col gap-20rpx">
      <!-- 套餐卡片 -->
      <view v-for="(item, index) in comboList" :key="index" class="bg-white rounded-12rpx shadow-sm overflow-hidden">
        <!-- 卡片头部 -->
        <view class="flex items-center p-25rpx">
          <view class="w-100rpx h-100rpx mr-16rpx p-15rpx border border-solid border-#f5f5f5 box-border rounded-md">
            <image class="w-full h-full" :src="qrCodeIcon" mode="aspectFit" />
          </view>
          <view class="">
            <view class="flex-1 flex items-center">
              <text class="text-30rpx text-#333 font-bold">套餐价格：</text>
              <text class="text-32rpx font-bold text-#D1302E">{{ item.price }}</text>
            </view>
            <text class="text-24rpx text-#999">{{ timeFormat(item.createTime) }}</text>
          </view>
        </view>

        <!-- 卡片底部 -->
        <view class="flex items-center justify-between p-16rpx-24rpx bg-[#FAFAFA] m-25rpx mt-0 p-15rpx rounded-md">
          <view class="flex items-center">
            <text class="text-22rpx text-#999">套餐天数：</text>
            <text class="text-22rpx text-#333">{{ item.days || 0 }}天</text>
          </view>
          <view class="h-20rpx w-1rpx bg-#ddd self-center"></view>
          <view class="flex items-center">
            <text class="text-22rpx text-#999">可购买次数：</text>
            <text class="text-22rpx text-#333">{{ item.buyNum || 0 }}次</text>
          </view>
          <view class="h-20rpx w-1rpx bg-#ddd self-center"></view>
          <view class="flex items-center">
            <text class="text-22rpx text-#999">已购买人数：</text>
            <text class="text-22rpx text-#333">{{ item.buyCount || 0 }}人</text>
          </view>
        </view>
      </view>
    </view>


  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import qrCodeIcon from '@/static/images/qr-code-2.png'
import { useRoute } from 'vue-router'
import { getDiscountPrivilegeList } from '@/api/combo'
import { format } from '@/utils/format'


// 套餐列表数据
const comboList = ref([])

const route = useRoute()

// 获取套餐列表
const fetchComboList = async (): Promise<void> => {
  uni.showLoading({
    title: '加载中...',
    mask: true,
  })
  const res = await getDiscountPrivilegeList(Number(route.query?.privilegeId))
  uni.hideLoading()
  comboList.value = Array.isArray(res) ? res : []
}

const timeFormat = computed(() => {
  return (t: number) => (t ? format(t, 'YYYY-MM-DD HH:mm:ss') : '')
})

// 生命周期钩子
onMounted(() => {
  fetchComboList()
})

// 下拉刷新
onPullDownRefresh(() => {
  fetchComboList().then(() => {
    uni.stopPullDownRefresh()
  }).catch(() => {
    uni.stopPullDownRefresh()
  })
})

</script>

<style>
/* 自定义样式类，用于补充UnoCSS不能直接实现的样式 */
.border-bottom {
  border-bottom: 1rpx solid #f5f5f5;
}

.shadow-sm {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>