<template>
  <!-- <wd-popup v-model="visible" position="top" custom-class="match-filter" custom-style="height:fit-content;" -->
  <wd-popup v-model="visible" position="top" custom-class="match-filter" @close="handleClose">
    <wd-loading color="#d1302e" v-if="loading" />
    <view v-else class="pt-45px">
      <!-- 筛选结果 -->
      <!-- <view class="flex justify-between px-30rpx border-b-solid border-b-1rpx border-b-black border-b-opacity-10">
        <text v-for="{ name, id } in focused" :key="id" class="flex justify-between items-center h-80rpx text-black"
          :class="typeClazz(id)">
          {{ name }}
        </text>
      </view> -->
      <!-- <view class="flex justify-between px-30rpx border-b-solid border-b-1rpx border-b-black border-b-opacity-10">
        <text v-for="{ name, id } in focused" :key="id" class="flex justify-between items-center h-80rpx text-black"
          :class="typeClazz(id)">
          {{ name }}
        </text>
      </view> -->
      <view class="flex items-center relative pl-20rpx pr-100rpx border-bottom">
        <view class="flex-1 flex flex-nowrap overflow-x-auto px-30rpx">
          <text v-for="{ name, id } in focused" :key="id"
            class="flex-none flex justify-between items-center h-80rpx ml-40rpx first:ml-0 text-black"
            :class="typeClazz(id)">
            {{ name }}
          </text>
        </view>
        <view class="absolute right-40rpx" @click="hidePopup">
          <wd-icon name="arrow-up" size="40rpx" />
        </view>
      </view>
      <!-- 一级赛事分类 -->
      <scroll-view :show-scrollbar="false" scroll-y @touchmove.stop>
        <!-- <view class="overflow-y-auto h-1000rpx"> -->
        <view v-for="continental in categories" :key="continental.id"
          class="border-b-solid border-b-1rpx border-b-#797979 border-b-opacity-20">
          <!-- 一级赛事标题 -->
          <view class="flex justify-between items-center h-100rpx px-30rpx" style="background: rgba(0, 0, 0, 0.02)"
            @click="collapseLevel2(continental.id)">
            <text class="level1-icon">{{ continental.name }}</text>
            <text class="ml-20rpx mr-auto text-28rpx text-black text-opacity-90">
              {{ `${continental.name}赛事` }}
            </text>
            <wd-icon name="arrow-up" size="40rpx" color="#999999" v-if="activeLevel2 === continental.id" />
            <wd-icon name="arrow-down" size="40rpx" color="#999999" v-else />
          </view>
          <template v-if="!isEmpty(continental.children) && activeLevel2 === continental.id">
            <!-- 国际赛事 -->
            <template v-if="continental.id === 1">
              <wd-row :gutter="10">
                <template v-for="{ id, name } in continental.children" :key="id">
                  <!-- 已关注的 -->
                  <wd-col :span="6" v-if="isFocused(id)">
                    <view @click="removeFocused({ id, name })"
                      class="relative flex justify-center items-center h-70rpx mb-20rpx rounded-lg border-solid border-1rpx border-#D1302E bg-#D1302E bg-opacity-10 text-26rpx text-#D1302E">
                      {{ name }}
                      <view class="opt">
                        <wd-icon name="minus-circle-filled" size="30rpx" color="#D1302E" />
                      </view>
                    </view>
                  </wd-col>
                  <!-- 未关注的 -->
                  <wd-col :span="6" v-else>
                    <view @click="addFocused({ id, name })" :style="{ background: 'rgba(0,0,0,0.02)' }"
                      class="relative flex justify-center items-center h-70rpx mb-20rpx rounded-lg border-solid border-1rpx border-black border-opacity-10 text-26rpx text-black text-opacity-50">
                      {{ name }}
                      <view class="opt">
                        <wd-icon name="add-circle" size="30rpx" color="rgba(0,0,0,0.5)" />
                      </view>
                    </view>
                  </wd-col>
                </template>
              </wd-row>
            </template>
            <!-- 国家赛事，二级分类 -->
            <template v-else>
              <view v-for="{ id, name, logo, children } in continental.children" :key="id">
                <view class="flex justify-between items-center h-100rpx px-30rpx" @click="collapseLevel3(id)">
                  <image :src="logo" class="w-60rpx h-60rpx" />
                  <text class="ml-20rpx mr-auto text-28rpx text-black text-opacity-90">
                    {{ name }}
                  </text>
                  <wd-icon name="arrow-up" size="40rpx" color="#999999" v-if="activeLevel3 === id" />
                  <wd-icon name="arrow-down" size="40rpx" color="#999999" v-else />
                </view>
                <template v-if="activeLevel3 === id">
                  <wd-row :gutter="10">
                    <template v-for="{ id, name } in children" :key="id">
                      <!-- 已关注的 -->
                      <wd-col :span="6" v-if="isFocused(id)">
                        <view @click="removeFocused({ id, name })"
                          class="relative flex justify-center items-center h-70rpx mb-20rpx rounded-lg border-solid border-1rpx border-#D1302E bg-#D1302E bg-opacity-10 text-26rpx text-#D1302E">
                          {{ name }}
                          <view class="opt">
                            <wd-icon name="minus-circle-filled" size="30rpx" color="#D1302E" />
                          </view>
                        </view>
                      </wd-col>
                      <!-- 未关注的 -->
                      <wd-col :span="6" v-else>
                        <view @click="addFocused({ id, name })" :style="{ background: 'rgba(0,0,0,0.02)' }"
                          class="relative flex justify-center items-center h-70rpx mb-20rpx rounded-lg border-solid border-1rpx border-black border-opacity-10 text-26rpx text-black text-opacity-50">
                          {{ name }}
                          <view class="opt">
                            <wd-icon name="add-circle" size="30rpx" color="rgba(0,0,0,0.5)" />
                          </view>
                        </view>
                      </wd-col>
                    </template>
                  </wd-row>
                </template>
              </view>
            </template>
          </template>
        </view>
      </scroll-view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { ICascadeMatchCategory, IMatchCategory } from '@/api/match'
import { isEmpty } from 'lodash-es'

const props = defineProps<{ focused: IMatchCategory[]; categories: ICascadeMatchCategory[] }>()
const emit = defineEmits<{
  (e: 'onCategoriesChange', opt: boolean, category: IMatchCategory): void
  (e: 'submit'): void
  (e: 'collapseLevel2', key: number, expand: boolean): void
  (e: 'collapseLevel3', cId: number, key: number, expand: boolean): void
}>()

const visible = ref(false)
const loading = ref(false)
const active = ref(-1)
const activeLevel2 = ref(-1)
const activeLevel3 = ref(-1)
function collapseLevel2(key: number) {
  emit('collapseLevel2', key, activeLevel2.value !== key)
  activeLevel2.value = activeLevel2.value === key ? -1 : key
  activeLevel3.value = -1
}

function collapseLevel3(key: number) {
  emit('collapseLevel3', activeLevel2.value, key, activeLevel3.value !== key)
  activeLevel3.value = activeLevel3.value === key ? -1 : key
}

const isFocused = computed(() => {
  return (id: number) => props.focused.map((f) => f.id).includes(id)
})

const typeClazz = computed(() => {
  return (key: number) => {
    if (key === active.value)
      return "relative text-opacity-90 after:absolute after:block after:content-[''] after:w-full after:h-6rpx after:bg-#D1302E after:bottom-0"
    return 'text-opacity-50'
  }
})

function removeFocused(category: IMatchCategory) {
  emit('onCategoriesChange', false, category)
}

function addFocused(category: IMatchCategory) {
  emit('onCategoriesChange', true, category)
}

function handleClose() {
  emit('submit')
}

function open(key: number) {
  active.value = key
  visible.value = true
}

function hidePopup() {
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.opt {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  width: 30rpx;
  height: 30rpx;
}

.level1-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  font-size: 18rpx;
  color: white;
  background-image: url('https://sacdn.850g.com/football/static/match-category_bg.png');
  background-size: 100% 100%;
}

.stop-page-scroll {
  position: fixed;
  width: 1100%;
  overflow: hidden;
}
</style>
