<template>
  <view>
    <view v-for="{ date, value } in list" :key="date">
      <!-- 时间星期信息 -->
      <view
        style="background-color: rgba(0, 0, 0, 0.02)"
        class="flex justify-center items-center h-70rpx mb-20rpx text-28rpx text-black text-opacity-50"
      >
        {{ dateFormat(date) }}
      </view>
      <!-- 赛事列表 -->
      <view
        v-for="{
          id,
          time,
          roundName,
          homeTeamLogo,
          homeTeamName,
          homeScore,
          awayTeamLogo,
          awayTeamName,
          awayScore,
          statusId,
          statusDesc,
        } in value"
        :key="id"
        class="flex flex-col justify-center items-center py-20rpx px-30rpx border-b-solid border-bottom"
        @click="gotoMatchDetail(id)"
      >
        <view class="flex text-24rpx text-black text-opacity-50 leading-34rpx">
          <text>{{ time }}</text>
          <text>{{ roundName }}</text>
        </view>
        <view class="flex w-full mt-14rpx mb-10rpx">
          <view class="flex-1 flex items-center">
            <image
              :src="homeTeamLogo"
              mode="scaleToFill"
              class="w-40rpx h-40rpx ml-auto mr-14rpx"
            />
            <text>{{ homeTeamName }}</text>
          </view>
          <text
            class="px-40rpx text-40rpx"
            :class="showProgress(statusId) ? 'text-#D1302E' : 'text-black text-opacity-50'"
          >
            {{ `${homeScore} - ${awayScore}` }}
          </text>
          <view class="flex-1 flex items-center">
            <image :src="awayTeamLogo" mode="scaleToFill" class="w-40rpx h-40rpx" />
            <text class="ml-14rpx mr-auto">{{ awayTeamName }}</text>
          </view>
        </view>
        <view
          v-if="statusId === MATCH_STATUS.FULL_TIME"
          class="flex justify-center items-center w-100rpx h-46rpx bg-black bg-opacity-10 rounded-md text-26rpx text-black text-opacity-50"
        >
          已结束
        </view>
        <view class="flex items-center" v-else-if="showProgress(statusId)">
          <image :src="inProgressIcon" mode="scaleToFill" class="w-26rpx h-26rpx" />
          <view class="flex items-center ml-5rpx text-26rpx text-#D1302E">
            <text>进行中</text>
          </view>
        </view>
        <text v-else class="text-26rpx text-black text-opacity-50">{{ statusDesc }}</text>
        <!-- <view class="flex flex-col justify-between gap-y-20rpx w-fit py-20rpx text-28rpx text-black text-opacity-50">
          <text>{{ time }}</text>
          <text>{{ roundName }}</text>
        </view>
        <view class="flex flex-col w-300rpx h-full justify-between ml-80rpx mr-auto">
          <view class="flex items-center text-black text-30rpx">
            <image :src="homeTeamLogo" mode="scaleToFill" class="w-44rpx h-44rpx" />
            <text class="flex-1 ml-18rpx mr-auto">{{ homeTeamName }}</text>
            <text>{{ homeScore }}</text>
          </view>
          <view class="flex items-center text-black text-30rpx">
            <image :src="awayTeamLogo" mode="scaleToFill" class="w-44rpx h-44rpx" />
            <text class="flex-1 ml-18rpx mr-auto">{{ awayTeamName }}</text>
            <text class="text-opacity-50 text-black">{{ awayScore }}</text>
          </view>
        </view>
        <view v-if="statusId === MATCH_STATUS.FULL_TIME"
          class="flex justify-center items-center w-100rpx h-46rpx bg-black bg-opacity-10 rounded-md text-26rpx text-black text-opacity-50">
          已结束
        </view>
        <view class="flex items-center" v-else-if="showProgress(statusId)">
          <wd-icon name="play-circle-filled" size="30rpx" color="#D1302E" />
          <text class="ml-5rpx text-26rpx text-#D1302E">进行中</text>
        </view>
        <text v-else class="text-26rpx text-black text-opacity-50">{{ statusDesc }}</text> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { IMatchItem } from '@/api/match'
import { MATCH_STATUS } from '@/utils/enum'
import { format, formatWeek } from '@/utils/format'
import { ConfigType } from 'dayjs'
import { isEmpty } from 'lodash-es'
import inProgressIcon from '@/static/images/in-progress.svg'

const props = defineProps<{ group: { [key: string]: IMatchItem[] } }>()

const list = computed(() => {
  if (isEmpty(props.group)) return []

  return Object.keys(props.group).map((key) => ({ date: key, value: props.group[key] }))
})

/* 日期标题 */
const dateFormat = computed(() => {
  return (d: ConfigType) => `${format(d, 'YYYY年MM月DD日')} ${formatWeek(d)}`
})

const showProgress = computed(() => {
  return (status: MATCH_STATUS) =>
    [
      MATCH_STATUS.FIRST_HALF, // 上半场
      MATCH_STATUS.HALF_TIME, // 中场
      MATCH_STATUS.SECOND_HALF, // 下半场
      MATCH_STATUS.EXTRA_TIME, // 加时赛
      MATCH_STATUS.EXTRA_TIME_DEPRECATED, // 加时赛(弃用)
      MATCH_STATUS.PENALTY_SHOOTOUT, // 点球决战
    ].includes(status)
})

function gotoMatchDetail(id: number) {
  const pages = getCurrentPages()
  const fromThird = pages[pages.length - 1].route === 'pages/third/competition'
  if (fromThird) {
    uni.navigateTo({ url: `/pages/matchDetail/index?matchId=${id}&from=third` })
  } else {
    uni.navigateTo({ url: `/pages/matchDetail/index?matchId=${id}` })
  }
}
</script>
