<route lang="json5">
{
  style: {
    navigationBarTitleText: '赛事',
  },
}
</route>
<template>
  <scroll-view :show-scrollbar="false" scroll-y class="h-full pb-200rpx overflow-y-auto" lower-threshold="100"
    @scrolltolower="scrolltolower">
    <!-- <view> -->
    <!-- 搜索栏 -->
    <view class="px-30rpx search">
      <wd-input prefix-icon="search" no-border placeholder="搜索球队名称 曼城 I 利物浦" v-model="name" @input="handleInput" />
    </view>
    <!-- </view> -->
    <view class="flex items-center relative pl-40rpx pr-100rpx">
      <view class="flex-1 flex flex-nowrap overflow-x-auto" style="-ms-overflow-style: none; scrollbar-width: none;">
        <text v-for="{ name, id } in focusedCategories" :key="id"
          class="flex-none h-full leading-80rpx text-black text-28rpx ml-40rpx first:ml-0" :class="typeClazz(id)"
          @click="changeType(id)">
          {{ name }}
        </text>
      </view>
      <image :src="othersIcon" class="absolute right-40rpx w-40rpx h-40rpx" @click="showFilter" />
    </view>
    <template v-if="isEmpty(historyMatchListGroup) && isEmpty(matchListGroup)">
      <view class="flex flex-col items-center pt-110rpx pb-64rpx">
        <image :src="emptyImg" class="w-488rpx h-266rpx" />
        <text class="mt-30rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
      </view>
    </template>
    <template v-else>
      <!-- 赛事列表(历史赛事) -->
      <match-list :group="historyMatchListGroup" />
      <!-- 赛事列表 -->
      <match-list :group="matchListGroup" />
    </template>
    <!-- 赛事类别筛选弹框 -->
    <match-filter ref="filterRef" :focused="focusedCategories" :categories="matchCategories"
      @collapse-level2="handleLevel2Collapse" @collapse-level3="handleLevel3Collapse" @submit="handleCategoriesSubmit"
      @on-categories-change="handleFocusedCategoriesChange" />
  </scroll-view>
</template>

<script lang="ts" setup>
import matchFilter from './components/matchFilter.vue'
import othersIcon from '@/static/images/others.svg'
import {
  ICascadeMatchCategory,
  IMatchCategory,
  IMatchItem,
  getCategoryLevel1,
  getCategoryLevel2,
  getCategoryLevel2International,
  getCategoryLevel3,
  getFocusedCategoriesInfo,
  getHistoryMatchList,
  getMatchList,
  updateCompetitionIds,
} from '@/api/match'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { debounce, groupBy, isEmpty, isNil } from 'lodash-es'
import { DEFAULT_MATCH_CATEGORIES, DEFAULT_PAGE_SIZE } from '@/utils/constant'
import { getUserInfo } from '@/service/userService'
import matchList from './components/matchList.vue'
import emptyImg from '@/static/images/empty.png'

const userStore = useUserStore()
const { matchCategories: mc } = storeToRefs(userStore)
const filterRef = ref()
const name = ref('')
const activeType = ref(-1)

const matchCategories = ref<ICascadeMatchCategory[]>([])
const originFocusedCategories = ref<number[]>([])
const focusedCategories = ref<IMatchCategory[]>([])

const matchListGroup = ref<{ [key: string]: IMatchItem[] }>({})
const pageNo = ref(0)
const totalPage = ref(0)

const historyMatchListGroup = ref<{ [key: string]: IMatchItem[] }>({})
const historyPageNo = ref(0)
const historyTotalPage = ref(0)

/*
 * 增加或删除关注的赛事类型
 * operation为true则增加，否则删除
 *
 */
function handleFocusedCategoriesChange(operation: boolean, category: IMatchCategory) {
  if (operation) {
    // if (focusedCategories.value.length >= 6) {
    //   uni.showToast({ title: '最多添加6个' })
    //   return
    // }
    focusedCategories.value = [...focusedCategories.value, category]
  } else {
    focusedCategories.value = focusedCategories.value.filter(({ id }) => id !== category.id)
  }
}

async function handleCategoriesSubmit() {
  const needSubmit = !(
    originFocusedCategories.value.length === focusedCategories.value.length &&
    focusedCategories.value.every(({ id }) => originFocusedCategories.value.includes(id))
  )
  if (needSubmit) {
    const ids = focusedCategories.value.map(({ id }) => id).join(',')
    await updateCompetitionIds(ids)
    originFocusedCategories.value = focusedCategories.value.map(({ id }) => id)
    const userInfoResult = await getUserInfo()
    userStore.setUserInfo(userInfoResult)
  }
}

const typeClazz = computed(() => {
  return (key: number) => {
    if (key === activeType.value || (key < 0 && isNil(activeType.value)))
      return "relative text-opacity-90 after:absolute after:block after:content-[''] after:w-full after:h-6rpx after:bg-#D1302E after:bottom-0"
    return 'text-opacity-50'
  }
})
function showFilter() {
  filterRef.value && filterRef.value.open(activeType.value)
}

function getList(showLoading = true, loadMore = true) {
  const competitionId = activeType.value < 0 ? null : activeType.value
  let params: any = {
    pageNo: pageNo.value,
    pageSize: 10,
    competitionId,
  }
  getMatchList(params).then((resp) => {
    console.log('resp: ', resp)
    let previousData = matchListGroup.value
    matchListGroup.value = resp.list.reduce((prev, c) => {
      const key = c.date
      prev[key] = prev[key] ? [...prev[key], c] : [c]
      return prev
    }, previousData)
    pageNo.value = pageNo.value * 10 > resp.total ? 1 : pageNo.value + 1
  })
}
function getMatchRecords(showLoading = true, loadMore = true) {
  // if (activeType.value < 0) return Promise.resolve(false)
  if (totalPage.value && pageNo.value >= totalPage.value && loadMore) {
    uni.showToast({ icon: 'none', title: '没有更多数据了' })
    return Promise.resolve(false)
  }

  const competitionId = activeType.value < 0 ? null : activeType.value
  let params: any = {
    pageNo: loadMore ? pageNo.value + 1 : 1,
    pageSize: DEFAULT_PAGE_SIZE,
    competitionId,
  }

  if (name.value.trim() !== '') {
    params = { ...params, name: name.value }
  }

  showLoading && uni.showLoading({ title: '加载中...' })
  return getMatchList(params).then((m) => {
    const { list, total } = m
    if (loadMore) {
      let previousData = matchListGroup.value
      matchListGroup.value = list.reduce((prev, c) => {
        const key = c.date
        prev[key] = prev[key] ? [...prev[key], c] : [c]
        return prev
      }, previousData)
      pageNo.value = pageNo.value + 1
    } else {
      matchListGroup.value = groupBy(list, 'date')
      pageNo.value = 1
    }

    totalPage.value = Math.ceil(total / DEFAULT_PAGE_SIZE)
    showLoading && uni.hideLoading()

    return isEmpty(list)
  })
}

async function changeType(key: number) {
  if (key !== activeType.value) {
    activeType.value = key
  }

  const noData = await getMatchRecords(false, false)
  // 清空历史赛事
  historyMatchListGroup.value = {}
  historyPageNo.value = 0
  historyTotalPage.value = 0
  if (noData) {
    // 如果没有赛事则加载历史数据
    getHistoryMatchRecords()
  }
}

const handleInput = debounce(() => {
  getMatchRecords(true, false)
  // 清空历史赛事
  historyMatchListGroup.value = {}
  historyPageNo.value = 0
  historyTotalPage.value = 0
}, 500)

function getHistoryMatchRecords() {
  if (activeType.value < 0) return
  if (historyTotalPage.value && historyPageNo.value >= historyTotalPage.value) {
    uni.showToast({ icon: 'none', title: '没有更多数据了' })
    uni.stopPullDownRefresh()
    return
  }

  let params: any = {
    pageNo: historyPageNo.value + 1,
    pageSize: DEFAULT_PAGE_SIZE,
    competitionId: activeType.value,
  }

  if (name.value.trim() !== '') {
    params = { ...params, name: name.value }
  }

  getHistoryMatchList(params).then((m) => {
    const { list, total } = m
    let previousData = historyMatchListGroup.value
    historyMatchListGroup.value = list.reduce((prev, c) => {
      const key = c.date
      prev[key] = prev[key] ? [c, ...prev[key]] : [c]
      return prev
    }, previousData)

    // 根据日期字符串排序由低到高
    const sortedKeys = Object.keys(historyMatchListGroup.value).sort((a, b) => {
      const dateA = new Date(a)
      const dateB = new Date(b)
      return dateA.getTime() - dateB.getTime()
    })

    historyMatchListGroup.value = Object.fromEntries(
      sortedKeys.map((key) => [key, historyMatchListGroup.value[key]]),
    )

    // map中的 value 是一个对象数组 需要按照对象的 matchTime 升序排序
    historyMatchListGroup.value = Object.fromEntries(
      Object.entries(historyMatchListGroup.value).map(([key, value]) => [
        key,
        value.sort((a, b) => a.matchTime - b.matchTime),
      ]),
    )

    console.log(historyMatchListGroup.value)

    historyPageNo.value = historyPageNo.value + 1
    historyTotalPage.value = Math.ceil(total / DEFAULT_PAGE_SIZE)
    uni.stopPullDownRefresh()
  })
}

async function handleLevel2Collapse(key: number, expand: boolean) {
  const selectedIndex = matchCategories.value.findIndex(({ id }) => id === key)
  if (selectedIndex < 0) return
  if (expand && isNil(matchCategories.value[selectedIndex].children)) {
    // 如果是展开操作，并且还未加载数据
    if (key === 1) {
      const internalRet = await getCategoryLevel2International()
      matchCategories.value[selectedIndex].children = internalRet.map(
        ({ id, shortName, logo }) => ({
          id,
          name: shortName,
          logo,
        }),
      )
    } else {
      const ret = await getCategoryLevel2(key)
      matchCategories.value[selectedIndex].children = ret
    }
  }
}

/*
 * 展开国家层级(最后一层)
 * cid 州id
 */
async function handleLevel3Collapse(cid: number, key: number, expand: boolean) {
  const continentalIndex = matchCategories.value.findIndex((c) => c.id === cid)
  if (continentalIndex < 0 || isEmpty(matchCategories.value[continentalIndex].children)) return
  const countryId = matchCategories.value[continentalIndex].children.findIndex((c) => c.id === key)

  if (expand && isNil(matchCategories.value[continentalIndex].children[countryId].children)) {
    const matchList = await getCategoryLevel3(key)
    matchCategories.value[continentalIndex].children[countryId].children = matchList.map(
      ({ id, shortName, logo }) => ({
        id,
        name: shortName,
        logo,
      }),
    )
  }
}

// 触底加载更多赛事信息
// onReachBottom(getMatchRecords)

// 下拉加载历史数据
onPullDownRefresh(getHistoryMatchRecords)

// 屏幕高度和加载状态
const screenHeight = ref(0)
const isLoading = ref(false)

// 使用防抖处理的加载更多方法
const loadMoreDebounced = debounce(async () => {
  if (isLoading.value) return
  try {
    isLoading.value = true
    await getMatchRecords(false, true)
  } finally {
    isLoading.value = false
  }
}, 200)

const scrolltolower = () => {
  loadMoreDebounced()
}

onShow(async () => {
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 0,
  })
  const info = uni.getSystemInfoSync()
  screenHeight.value = info.screenHeight
  console.log('screenHeight.value: ', screenHeight.value)

  uni.showLoading({ title: '加载中...' })
  historyMatchListGroup.value = {}
  historyPageNo.value = 0
  historyTotalPage.value = 0
  getCategoryLevel1().then(async (c) => {
    // 获取关注的赛事类型
    uni.hideLoading()
    if (isEmpty(mc.value)) {
      const [f] = DEFAULT_MATCH_CATEGORIES
      activeType.value = f.id < 0 ? null : f.id
      focusedCategories.value = DEFAULT_MATCH_CATEGORIES
      originFocusedCategories.value = DEFAULT_MATCH_CATEGORIES.map(({ id }) => id)
    } else {
      // activeType.value = first(fc).id
      // focusedCategories.value = fc
      // originFocusedCategories.value = fc.map(({ id }) => id)
      // const [f] = mc.value
      // const [f] = mc.value
      // focusedCategories.value = [{ id: -1, name: '全部' }, { id: 0, name: '竞足' }]
      originFocusedCategories.value = [-1, 0, ...mc.value]
      activeType.value = -1
      getFocusedCategoriesInfo(mc.value.join(',')).then((res) => {
        focusedCategories.value = [
          { id: -1, name: '全部' },
          { id: 0, name: '竞足' },
          ...res.map(({ id, shortName }) => ({ id, name: shortName })),
        ]
      })
    }

    matchCategories.value = c.map(({ id, name }) => ({ id, name, logo: '' }))

    // if (activeType.value > 0) {
    const noData = await getMatchRecords(false, false)
    // 清空历史赛事
    historyMatchListGroup.value = {}
    historyPageNo.value = 0
    historyTotalPage.value = 0
    if (noData) {
      // 如果没有赛事则加载历史数据
      getHistoryMatchRecords()
    }
  })
})
</script>

<style lang="scss" scoped>
.search {
  :deep(.wd-input) {
    padding: 10rpx;
    background: rgba(0, 0, 0, 0.02);
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    border-radius: 12rpx;
  }
}

.stop-page-scroll {
  position: fixed;
  width: 100%;
  overflow: hidden;
}
</style>
