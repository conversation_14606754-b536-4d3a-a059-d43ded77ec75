<route lang="json5" >
{
style: {
navigationBarTitleText: '投诉建议',
enablePullDownRefresh: false,
},
}
</route>
<template>
  <view class="bg-#FAFAFA">
    <view class="p-30rpx">
      <view class="text-#999 mb-20rpx">投诉账号</view>
      <view class="flex items-center gap-10rpx">
        <view>
          <image src="https://sacdn.850g.com/football/static/logo-icon.png" class="w-120rpx h-120rpx rounded-full"></image>
        </view>
        <view class="text-28rpx">
          <view>神鱼体育</view>
          <view class="text-#999">服务号</view>
        </view>
      </view>
    </view>
    <!-- 问题描述 -->
    <view class="pb-30rpx bg-[white] border-b-solid border-b-#E5E5E5 border-b-1px">
        <wd-textarea clear-trigger="focus" v-model="description" :maxlength="200" clearable show-word-limit
                     placeholder="请描述您遇到的问题，我们会尽快为您解决！"/>
    </view>

    <!-- 上传图片  -->
    <view class="p-30rpx bg-[white]">
      <view class="text-28rpx font-medium text-gray-800 mb-20rpx">
        上传图片
        <text class="text-24rpx text-gray-400 font-normal">(选填)</text>
      </view>
      <view class="flex gap-20rpx flex-wrap">
        <wd-upload
          ref="uploadRef"
          :action="action"
          :file-list="imgUrl"
          :header="uploadHearder"
          :limit="1"
          image-mode="aspectFill"
          @change="changeFileList"
          @success="successUpload"
        ></wd-upload>
      </view>
    </view>
    <view class="flex items-center p-30rpx bg-[white]">
      <wd-checkbox v-model="checkVal" checked-color="#d1302e"></wd-checkbox>
      <text class="font-size-[24rpx] text-[#666]">
        允许使用公众号当前页面的数据和截图作为投诉证据
      </text>
    </view>
    <view class="p-30rpx bg-[white]">
      <wd-button class="w-full" :round="false" type="primary" @click="confirm">提交</wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useUserStore } from "@/store";
import { storeToRefs } from "pinia";
import { complaintCreate } from "@/api/article";
import { PHONE_REG } from "@/utils/regex";


const userStore = useUserStore();
const { userInfo } = storeToRefs(userStore);

const emit = defineEmits<{ (e: "close"): void }>();

const checkVal = ref(true);
// 表单数据
const description = ref("");
const img = ref("");
const imgUrl = ref([]);


const uploadHearder = computed(() => {
  return {
    Authorization: `Bearer ${userInfo.value.token}`
  };
});
const changeFileList = ({ fileList: files }) => {
  imgUrl.value = files;
};
const action = computed(() => {
  return import.meta.env.VITE_UPLOAD_BASEURL;
});
const successUpload = ({ file }) => {
  const response = file.response;
  const result = JSON.parse(response);
  img.value = result.data;
};

// 确认提交
const confirm = () => {
  if (!description.value.trim()) {
    uni.showToast({
      title: "请输入投诉内容",
      icon: "none"
    });
    return;
  }
  // 提交数据
  const formData = {
    contents: description.value,
    screenshotUrl: img.value,
  };
  complaintCreate(formData).then(res => {
    uni.showToast({
      title: "提交成功",
      icon: "success"
    });
    uni.navigateTo({
      url: `/pages/complaints/configSuccess`,
    })
  });
};

</script>

<style lang="scss" scoped>
/* 使用 UnoCSS，无需额外样式 */
</style>
