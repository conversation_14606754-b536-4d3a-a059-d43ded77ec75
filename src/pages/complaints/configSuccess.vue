<route lang="json5" >
{
style: {
navigationBarTitleText: '投诉建议',
enablePullDownRefresh: false,
},
}
</route>
<template>
  <view class="bg-[#ffffff] pb-20rpx h-screen">
    <view class="mt-30% text-center">
      <image class="w-90rpx h-90rpx" src="https://sacdn.850g.com/football/static/success-icon.png"></image>
    </view>
    <view class="text-32rpx text-center mt-50rpx">已提交投诉</view>
    <view class="px-30% mt-20% bg-[white]">
      <wd-button class="w-full" :round="false" type="info" @click="toHome">返回首页</wd-button>
    </view>
  </view>
</template>
<script setup lang="ts">

const toHome = () => {
  uni.switchTab({ url: '/pages/index/index' })
};
</script>
