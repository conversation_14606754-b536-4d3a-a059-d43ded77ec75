<template>
  <view class="flex flex-col gap-y-30rpx rounded-lg shadow mt-30rpx overflow-hidden box-border">
    <view
      v-for="{ matchId, matchTime, homeName, awayName, matchPlays, odds, articleMatchInfo } in data"
      :key="matchId"
      class="flex flex-col pt-30rpx bg-white p-20rpx"
    >
      <view class="flex items-center mb-15rpx">
        <text
          v-if="schemePlay"
          :class="matchTypeClazz(schemePlay)"
          class="center w-50rpx h-28rpx text-20rpx rounded-16rpx border-1rpx border-solid"
        >
          {{ matchTypeTxt(schemePlay) }}
        </text>
        <text class="text-#999 text-24rpx">{{ matchInfo(articleMatchInfo, schemePlay) }}</text>
        <view
          class="flex items-center gap-x-10rpx ml-auto text-26rpx text-black text-opacity-50"
          @click="gotoDetailPage(matchId)"
        >
          <text class="text-#333 text-24rpx">比赛详情</text>
          <wd-icon name="arrow-right" size="28rpx" />
        </view>
      </view>
      <view class="flex justify-around mt-40rpx">
        <view class="flex-1 flex flex-col gap-y-4rpx items-center">
          <image
            :src="articleMatchInfo.homeTeamLogo"
            mode="scaleToFill"
            class="w-100rpx h-100rpx"
          />
          <text class="text-#333 text-28rpx">{{ homeName }}</text>
        </view>
        <!-- 未开赛 -->
        <view
          class="flex flex-col items-center"
          v-if="articleMatchInfo.statusId === MATCH_STATUS.NOT_STARTED"
        >
          <text
            class="center w-120rpx h-40rpx mt-auto mb-18rpx rounded-20rpx border-1rpx border-solid border-#EBEBEB text-22rpx text-#999"
          >
            未开赛
          </text>
          <text class="text-#333 text-28rpx leading-40rpx">VS</text>
        </view>
        <!-- 进行中 -->
        <view
          class="flex flex-col justify-between items-center mt-12rpx mb-4rpx"
          v-else-if="isInProgress(articleMatchInfo.statusId)"
        >
          <text
            class="center w-120rpx h-40rpx rounded-20rpx border-1rpx border-solid border-#EBEBEB text-22rpx text-#999"
          >
            进行中
          </text>
          <text class="text-#333 text-32rpx leading-45rpx">
            {{ `${articleMatchInfo.homeScore} : ${articleMatchInfo.awayScore}` }}
          </text>
          <text class="text-#999 text-24rpx leading-33rpx">
            {{ `半场 : (${articleMatchInfo.homeHalfScore}:${articleMatchInfo.awayHalfScore})` }}
          </text>
        </view>
        <!-- 完场 -->
        <view
          class="flex flex-col justify-between items-center mt-12rpx mb-4rpx"
          v-else-if="articleMatchInfo.statusId === MATCH_STATUS.FULL_TIME"
        >
          <text
            class="center w-120rpx h-40rpx rounded-20rpx border-1rpx border-solid border-#CF302C text-22rpx text-#CF302C"
          >
            已结束
          </text>
          <text class="text-#333 text-32rpx leading-45rpx">
            {{ `${articleMatchInfo.homeScore} : ${articleMatchInfo.awayScore}` }}
          </text>
          <text class="text-#999 text-24rpx leading-33rpx">
            {{ `半场 : (${articleMatchInfo.homeHalfScore}:${articleMatchInfo.awayHalfScore})` }}
          </text>
        </view>
        <!-- 异常情况 -->
        <view v-else class="center">
          <text
            class="center w-120rpx h-40rpx rounded-20rpx border-1rpx border-solid border-#CF302C text-22rpx text-#CF302C"
          >
            {{ `${MATCH_STATUS_TXT[articleMatchInfo.statusId]}` }}
          </text>
        </view>
        <view class="flex-1 flex flex-col gap-y-4rpx items-center">
          <image
            :src="articleMatchInfo.awayTeamLogo"
            mode="scaleToFill"
            class="w-100rpx h-100rpx"
          />
          <text class="text-#333 text-28rpx">{{ awayName }}</text>
        </view>
      </view>

      <!-- <view class="flex justify-center items-center gap-x-30rpx mt-20rpx text-36rpx text-opacity-90">
        <view class="flex-1 flex justify-end items-center gap-x-14rpx">
          <image :src="articleMatchInfo.homeTeamLogo" mode="scaleToFill" class="w-40rpx h-40rpx" />
          <text>{{ homeName }}</text>
        </view>
        <text class="text-30rpx text-black text-opacity-50">
          {{ score(matchTime, articleMatchInfo.homeScore, articleMatchInfo.awayScore) }}
        </text>
        <view class="flex-1 flex items-center gap-x-14rpx">
          <image :src="articleMatchInfo.awayTeamLogo" mode="scaleToFill" class="w-40rpx h-40rpx" />
          <text>{{ awayName }}</text>
        </view>
      </view> -->
      <!-- 半场胜负，全场角球信息 -->
      <!-- <view class="flex justify-center gap-x-20rpx text-26rpx text-black text-opacity-50">
        <text>
          {{ halfMatchResult(articleMatchInfo.homeHalfScore, articleMatchInfo.awayHalfScore) }}
        </text>
        <text>{{ `角:${articleMatchInfo.homeCorner + articleMatchInfo.awayCorner}` }}</text>
      </view> -->
      <!-- 已购买方案 -->
      <template v-if="isBuy">
        <!-- 主玩法 -->
        <view v-if="!isEmpty(matchPlays.main)">
          <view class="flex text-28rpx my-20rpx text-black text-opacity-90 font-bold">主玩法</view>
          <view
            v-for="{ playId, result, opinion } in matchPlays.main"
            :key="playId"
            class="flex flex-col gap-y-20rpx"
          >
            <template v-if="playId === PLAY_TYPE.WIN_LOSE_DRAW">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center rounded-4rpx bg-#E6F5DD text-#60B53F text-24rpx">胜平负</view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes('3'),
                      articleMatchInfo.homeScore > articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">主胜</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.spf">
                    {{ odds.spf ? odds.spf.split(',')[0] : '' }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="result.split(',')[0] === '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="result.split(',').includes('3') && result.split(',')[0] !== '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="opinion && articleMatchInfo.homeScore > articleMatchInfo.awayScore"
                  >
                    <template v-if="result.split(',').includes('3')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes('1'),
                      articleMatchInfo.homeScore === articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">平</text>
                  <text class="text-22rpx leading-22rpx">
                    {{ odds.spf ? odds.spf.split(',')[1] : '' }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="result.split(',')[0] === '1'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="result.split(',').includes('1') && result.split(',')[0] !== '1'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="opinion && articleMatchInfo.homeScore === articleMatchInfo.awayScore"
                  >
                    <template v-if="result.split(',').includes('1')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes('0'),
                      articleMatchInfo.homeScore < articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">客胜</text>
                  <text class="text-22rpx leading-22rpx">
                    {{ odds.spf ? odds.spf.split(',')[2] : '' }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="result.split(',')[0] === '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="result.split(',').includes('0') && result.split(',')[0] !== '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="opinion && articleMatchInfo.homeScore < articleMatchInfo.awayScore"
                  >
                    <template v-if="result.split(',').includes('0')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.SCORE">
              <!-- 比分 -->
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center rounded-4rpx text-24rpx bg-#FFE7E8 text-#ED8C90">比分</view>
                <view
                  v-for="(r, index) in result.split(',')"
                  :key="r"
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes(r),
                      isScoreHit(r, articleMatchInfo.homeScore, articleMatchInfo.awayScore),
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">{{ r }}</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.bf">
                    {{ getOdds(BF_SCORES, odds.bf, r) }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="index === 0"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      isScoreHit(r, articleMatchInfo.homeScore, articleMatchInfo.awayScore)
                    "
                  >
                    <wd-icon
                      name="check-circle-filled"
                      size="30rpx"
                      color="#fff"
                      class="absolute right-10rpx"
                    />
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW">
              <!-- 让球胜平负 -->
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#FEF2E7 text-#E8AF74">
                  让球
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('3'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] >
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">
                    {{ `主队${odds.rq ? odds.rq.split(',')[0] : ''}` }}
                  </text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.rq">
                    {{ odds.rq ? odds.rq.split(',')[1] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('3') &&
                        result.split(',').slice(1)[0] !== '3'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      odds.rq &&
                      opinion &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] >
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('3')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('1'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] ===
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">平局</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.rq">
                    {{ odds.rq ? odds.rq.split(',')[2] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '1'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('1') &&
                        result.split(',').slice(1)[0] !== '1'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      odds.rq &&
                      opinion &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] ===
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('1')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center gap-y-6rpx items-center flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('0'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] <
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">客队</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.rq">
                    {{ odds.rq ? odds.rq.split(',')[3] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('0') &&
                        result.split(',').slice(1)[0] !== '0'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      odds.rq &&
                      opinion &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] <
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('0')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.RQ">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#DEFCFF text-#5EB5BD">
                  让球
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('3'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] >
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">
                    {{ `主队${odds.rq ? odds.rq.split(',')[0] : ''}` }}
                  </text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.rq">
                    {{ odds.rq ? odds.rq.split(',')[1] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('3') &&
                        result.split(',').slice(1)[0] !== '3'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      odds.rq &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] >
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('3')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('0'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] <
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">
                    {{ `客队${odds.rq ? 0 - +odds.rq.split(',')[0] : ''}` }}
                  </text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.rq">
                    {{ odds.rq ? odds.rq.split(',')[3] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('0') &&
                        result.split(',').slice(1)[0] !== '0'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      odds.rq &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] <
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('0')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.BQC">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#FFF8E3 text-#C3AA63">
                  半全场
                </view>
                <view
                  v-for="(r, index) in result.split(',')"
                  :key="r"
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes(r),
                      isBQCHit(
                        r,
                        articleMatchInfo.homeScore,
                        articleMatchInfo.awayScore,
                        articleMatchInfo.homeHalfScore,
                        articleMatchInfo.awayHalfScore,
                      ),
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">{{ r }}</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.bqc">
                    {{ getOdds(BQC_SCORE, odds.bqc, r) }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="index === 0"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      isBQCHit(
                        r,
                        articleMatchInfo.homeScore,
                        articleMatchInfo.awayScore,
                        articleMatchInfo.homeHalfScore,
                        articleMatchInfo.awayHalfScore,
                      )
                    "
                  >
                    <wd-icon
                      name="check-circle-filled"
                      size="30rpx"
                      color="#fff"
                      class="absolute right-10rpx"
                    />
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.JQ">
              <!-- 进球 -->
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#EDF9FF text-#4188DF">
                  进球
                </view>
                <view
                  v-for="(r, index) in result.split(',')"
                  :key="r"
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes(r),
                      isJQHit(+r, articleMatchInfo.homeScore, articleMatchInfo.awayScore),
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">{{ r }}</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.jq">
                    {{ getOdds(JQ_SCORE, odds.jq, r) }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="index === 0"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion && isJQHit(+r, articleMatchInfo.homeScore, articleMatchInfo.awayScore)
                    "
                  >
                    <wd-icon
                      name="check-circle-filled"
                      size="30rpx"
                      color="#fff"
                      class="absolute right-10rpx"
                    />
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.DXQ">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#EEF0FF text-#7C89E0">
                  {{ `大小球${odds.dxq ? '(' + odds.dxq.split(',')[1] + ')' : ''}` }}
                </view>
                <view
                  class="relative center flex-1 rounded-4rpx text-24rpx overflow-hidden"
                  :class="
                    odds.dxq
                      ? bgColor(
                          opinion,
                          result.split(',').slice(1).includes('3'),
                          articleMatchInfo.homeScore + articleMatchInfo.awayScore >=
                            +odds.dxq.split(',')[1],
                        )
                      : 'bg-#F4F4F4 text-black text-opacity-30'
                  "
                >
                  {{ `大${odds.dxq ? odds.dxq.split(',')[0] : ''}` }}
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('3') &&
                        result.split(',').slice(1)[0] !== '3'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      odds.dxq &&
                      articleMatchInfo.homeScore + articleMatchInfo.awayScore >=
                        +odds.dxq.split(',')[1]
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('3')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative center flex-1 rounded-4rpx overflow-hidden text-24rpx"
                  :class="
                    odds.dxq
                      ? bgColor(
                          opinion,
                          result.split(',').slice(1).includes('0'),
                          articleMatchInfo.homeScore + articleMatchInfo.awayScore <=
                            +odds.dxq.split(',')[1],
                        )
                      : 'bg-#F4F4F4 text-black text-opacity-30'
                  "
                >
                  {{ `小${odds.dxq ? odds.dxq.split(',')[2] : ''}` }}
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('0') &&
                        result.split(',').slice(1)[0] !== '0'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      odds.dxq &&
                      articleMatchInfo.homeScore + articleMatchInfo.awayScore <=
                        +odds.dxq.split(',')[1]
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('0')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
              </view>
            </template>
          </view>
        </view>

        <view v-if="!isEmpty(matchPlays.bonus)" class="mt-30rpx">
          <view class="flex text-28rpx my-20rpx text-black text-opacity-90 font-bold">
            附赠玩法
            <text class="text-24rpx text-black text-opacity-50">（比赛成果不计入成绩）</text>
          </view>
          <view
            v-for="{ playId, result, opinion } in matchPlays.bonus"
            :key="playId"
            class="flex flex-col gap-y-20rpx"
          >
            <template v-if="playId === PLAY_TYPE.WIN_LOSE_DRAW">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx bg-#E6F5DD text-#60B53F text-24rpx">
                  胜平负
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes('3'),
                      articleMatchInfo.homeScore > articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">主胜</text>
                  <text class="text-22rpx leading-22rpx">
                    {{ odds.spf ? odds.spf.split(',')[0] : '' }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="result.split(',')[0] === '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="result.split(',').includes('3') && result.split(',')[0] !== '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="opinion && articleMatchInfo.homeScore > articleMatchInfo.awayScore"
                  >
                    <template v-if="result.split(',').includes('3')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes('1'),
                      articleMatchInfo.homeScore === articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">平局</text>
                  <text class="text-22rpx leading-22rpx">
                    {{ odds.spf ? odds.spf.split(',')[1] : '' }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="result.split(',')[0] === '1'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="result.split(',').includes('1') && result.split(',')[0] !== '1'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="opinion && articleMatchInfo.homeScore === articleMatchInfo.awayScore"
                  >
                    <template v-if="result.split(',').includes('1')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes('0'),
                      articleMatchInfo.homeScore < articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">客队赢</text>
                  <text class="text-22rpx leading-22rpx">
                    {{ odds.spf ? odds.spf.split(',')[2] : '' }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="result.split(',')[0] === '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="result.split(',').includes('0') && result.split(',')[0] !== '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="opinion && articleMatchInfo.homeScore < articleMatchInfo.awayScore"
                  >
                    <template v-if="result.split(',').includes('0')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.SCORE">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#FFE7E8 text-#ED8C90">
                  比分
                </view>
                <view
                  v-for="(r, index) in result.split(',')"
                  :key="r"
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes(r),
                      isScoreHit(r, articleMatchInfo.homeScore, articleMatchInfo.awayScore),
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">{{ r }}</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.bf">
                    {{ getOdds(BF_SCORES, odds.bf, r) }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="index === 0"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      isScoreHit(r, articleMatchInfo.homeScore, articleMatchInfo.awayScore)
                    "
                  >
                    <wd-icon
                      name="check-circle-filled"
                      size="30rpx"
                      color="#fff"
                      class="absolute right-10rpx"
                    />
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW">
              <!-- 让球胜平负 -->
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#FEF2E7 text-#E8AF74">
                  让球
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('3'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] >
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">
                    {{ `主队${odds.rq ? odds.rq.split(',')[0] : ''}` }}
                  </text>
                  <text class="text-22rpx leading-22rpx">
                    {{ odds.rq ? odds.rq.split(',')[1] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('3') &&
                        result.split(',').slice(1)[0] !== '3'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      odds.rq &&
                      opinion &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] >
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('3')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 last:rounded-r-12rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('1'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] ===
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">平局</text>
                  <text class="text-22rpx leading-22rpx">
                    {{ odds.rq ? odds.rq.split(',')[2] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '1'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('1') &&
                        result.split(',').slice(1)[0] !== '1'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      odds.rq &&
                      opinion &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] ===
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('1')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('0'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] <
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">客队</text>
                  <text class="text-22rpx leading-22rpx">
                    {{ odds.rq ? odds.rq.split(',')[3] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('0') &&
                        result.split(',').slice(1)[0] !== '0'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      odds.rq &&
                      opinion &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] <
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('0')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.RQ">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#DEFCFF text-#5EB5BD">
                  让球
                </view>
                <view
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('3'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] >
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">
                    {{ `主队${odds.rq ? odds.rq.split(',')[0] : ''}` }}
                  </text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.rq">
                    {{ odds.rq ? odds.rq.split(',')[1] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('3') &&
                        result.split(',').slice(1)[0] !== '3'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      odds.rq &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] >
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('3')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative flex flex-col justify-center items-center flex-1 text-white last:rounded-r-12rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.split(',').slice(1).includes('0'),
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] <
                        articleMatchInfo.awayScore,
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">
                    {{ `客队${odds.rq ? 0 - +odds.rq.split(',')[0] : ''}` }}
                  </text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.rq">
                    {{ odds.rq ? odds.rq.split(',')[3] : '' }}
                  </text>
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('0') &&
                        result.split(',').slice(1)[0] !== '0'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      odds.rq &&
                      articleMatchInfo.homeScore + +odds.rq.split(',')[0] <
                        articleMatchInfo.awayScore
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('0')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.BQC">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#FFF8E3 text-#C3AA63">
                  半全场
                </view>
                <view
                  v-for="(r, index) in result.split(',')"
                  :key="r"
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes(r),
                      isBQCHit(
                        r,
                        articleMatchInfo.homeScore,
                        articleMatchInfo.awayScore,
                        articleMatchInfo.homeHalfScore,
                        articleMatchInfo.awayHalfScore,
                      ),
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">{{ r }}</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.bqc">
                    {{ getOdds(BQC_SCORE, odds.bqc, r) }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="index === 0"
                      class="absolute center left-0 top-0 h-30rpx px</view>-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      isBQCHit(
                        r,
                        articleMatchInfo.homeScore,
                        articleMatchInfo.awayScore,
                        articleMatchInfo.homeHalfScore,
                        articleMatchInfo.awayHalfScore,
                      )
                    "
                  >
                    <wd-icon
                      name="check-circle-filled"
                      size="30rpx"
                      color="#fff"
                      class="absolute right-10rpx"
                    />
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.JQ">
              <!-- 进球 -->
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#EDF9FF text-#4188DF">
                  进球
                </view>
                <view
                  v-for="(r, index) in result.split(',')"
                  :key="r"
                  class="relative flex flex-col justify-center items-center gap-y-6rpx flex-1 rounded-4rpx overflow-hidden"
                  :class="
                    bgColor(
                      opinion,
                      result.includes(r),
                      isJQHit(+r, articleMatchInfo.homeScore, articleMatchInfo.awayScore),
                    )
                  "
                >
                  <text class="text-24rpx leading-24rpx">{{ r }}</text>
                  <text class="text-22rpx leading-22rpx" v-if="odds.jq">
                    {{ getOdds(JQ_SCORE, odds.jq, r) }}
                  </text>
                  <template v-if="result.split(',').length > 1">
                    <text
                      v-if="index === 0"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion && isJQHit(+r, articleMatchInfo.homeScore, articleMatchInfo.awayScore)
                    "
                  >
                    <wd-icon
                      name="check-circle-filled"
                      size="30rpx"
                      color="#fff"
                      class="absolute right-10rpx"
                    />
                  </template>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.DXQ">
              <view class="relative grid grid-cols-4 gap-2 h-70rpx mb-30rpx">
                <template v-if="opinion === 1">
                  <image
                    :src="hitIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <template v-else-if="opinion === 2">
                  <image
                    :src="missIcon"
                    mode="scaleToFill"
                    class="absolute -left-25rpx top-25rpx h-50rpx w-50rpx"
                  />
                </template>
                <view class="center flex-1 rounded-4rpx text-24rpx bg-#EEF0FF text-#7C89E0">
                  {{ `大小球${odds.dxq ? '(' + odds.dxq.split(',')[1] + ')' : ''}` }}
                </view>
                <view
                  class="relative center flex-1 rounded-4rpx text-24rpx overflow-hidden"
                  :class="
                    odds.dxq
                      ? bgColor(
                          opinion,
                          result.split(',').slice(1).includes('3'),
                          articleMatchInfo.homeScore + articleMatchInfo.awayScore >=
                            +odds.dxq.split(',')[1],
                        )
                      : ''
                  "
                >
                  {{ `大${odds.dxq ? odds.dxq.split(',')[0] : ''}` }}
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '3'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                  </template>
                  <text
                    v-else-if="
                      result.split(',').slice(1).includes('3') &&
                      result.split(',').slice(1)[0] !== '3'
                    "
                    class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                  >
                    次推
                  </text>
                  <template
                    v-if="
                      opinion &&
                      odds.dxq &&
                      articleMatchInfo.homeScore + articleMatchInfo.awayScore >=
                        +odds.dxq.split(',')[1]
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('3')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
                <view
                  class="relative center flex-1 rounded-4rpx text-24rpx overflow-hidden"
                  :class="
                    odds.dxq
                      ? bgColor(
                          opinion,
                          result.split(',').slice(1).includes('0'),
                          articleMatchInfo.homeScore + articleMatchInfo.awayScore <=
                            +odds.dxq.split(',')[1],
                        )
                      : ''
                  "
                >
                  {{ `小${odds.dxq ? odds.dxq.split(',')[2] : ''}` }}
                  <template v-if="result.split(',').slice(1).length > 1">
                    <text
                      v-if="result.split(',').slice(1)[0] === '0'"
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      首推
                    </text>
                    <text
                      v-else-if="
                        result.split(',').slice(1).includes('0') &&
                        result.split(',').slice(1)[0] !== '0'
                      "
                      class="absolute center left-0 top-0 h-30rpx px-5rpx bg-#F2BB07 text-white text-24rpx"
                    >
                      次推
                    </text>
                  </template>
                  <template
                    v-if="
                      opinion &&
                      odds.dxq &&
                      articleMatchInfo.homeScore + articleMatchInfo.awayScore <=
                        +odds.dxq.split(',')[1]
                    "
                  >
                    <template v-if="result.split(',').slice(1).includes('0')">
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#fff"
                        class="absolute right-10rpx"
                      />
                    </template>
                    <template v-else>
                      <wd-icon
                        name="check-circle-filled"
                        size="30rpx"
                        color="#FE3C3D"
                        class="absolute right-10rpx"
                      />
                    </template>
                  </template>
                </view>
              </view>
            </template>
          </view>
        </view>
      </template>
      <!-- 未购买 -->
      <template v-else>
        <view v-if="!isEmpty(matchPlays.main)">
          <view class="flex text-32rpx my-20rpx text-black text-opacity-90 font-bold">主玩法</view>
          <view
            v-for="{ playId, result } in matchPlays.main"
            :key="playId"
            class="flex flex-col gap-y-20rpx"
          >
            <template v-if="playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW">
              <view class="grid grid-cols-4 gap-2 h-70rpx mb-30rpx text-24rpx">
                <view
                  class="flex flex-col justify-center items-center px-20rpx rounded-4rpx bg-#DEFCFF text-#5EB5BD"
                >
                  让球
                </view>
                <view class="text-center flex-col px-20rpx rounded-4rpx bg-#F4F4F4">
                  <view>{{ `主胜${odds.rq ? '(' + odds.rq.split(',')[0] + ')' : ''}` }}</view>
                  <view v-if="odds.rq">{{ odds.rq.split(',').slice(1)[0] }}</view>
                </view>
                <view class="text-center flex-col px-20rpx rounded-4rpx bg-#F4F4F4">
                  <view>平</view>
                  <view v-if="odds.rq">{{ odds.rq.split(',').slice(1)[1] }}</view>
                </view>
                <view class="text-center flex-col px-20rpx rounded-4rpx bg-#F4F4F4">
                  <view>客胜</view>
                  <view v-if="odds.rq">{{ odds.rq.split(',').slice(1)[2] }}</view>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.WIN_LOSE_DRAW">
              <view class="grid grid-cols-4 gap-2 h-70rpx mb-30rpx text-24rpx">
                <view
                  class="flex flex-col justify-center items-center px-40rpx rounded-4rpx bg-#E6F5DD text-#60B53F"
                >
                  胜负平
                </view>
                <view class="text-center flex-col px-40rpx rounded-4rpx bg-#F4F4F4">
                  <view>主胜</view>
                  <view v-if="odds.spf">{{ odds.spf.split(',')[0] }}</view>
                </view>
                <view class="text-center flex-col px-40rpx rounded-4rpx bg-#F4F4F4">
                  <view>平</view>
                  <view v-if="odds.spf">{{ odds.spf.split(',')[1] }}</view>
                </view>
                <view class="text-center flex-col px-40rpx rounded-4rpx bg-#F4F4F4">
                  <view>客胜</view>
                  <view v-if="odds.spf">{{ odds.spf.split(',')[2] }}</view>
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.SCORE">
              <view class="grid grid-cols-4 gap-2 h-70rpx mb-30rpx text-24rpx">
                <view
                  class="flex flex-col justify-center items-center px-40rpx rounded-4rpx bg-#FFE7E8 text-#ED8C90"
                >
                  比分
                  <image :src="maskIcon" mode="scaleToFill" class="w-60rpx h-40rpx" />
                </view>
                <view
                  v-for="r in result.split(',')"
                  :key="r"
                  class="flex flex-col justify-center items-center flex-1 text-white bg-#F4F4F4 last:rounded-r-12rpx overflow-hidden"
                >
                  <image :src="maskIcon" mode="scaleToFill" class="w-60rpx h-20rpx" />
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.BQC">
              <view class="grid grid-cols-4 gap-2 h-70rpx mb-30rpx text-24rpx">
                <view
                  v-for="r in result.split(',')"
                  :key="r"
                  class="flex flex-col justify-center items-center flex-1 text-white bg-#F4F4F4 last:rounded-r-12rpx overflow-hidden"
                >
                  <image :src="maskIcon" mode="scaleToFill" class="w-60rpx h-20rpx" />
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.JQ">
              <view class="grid grid-cols-4 gap-2 h-70rpx mb-30rpx text-24rpx">
                <view
                  class="flex flex-col justify-center items-center px-40rpx rounded-4rpx bg-#EDF9FF text-#4188DF"
                >
                  进球
                  <image :src="maskIcon" mode="scaleToFill" class="w-60rpx h-40rpx" />
                </view>
                <view
                  v-for="r in result.split(',')"
                  :key="r"
                  class="flex flex-col justify-center items-center flex-1 text-white bg-#F4F4F4 last:rounded-r-12rpx overflow-hidden"
                >
                  <image :src="maskIcon" mode="scaleToFill" class="w-60rpx h-40rpx" />
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.RQ">
              <view class="grid grid-cols-4 gap-2 h-70rpx mb-30rpx text-24rpx">
                <view class="flex items-center px-40rpx rounded-4rpx bg-#F4F4F4">让球</view>
                <view
                  class="flex flex-col justify-center items-center flex-1 text-white bg-bg-#F4F4F4 last:rounded-r-12rpx overflow-hidden"
                >
                  <text>主队</text>
                  <image :src="maskIcon" mode="scaleToFill" class="w-60rpx h-40rpx" />
                </view>
                <view
                  class="flex flex-col justify-center items-center flex-1 text-white bg-bg-#F4F4F4 last:rounded-r-12rpx overflow-hidden"
                >
                  <text>客队</text>
                  <image :src="maskIcon" mode="scaleToFill" class="w-60rpx h-40rpx" />
                </view>
              </view>
            </template>
            <template v-else-if="playId === PLAY_TYPE.DXQ">
              <view class="grid grid-cols-4 gap-2 h-70rpx mb-30rpx text-24rpx">
                <view class="flex items-center px-40rpx rounded-4rpx bg-#EEF0FF text-#7C89E0">
                  大小球
                </view>
                <view
                  class="flex flex-col justify-center items-center flex-1 text-white bg-#999 last:rounded-r-12rpx overflow-hidden"
                >
                  <text>大</text>
                </view>
                <view
                  class="flex flex-col justify-center items-center flex-1 text-white bg-#999 last:rounded-r-12rpx overflow-hidden"
                >
                  <text>小</text>
                </view>
              </view>
            </template>
          </view>
        </view>
        <view v-if="!isEmpty(matchPlays.bonus)" class="mt-30rpx">
          <view class="flex text-28rpx my-20rpx text-black text-opacity-90 font-bold">
            附赠玩法
            <text class="text-26rpx text-black text-opacity-50">（比赛成果不计入成绩）</text>
          </view>
          <view class="grid grid-cols-4 gap-2 h-70rpx mb-30rpx text-24rpx">
            <view
              v-for="{ playId } in matchPlays.bonus"
              :key="playId"
              class="flex flex-col gap-y-20rpx"
            >
              <template v-if="playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW">
                <view class="center h-full px-20rpx rounded-4rpx bg-#DEFCFF text-#5EB5BD">
                  让球
                </view>
              </template>
              <template v-if="playId === PLAY_TYPE.WIN_LOSE_DRAW">
                <view class="center h-full px-20rpx rounded-4rpx bg-#E6F5DD text-#60B53F">
                  胜负平
                </view>
              </template>
              <template v-if="playId === PLAY_TYPE.SCORE">
                <view class="center h-full px-20rpx rounded-4rpx bg-#FFE7E8 text-#ED8C90">
                  比分
                </view>
              </template>
              <template v-if="playId === PLAY_TYPE.BQC">
                <view class="center h-full px-20rpx rounded-4rpx bg-#FFF8E3 text-#C3AA63">
                  半全场
                </view>
              </template>
              <template v-if="playId === PLAY_TYPE.JQ">
                <view class="center h-full px-20rpx rounded-4rpx bg-#EDF9FF text-#4188DF">
                  进球
                </view>
              </template>
              <template v-if="playId === PLAY_TYPE.RQ">
                <view class="center h-full px-20rpx rounded-4rpx bg-#DEFCFF text-#5EB5BD">
                  让球
                </view>
              </template>
              <template v-if="playId === PLAY_TYPE.DXQ">
                <view class="center h-full px-20rpx rounded-4rpx bg-#EEF0FF text-#7C89E0">
                  大小球
                </view>
              </template>
            </view>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { IArticleMatchInfo, IMatchResult, IMatchSchem } from '@/api/article'
import { PLAY_TYPE, SCHEME_TYPE, MATCH_STATUS } from '@/utils/enum'
import { isEmpty } from 'lodash-es'
import { BF_SCORES, BQC_SCORE, JQ_SCORE, WEEK_CN, MATCH_STATUS_TXT } from '@/utils/constant'
import maskIcon from '@/static/images/mask.svg'
import dayjs from 'dayjs'
import hitIcon from '@/static/icons/hit.svg'
import missIcon from '@/static/icons/miss.svg'

const props = defineProps<{
  scheme: (IMatchSchem & { articleMatchInfo: IArticleMatchInfo })[]
  isBuy: number
  schemePlay: SCHEME_TYPE
  issue: string
  sellOutTime: string
}>()

const matchTypeTxt = computed(() => {
  return (sp: SCHEME_TYPE) => {
    switch (sp) {
      case SCHEME_TYPE.MATCH_LOTTERY:
        return '14场'
      case SCHEME_TYPE.ANY_NINE:
        return '任9'
      case SCHEME_TYPE.TEAM_PARLAY:
      case SCHEME_TYPE.SINGLE:
      case SCHEME_TYPE.MULTI_TEAM_PARLAY:
        return '竞足'
      case SCHEME_TYPE.SINGLE_GAME_BET:
      case SCHEME_TYPE.TWO_SELECTIONS_PARLAY:
      case SCHEME_TYPE.MULTIPLE_SELECTIONS_PARLAY:
        return '足球'
    }
  }
})

const matchTypeClazz = computed(() => {
  return (sp: SCHEME_TYPE) => {
    switch (sp) {
      case SCHEME_TYPE.MATCH_LOTTERY:
        return 'text-#60B53F border-#60B53F'
      case SCHEME_TYPE.ANY_NINE:
        return 'text-#FF7600 border-#FF7600'
      case SCHEME_TYPE.TEAM_PARLAY:
      case SCHEME_TYPE.SINGLE:
      case SCHEME_TYPE.MULTI_TEAM_PARLAY:
        return 'text-#CF302C border-#CF302C'
      case SCHEME_TYPE.SINGLE_GAME_BET:
      case SCHEME_TYPE.TWO_SELECTIONS_PARLAY:
      case SCHEME_TYPE.MULTIPLE_SELECTIONS_PARLAY:
        return 'text-#4188DF border-#4188DF'
    }
  }
})

// function score(matchtime: number, hScore?: number, aScore?: number) {
//   const now = Date.now()
//   if (matchtime * 1000 - now > 0) return 'VS'
//   return `${hScore}:${aScore}`
// }

// function halfMatchResult(homeHalfScore: number, awayHalfScore: number) {
//   if (homeHalfScore === awayHalfScore) return '半:平'
//   return homeHalfScore > awayHalfScore ? '半:胜' : '半:负'
// }

// function schemeType(sp: SCHEME_TYPE) {
//   switch (sp) {
//     case SCHEME_TYPE.MATCH_LOTTERY:
//       return '14场'
//     case SCHEME_TYPE.ANY_NINE:
//       return '任九'
//     case SCHEME_TYPE.SINGLE_GAME_BET:
//     case SCHEME_TYPE.TWO_SELECTIONS_PARLAY:
//     case SCHEME_TYPE.MULTIPLE_SELECTIONS_PARLAY:
//       return '足球'
//     case SCHEME_TYPE.TEAM_PARLAY:
//     case SCHEME_TYPE.SINGLE:
//     case SCHEME_TYPE.MULTI_TEAM_PARLAY:
//       return '竞足'
//     default:
//       return ''
//   }
// }

function matchInfo(match: IArticleMatchInfo, sp: SCHEME_TYPE) {
  const { matchTime, competitionName, issueNum } = match
  const datetime = dayjs(matchTime * 1000)
  const week = WEEK_CN[datetime.day()]
  const time = datetime.format('MM-DD HH:mm')

  if ([SCHEME_TYPE.TEAM_PARLAY, SCHEME_TYPE.SINGLE, SCHEME_TYPE.MULTI_TEAM_PARLAY].includes(sp)) {
    const wIndex = issueNum.slice(0, 1)
    const _week = wIndex === '7' ? WEEK_CN[0] : WEEK_CN[+wIndex]
    const _issueNum = issueNum.slice(1)
    // return `${_week} ${competitionName} ${_issueNum} ${time}`
    return `【${_week}${_issueNum}】 ${competitionName} ${time}`
  }
  return `【${week}】 ${competitionName} ${issueNum || ''} ${time}`
}

function isInProgress(status: MATCH_STATUS) {
  return [
    MATCH_STATUS.FIRST_HALF,
    MATCH_STATUS.HALF_TIME,
    MATCH_STATUS.SECOND_HALF,
    MATCH_STATUS.EXTRA_TIME,
    MATCH_STATUS.PENALTY_SHOOTOUT,
  ].includes(status)
}

// function matchCaption(match: IArticleMatchInfo, sp: SCHEME_TYPE) {
//   const { matchTime, competitionName, issueNum } = match
//   const datetime = dayjs(matchTime * 1000)
//   const week = WEEK_CN[datetime.day()]
//   const time = datetime.format('MM-DD HH:mm')

//   if ([SCHEME_TYPE.TEAM_PARLAY, SCHEME_TYPE.SINGLE, SCHEME_TYPE.MULTI_TEAM_PARLAY].includes(sp)) {
//     const wIndex = issueNum.slice(0, 1)
//     const _week = wIndex === '7' ? WEEK_CN[0] : WEEK_CN[+wIndex]
//     const _issueNum = issueNum.slice(1)
//     return `${_week} ${competitionName} ${_issueNum} ${time}`
//   }
//   return `${week} ${competitionName} ${issueNum || ''} ${time}`
// }

/* 获取比分，半全场指定结果的赔率 */
function getOdds(options: string[], odd: string | null, predict: string) {
  if (!odd) return ''
  const index = options.findIndex((e) => e === predict)

  if (index >= 0) return odd.split(',')[index]
  return ''
}

/* *
 * judged 是否已经判别
 * isChecked: 是否选中
 * isHit: 是否命中
 */
function bgColor(judged: boolean, isChecked: boolean, isHit: boolean) {
  if (!judged) return isChecked ? 'bg-#FE3C3D text-white' : 'bg-#F4F4F4 text-black text-opacity-30'
  if (isHit) return isChecked ? 'bg-#FE3C3D text-white' : 'bg-#F4F4F4 text-black text-opacity-30'
  return isChecked ? 'bg-#999 text-white' : 'bg-#F4F4F4 text-black text-opacity-30'
}

/* 比分玩法选项是否命中 */
function isScoreHit(predict: string, homeScore: number, awayScore: number) {
  const result = `${homeScore}:${awayScore}`
  if (predict === result) return true
  else if (
    predict === '胜其他' &&
    !BF_SCORES.slice(0, 13).includes(result) &&
    homeScore > awayScore
  )
    return true
  else if (
    predict === '平其他' &&
    !BF_SCORES.slice(13, 18).includes(result) &&
    homeScore === awayScore
  )
    return true
  else if (predict === '负其他' && !BF_SCORES.slice(18).includes(result) && homeScore < awayScore)
    return true
  return false
}

/* 半全场玩法选项是否命中 */
function isBQCHit(
  predict: string,
  homeScore: number,
  awayScore: number,
  homeHalfScore: number,
  awayHalfScore: number,
) {
  if (predict === '胜胜' && homeHalfScore > awayHalfScore && homeScore > awayScore) return true
  if (predict === '胜平' && homeHalfScore > awayHalfScore && homeScore === awayScore) return true
  if (predict === '胜负' && homeHalfScore > awayHalfScore && homeScore < awayScore) return true
  if (predict === '平胜' && homeHalfScore === awayHalfScore && homeScore > awayScore) return true
  if (predict === '平平' && homeHalfScore === awayHalfScore && homeScore === awayScore) return true
  if (predict === '平负' && homeHalfScore === awayHalfScore && homeScore < awayScore) return true
  if (predict === '负胜' && homeHalfScore < awayHalfScore && homeScore > awayScore) return true
  if (predict === '负平' && homeHalfScore < awayHalfScore && homeScore === awayScore) return true
  if (predict === '负负' && homeHalfScore < awayHalfScore && homeScore < awayScore) return true
  return false
}

/* 进球玩法选项是否命中 */
function isJQHit(r: number, homeScore: number, awayScore: number) {
  if (homeScore + awayScore === r) return true
  if (r === 7 && homeScore + awayScore >= 7) return true
  return false
}

const SPFMainTxt = computed(() => {
  return (result: string, odd: string | null) => {
    if (!result) return '主队'
    const [rqStr] = odd ? odd.split(',') : ['0']
    const rq = parseInt(rqStr)
    return rq > 0 ? `主队+${rq}` : `主队${rq}`
  }
})

const SPFAwayTxt = computed(() => {
  return (result: string, odd: string | null) => {
    if (!result) return '客队'
    const [rqStr] = odd ? odd.split(',') : ['0']
    // const rq = parseInt(rqStr)
    const rq = 0 - parseInt(rqStr)
    return rq > 0 ? `客队+${rq}` : `客队${rq}`
  }
})

const isActiveClazz = computed(() => {
  return (active: boolean) =>
    active ? 'bg-#D1302E text-white' : 'border-1rpx border-solid border-black border-opacity-10'
})

const data = computed(() => {
  const { scheme } = props

  // return scheme && !isEmpty(scheme)
  //   ? scheme.map(({ matchPlays, ...r }) => ({
  //       ...r,
  //       matchPlays: matchPlays.reduce(
  //         (p, v) => {
  //           let val = v.result
  //           if (
  //             [PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, PLAY_TYPE.RQ, PLAY_TYPE.DXQ].includes(v.playId)
  //           ) {
  //             val = v.result.split(',').length === 1 ? [0, v.result].join(',') : v.result
  //           }

  //           if (v.type === 0) return { ...p, main: [...p.main, { ...v, result: val }] }
  //           return { ...p, bonus: [...p.bonus, { ...v, result: val }] }
  //         },
  //         {
  //           main: [],
  //           bonus: [],
  //         },
  //       ),
  //     }))
  //   : []

  return scheme && !isEmpty(scheme)
    ? scheme.map(({ matchPlays, ...r }) => ({
        ...r,
        matchPlays: matchPlays.reduce(
          (p, v) => {
            let val = v.result
            if (
              [PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, PLAY_TYPE.RQ, PLAY_TYPE.DXQ].includes(v.playId)
            ) {
              val = v.result.split(',').length === 1 ? [0, v.result].join(',') : v.result
            }

            const current = { ...v, result: val }

            if (v.type === 0) {
              // 主玩法
              if (v.playId === PLAY_TYPE.WIN_LOSE_DRAW) {
                // 非让球
                return { ...p, main: [current, ...p.main] }
              }

              if (v.playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW && !isEmpty(p.bonus)) {
                // 让球
                const [first, ...rest] = p.main
                return first.playId === PLAY_TYPE.WIN_LOSE_DRAW
                  ? { ...p, main: [first, current, ...rest] }
                  : { ...p, main: [current, first, ...rest] }
              }
              return { ...p, main: [...p.main, current] }
            }

            // 附赠玩法
            if (v.playId === PLAY_TYPE.WIN_LOSE_DRAW) {
              // 非让球
              return { ...p, bonus: [current, ...p.bonus] }
            }

            if (v.playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW && !isEmpty(p.bonus)) {
              // 让球
              const [first, ...rest] = p.bonus
              return first.playId === PLAY_TYPE.WIN_LOSE_DRAW
                ? { ...p, bonus: [first, current, ...rest] }
                : { ...p, bonus: [current, first, ...rest] }
            }

            return { ...p, bonus: [...p.bonus, current] }
          },
          {
            main: [],
            bonus: [],
          },
        ),
      }))
    : []
})

function gotoDetailPage(matchId: number) {
  uni.navigateTo({ url: `/pages/matchDetail/index?matchId=${matchId}` })
}
</script>

<style lang="scss" scoped>
.bqc {
  position: relative;
  display: grid;
  grid-template-rows: repeat(3, 100rpx);
  grid-template-columns: repeat(3, 200rpx);
  place-content: center;
  place-items: center;

  > text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    margin-top: -1rpx;
    margin-left: -1rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.1);
  }
}

.jq {
  position: relative;
  display: grid;
  grid-template-rows: repeat(2, 100rpx);
  grid-template-columns: repeat(4, 160rpx);
  place-content: center;
  place-items: center;

  > text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    margin-top: -1rpx;
    margin-left: -1rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.1);
  }
}

// mark
</style>
