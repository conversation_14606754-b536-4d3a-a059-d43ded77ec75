<route lang="json5" type="detail">
{
  style: {
    navigationBarTitleText: '神鱼体育',
    enablePullDownRefresh: false,
  },
}
</route>

<template>
  <view v-if="pushVisible && article.authorId != userStore.userInfo?.id">
    <subAuthorMp
      :article-id="article.id"
      :author-id="article.authorId"
      :avatar-url="article.authorAvatar"
      :shareType="shareType"
      :username="article.authorName"
    />
  </view>
  <view v-else-if="finishData" class="overflow-hidden bg-#FAFAFA min-h-screen pt-20rpx px-20rpx">
    <view>
      <view class="relative">
        <view class="flex flex-wrap items-center gap-x-16rpx leading-50rpx">
          <text
            v-if="article.schemePlay"
            :class="matchTypeClazz(article.schemePlay)"
            class="center w-50rpx h-28rpx text-20rpx rounded-16rpx border-1rpx border-solid"
          >
            {{ matchTypeTxt(article.schemePlay) }}
          </text>
          <text class="text-36rpx text-#333">{{ article.title }}</text>
        </view>
        <view class="flex items-center mt-10rpx mb-20rpx gap-x-30rpx">
          <text class="text-#999 text-22rpx">{{ publishTime(article.createTime) }}</text>
          <text class="text-#999 text-22rpx"  v-if="article.price">{{ `${article.buyNum}人购买` }}</text>
          <text
            v-if="article.win === 0 && article.refundType === 1"
            class="w-74rpx h-32rpx bg-#CF302C text-white text-22rpx rounded-8rpx"
          >
            不中退
          </text>
        </view>
        <template v-if="article.win === 1">
          <view class="absolute bottom-0 right-0">
            <image :src="hitIcon" class="w-100rpx h-100rpx" mode="scaleToFill" />
          </view>
        </template>
        <template v-if="article.win === 2">
          <view class="absolute bottom-0 right-0">
            <image :src="missIcon" class="w-100rpx h-100rpx" mode="scaleToFill" />
          </view>
        </template>
      </view>
      <view
        v-if="article?.belongAuthorPrivileges?.length > 0"
        class="flex items-center gap-x-10rpx mb-15rpx"
      >
        <text class="text-#999 text-22rpx">来自作者套餐</text>
        <view
          v-for="item in article.belongAuthorPrivileges"
          :key="item.id"
          class="text-#D1302E text-22rpx border border-solid bg-#FFECEE border-#D1302E rounded-xl px-10rpx py-5rpx"
          @click="goPrivilege(item)"
        >
          {{ item.privilegeName }}
        </view>
      </view>
      <view
        v-if="article.win === 0 && article.refundType === 1"
        class="flex items-center h-50rpx pl-14rpx text-#999 text-22rpx bg-#F3F4F5 rounded-4rpx"
      >
        本方案您享有不中退特权，若赛果和推荐不符，鱼币自动退还
      </view>
      <!-- 头 -->
      <view class="scheme-header bg-white rounded-lg shadow">
        <view class="flex justify-between">
          <image
            :src="article.authorAvatar"
            class="w-90rpx h-90rpx rounded-full"
            mode="scaleToFill"
            @click="toAuthorInfoPage"
          />
          <view class="flex flex-1 flex-col justify-between ml-20rpx mr-auto py-4rpx text-white">
            <text class="text-30rpx leading-42rpx text-#333 font-bold">
              {{ article.authorName }}
            </text>
            <text class="text-24rpx leading-32rpx text-#999999">{{ article.intro }}</text>
          </view>
          <text
            v-if="article.isAttention"
            class="center w-120rpx h-48rpx mt-4rpx border-1rpx border-solid border-#D1302E rounded-26rpx text-#D1302E text-26rpx leading-36rpx"
            @click="cancelFollowTheAuthor"
          >
            已关注
          </text>
          <text
            v-else
            class="center w-120rpx h-48rpx mt-4rpx border-1rpx border-solid border-#D1302E rounded-26rpx text-#D1302E text-26rpx leading-36rpx"
            @click="followTheAuthor"
          >
            +关注
          </text>
        </view>
        <view
          v-if="!isEmpty(article.currentResults) && article.accomplishment"
          class="flex flex-col gap-y-14rpx mt-30rpx"
        >
          <text class="text-#999999 text-22rpx leading-30rpx">
            {{
              `近5场战绩 : (红${
                article.currentResults.filter((i) => [1, 4, 5, 6, 8].includes(i)).length
              }黑${article.currentResults.filter((i) => [0, 2, 3, 7].includes(i)).length})`
            }}
          </text>
          <view class="flex gap-x-16rpx">
            <template v-for="(e, index) in article.currentResults" :key="`${e}-${index}`">
              <text
                v-if="[1, 4, 5, 6, 8].includes(e)"
                class="w-22rpx h-22rpx bg-#D1302E rounded-full"
              ></text>
              <text
                v-if="[0, 2, 3, 7].includes(e)"
                class="w-22rpx h-22rpx bg-#CBCBCB rounded-full"
              ></text>
            </template>
          </view>
        </view>
      </view>
      <view>
        <view
          v-for="(item, index) in article.articleAppendFreeList"
          :key="index"
          class="mt-[30rpx] rounded-lg shadow overflow-hidden"
        >
          <view class="p-[10rpx] bg-[#D1302E] bg-opacity-20">
            <text class="font">追加的免费内容</text>
            <text class="font ml-16">
              发布于: {{ dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            </text>
          </view>
          <view
            class="p-y-[30rpx] bg-white p-x-[10rpx]"
            v-html="item.contents"
          />
        </view>
      </view>
      <view v-if="article.isBuy">
        <view v-for="(item, index) in article.articleAppendList" :key="index" class="mt-[30rpx] rounded-lg shadow overflow-hidden">
          <view class="p-[10rpx] bg-[#59C76AFF] bg-opacity-20">
            <text class="font">追加的付费内容</text>
            <text class="font ml-16">
              发布于: {{ dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss") }}
            </text>
          </view>
          <view
            class="p-y-[30rpx] bg-white p-x-[10rpx]"
            v-html="item.contents"
          />
        </view>
      </view>
      <view @click="copyTxt(article.freeContents)" v-if="article.freeContents != '<p></p>' && article.freeContents != '<p><br></p>'"
            class="mt-[30rpx] rounded-lg shadow overflow-hidden">
        <view class="p-[10rpx] bg-[#D1302E] bg-opacity-20">
          <text class="font">免费内容</text>
        </view>
        <view
          class="p-y-[30rpx] bg-white p-x-[10rpx]"
          v-html="article.freeContents"
        />
      </view>
      <!-- 文章正文 -->
      <template v-if="article.isBuy">
        <view @click="copyTxt(article.contents)" class="mt-[30rpx] rounded-lg shadow overflow-hidden">
          <view class="p-[10rpx] bg-[#59C76AFF] bg-opacity-20">
            <text class="font">付费内容</text>
          </view>
          <view
            class="p-y-[30rpx] bg-white p-x-[10rpx]"
            v-html="article.contents"
          />
        </view>

        <view v-if="article.win !== 0 && article.conclusion && article.conclusion != ''">
          备注:{{ article.conclusion }}
        </view>
      </template>
      <template v-else>
        <view
          v-if="article.authorId"
          class="flex flex-col pt-[30rpx] p-x-[30rpx] pb-[48px] bg-white mt-30rpx"
        >
          <view class="no-buy">
            <text class="text-[#D1302E] font-normal" style="font-size: 36rpx">
              购买后可查看方案
            </text>
            <text
              class="font-normal mt-[20rpx] mb-[10rpx]"
              style="font-size: 30rpx; color: rgba(0, 0, 0, 0.5)"
            >
              *本页面所有内容及图片仅代表发布者个人观点*
            </text>
            <text class="font-normal" style="font-size: 30rpx; color: rgba(0, 0, 0, 0.5)">
              *请读者仅作参考！*
            </text>
          </view>
        </view>
      </template>
      <result
        :is-buy="article.isBuy"
        :issue="article?.issue"
        :scheme="scheme"
        :scheme-play="article.schemePlay"
        :sell-out-time="article.sellOutTime"
      />

      <!-- 战绩 -->
      <view v-if="article.accomplishmentShowPic" class="flex flex-col gap-y-30rpx">
        <text class="text-30rpx text-black text-opacity-90">战绩：</text>
        <image :src="article.accomplishmentShowPic" mode="scaleToFill" />
      </view>

      <!-- <view class="p-y-[30rpx] bg-white" style="word-break: break-all">
        <text>{{ article.freeContents }}</text>
      </view> -->

      <!-- 其他文章 -->
      <view
        v-if="article.recommend && false"
        class="flex flex-col rounded-xl m-y-[30rpx] pt-[30rpx] p-x-[30rpx] pb-[34rpx] mt-[20rpx] bg-white"
        @click="gotoOther(article.recommend.id)"
      >
        <text class="font-normal" style="font-size: 32rpx; color: rgba(0, 0, 0, 0.5)">
          其他文章
        </text>
        <text
          class="font-normal mt-[20rpx] mb-[10rpx]"
          style="font-size: 32rpx; color: rgba(0, 0, 0, 0.9)"
        >
          {{ article.recommend.title }}
        </text>
        <view class="flex justify-between items-center">
          <text class="mr-auto font-normal" style="font-size: 26rpx; color: rgba(0, 0, 0, 0.5)">
            {{ publishTime(article.recommend.createTime) }}
          </text>
          <image
            class="w-[40rpx] h-[40rpx]"
            src="https://sacdn.850g.com/football/static/gold.svg"
          />
          <text class="ml-[3rpx] text-[#D1302E] font-normal" style="font-size: 30rpx">
            {{ article.recommend.price }}
          </text>
          <text class="ml-[20rpx] font-normal" style="font-size: 26rpx; color: rgba(0, 0, 0, 0.5)">
            {{ article.recommend.buyNum }}人已购买
          </text>
        </view>
      </view>

      <view class="my-[30rpx] text-[#D1302E] text-28rpx">
        *温馨提示：此观点仅代表作者，不代表平台*
      </view>
    </view>
    <view v-if="article.isBuy == 0" class="w-full fixed bottom-0 left-0 bg-white z-1">
      <view v-if="article.privileges?.length < 1">
        <view
          v-if="article?.belongAuthorPrivileges?.length > 0"
          class="flex justify-between items-center px-20rpx py-20rpx bg-[linear-gradient(180deg,#FFE7B7_0%,#E4B566_100%)]"
          @click="comboVisible = true"
        >
          <view class="flex items-center">
            <view class="mr-5rpx">
              <image
                class="w-[50rpx] h-[25rpx]"
                src="https://sacdn.850g.com/football/static/vip-icon.png"
              ></image>
            </view>
            <text class="text-28rpx text-#844E02 font-medium">
              订阅套餐，单文仅需{{article?.privilegeArticlePrice}}鱼币最多可省{{ article?.privilegeReducePrice }}元
            </text>
          </view>
          <view>
            <wd-icon color="#844E02" name="arrow-right" size="22px"></wd-icon>
          </view>
        </view>
        <view class="flex justify-between items-center bg-white p-20rpx shadow">
          <view>
            <view class="text-26rpx text-#999999">解锁本文内容</view>
            <view class="text-26rpx text-#333333 font-medium">
              需支付:
              <text class="text-#D2302E">{{ article.price }}鱼币</text>
            </view>
          </view>
          <view>
            <view
              class="flex justify-center items-center w-[310rpx] h-[80rpx] text-white bg-[#D1302E] rounded"
              style="font-size: 32rpx; border: 1px solid #d1302e"
              @click="showPayModal"
            >
              确认支付
            </view>
          </view>
        </view>
      </view>
      <view v-else class="flex justify-between items-center bg-white p-20rpx shadow">
        <view v-if="priorityPrivileges.endDate" class="text-[#bfbcbc] text-26rpx">
          到期时间:{{ priorityPrivileges.endDate }}
        </view>

        <view v-else class="text-[#bfbcbc] text-26rpx">剩余次数:{{ priorityPrivileges.num }}</view>
        <view
          class="flex justify-center items-center w-[310rpx] h-[80rpx] text-white bg-[#D1302E] rounded"
          @click="handlePrivilegesFreeClick"
        >
          特权免费查看
        </view>
      </view>
    </view>
    <wd-message-box selector="delete-article" />

    <!--  套餐订阅弹框  -->
    <wd-popup
      v-model="comboVisible"
      :close-on-click-modal="false"
      custom-style="border-top-left-radius:16rpx;border-top-right-radius:16rpx;"
      position="bottom"
    >
      <view class="flex flex-col relative py-[30rpx] p-x-[40rpx]">
        <!-- 关闭按钮 -->
        <wd-icon
          class="absolute top-[20rpx] right-[50rpx]"
          name="close"
          size="30rpx"
          @click="comboVisible = false"
        />
        <view class="text-center text-30rpx font-medium mb-30rpx">套餐选择</view>
        <wd-collapse v-model="comboActive" accordion>
          <wd-collapse-item v-for="item in article?.belongAuthorPrivileges" :key="item.id" :name="item.id.toString()">
            <template #title>
              <view class="flex items-center justify-between">
                <view class="flex items-center text-24rpx">
                  <view>
                    <image :src="article.authorAvatar" class="w-100rpx h-100rpx rounded-full"></image>
                  </view>
                  <view class="ml-20rpx">
                    <view class="text-28rpx font-medium">{{item.privilegeName}}</view>
                    <view class="text-#999">共{{item.num}}篇方案 | 每日更新{{item.date}}篇方案</view>
                  </view>
                </view>
                <view>
                  <wd-icon v-if="comboActive === item.id.toString()" name="arrow-down" size="22px" color="#999"></wd-icon>
                  <wd-icon v-else name="arrow-right" size="22px" color="#999"></wd-icon>
                </view>
              </view>
            </template>
            <view
              v-for="child in item.children"
              :key="child.id"
              class="relative rounded-8rpx border-2rpx px-30rpx py-24rpx mb-20rpx transition-all duration-200 flex flex-col border border-solid"
              :class="selectedCardId === child.id ? 'bg-#fff4f4 border-#d1302e' : 'bg-white border-#eee'"
              @click="selectedCardId = child.id"
            >
              <view class="flex items-center mb-10rpx">
                <view class="bg-#d1302e text-white text-20rpx rounded-12rpx px-10rpx py-2rpx mr-12rpx">VIP</view>
                <text class="text-28rpx font-bold text-#333">{{ child.days }}天卡</text>
              </view>
              <view class="flex justify-between items-end">
                <view class="text-22rpx text-#999">
                  单篇 {{ child.articlePrice || '0' }}元&nbsp;&nbsp;省{{ child.reducePrice || '0' }}元
                </view>
                <view class="flex items-center">
                  <text class="text-32rpx font-bold text-#d1302e">{{ child.price }}元</text>
                </view>
              </view>
              <image
                v-if="selectedCardId === child.id"
                class="absolute right-[-1rpx] bottom-0 w-36rpx h-36rpx"
                src="https://sacdn.850g.com/football/static/vip-checked.png"
              />
            </view>
          </wd-collapse-item>
        </wd-collapse>
        <view class="flex items-center py-[40rpx]">
          <wd-checkbox v-model="checkVal" checked-color="#d1302e"></wd-checkbox>
          <text class="font-size-[24rpx] text-[#666]" @click="showAgreement">
            请仔细阅读并勾选
            <text class="text-[#d1302e]">《用户购买协议》</text>
          </text>
        </view>
        <view
          @click="submitComboOrder"
          class="flex justify-center items-center w-full h-80rpx rounded bg-#D1302E text-32rpx text-white"
        >
          立即订阅
        </view>
      </view>
    </wd-popup>
    <!-- 支付弹框 -->
    <wd-popup
      v-model="visible"
      :close-on-click-modal="false"
      custom-style="border-top-left-radius:16rpx;border-top-right-radius:16rpx;"
      position="bottom"
    >
      <view class="flex flex-col relative py-[30rpx] p-x-[40rpx]">
        <!-- 关闭按钮 -->
        <wd-icon
          class="absolute top-[20rpx] right-[50rpx]"
          name="close"
          size="30rpx"
          @click="hidePayModal"
        />
        <view class="text-center text-30rpx font-medium mb-30rpx">确认支付</view>
        <!-- 价格 -->
        <view class="flex justify-center items-baseline font-semibold text-60rpx">
          {{ article.price }}
          <text class="text-30rpx">鱼币</text>
        </view>
        <view class="text-center text-24rpx text-#999999">1鱼币 = ¥1.00元</view>
        <view class="flex items-center pt-[100rpx] pb-[40rpx]">
          <wd-checkbox v-model="checkVal" checked-color="#d1302e"></wd-checkbox>
          <text class="font-size-[24rpx] text-[#666]" @click="showAgreement">
            请仔细阅读并勾选
            <text class="text-[#d1302e]">《用户购买协议》</text>
          </text>
        </view>
        <!-- 鱼币支付 -->
        <view
          v-if="isDirectPay"
          class="flex justify-center items-center w-full h-80rpx rounded bg-#D1302E text-32rpx text-white"
          @click="submitOrder(PAY_TYPE.CURRENCY)"
        >
          确认支付
        </view>
        <!-- 微信支付 -->
        <view
          v-else
          class="flex justify-between items-center"
          @click="submitOrder(PAY_TYPE.WECHAT)"
        >
          <image class="w-[40rpx] h-[40rpx]" src="https://sacdn.850g.com/football/static/wx.svg" />
          <text class="ml-20rpx mr-auto">微信支付</text>
          <view
            class="flex justify-center items-center w-300rpx h-100rpx rounded-xl bg-#D1302E text-32rpx text-white"
          >
            充值并支付{{ article.price }}鱼币
          </view>
        </view>
      </view>
    </wd-popup>
    <!-- 接受推送消息二维码弹框 -->
    <mp-modal ref="mpRef" :code="mpAppInfo?.appQrCode" :type="mpAppInfo?.type" />
    <!-- 作者其他文章 -->
    <view v-if="!isEmpty(otherArticleList)" class="mb-220rpx p-14rpx bg-white shadow rounded-16rpx">
      <view class="ml-10rpx mb-20rpx text-32rpx leading-45rpx">其他在售</view>
      <view
        v-for="o in otherArticleList"
        :key="o.id"
        class="flex flex-col pt-20rpx pb-30rpx border-b-2rpx border-b-solid border-b-#F3F3F3 last:border-none"
        @click="gotoOther(o.id)"
      >
        <text class="text-30rpx text-#333 leading-42rpx">
          {{ otherContent(o.schemePlay, o.title) }}
        </text>
        <view
          class="flex items-center w-full h-54rpx my-12rpx px-10rpx bg-#F3F4F5 text-#999 text-22rpx box-border"
        >
          <text
            v-if="o.schemePlay"
            :class="matchTypeClazz(o.schemePlay)"
            class="center w-50rpx h-28rpx text-20rpx rounded-16rpx border-1rpx border-solid"
          >
            {{ matchTypeTxt(o.schemePlay) }}
          </text>
          <text class="ml-10rpx mr-20rpx shrink-0">
            {{ format(o.matchTime * 1000, 'MM-DD HH:mm') }}
          </text>
          <text class="mr-20rpx shrink-0">{{ o.competitionName }}</text>
          <text class="flex-1 shrink-0 truncate">{{ `${o.homeTeamName}vs${o.awayTeamName}` }}</text>
        </view>
        <view class="flex-1 flex justify-between items-center">
          <text class="text-22rpx text-#999">{{ `发布于${fromNow(o.createTime)}` }}</text>
          <view class="flex items-center">
            <text class="text-22rpx text-#999">{{ `${o.buyCount}人购买` }}</text>
            <image
              class="w-28rpx h-28rpx ml-20rpx"
              mode="scaleToFill"
              src="https://sacdn.850g.com/football/static/gold.svg"
            />
            <text class="text-22rpx text-#CF302C">{{ o.price }}</text>
          </view>
        </view>
      </view>
    </view>
    <wd-fab
      v-if="!mpVisible && !visible && pushVisible && article.authorId !== userStore.userInfo?.id"
      :expandable="false"
      class="mt-[-220rpx] ml-[45rpx]"
      position="right-bottom"
    >
      <template #trigger>
        <view class="revice" style="width: 180rpx; height: 180rpx" @click="showQrcode">
          <image class="w-full h-full" src="/static/images/button/revice.gif" />
        </view>
      </template>
    </wd-fab>
    <buyAgreement ref="buyAgreementRef" @pass="passAgreement" />
    <!-- <back v-if="!mpVisible && !visible" /> -->
    <!-- <qqMail /> -->
    <subMp
      v-if="
        attentionVisible && article.authorId !== userStore.userInfo?.id && article.isAttention === 0
      "
      ref="subMpRef"
      :article-id="article.id"
      :author-id="article.authorId"
    />
    <wd-fab
      v-else
      :expandable="false"
      class="mt-[-400rpx] ml-[-10rpx] rounded-[50%]"
      position="right-bottom"
    >
      <template #trigger>
        <view
          class="flex items-center justify-center flex-col w-[70rpx] h-[70rpx] bg-[#d62c2c] rounded-[50%] text-[22rpx] text-white text-center"
          @click="complaintsShow = true"
        >
          <wd-row>投诉</wd-row>
          <wd-row>举报</wd-row>
        </view>
      </template>
    </wd-fab>
    <qqDialog ref="qqDialogRef" />
  </view>
  <wd-popup
    v-model="complaintsShow"
    custom-style="border-top-left-radius:16rpx;border-top-right-radius:16rpx;background-color: #f8f8f8;"
    position="bottom"
  >
    <view @click="handleMsgClose" class="py-30rpx text-center bg-[white] text-#D2302E">不在接收此消息</view>
    <view @click="goToComplaints" class="py-30rpx text-center bg-[white]">投诉</view>
    <view @click="complaintsShow = false" class="py-30rpx text-center bg-[white] mt-20rpx">取消</view>
  </wd-popup>
  <wd-fab
    v-if="!mpVisible && !visible && pushVisible && article.authorId !== userStore.userInfo?.id"
    :expandable="false"
    class="mt-[-340rpx] ml-[45rpx]"
    position="right-bottom"
  >
    <template #trigger>
      <view class="revice" style="width: 180rpx; height: 180rpx" @click="showQrcode">
        <image
          class="w-full h-full"
          src="https://sacdn.850g.com/football/static/button/revice.gif"
        />
      </view>
    </template>
  </wd-fab>
  <wd-fab
    v-if="article.authorId == userStore.userInfo.id && !mpVisible && !visible"
    :expandable="false"
    class="mt-[-260rpx]"
    position="right-bottom"
  >
    <template #trigger>
      <view class="revice flex justify-center items-center" @click="goShare">
        <image
          class="w-full h-full"
          src="https://sacdn.850g.com/football/static/button/share.png"
        />
      </view>
    </template>
  </wd-fab>
  <articleShare ref="shareRef" />
  <buyAgreement ref="buyAgreementRef" @pass="passAgreement" />
  <!-- <back v-if="!mpVisible && !visible && article.publicUser" />
  <authorBack v-if="!mpVisible && !visible && !article.publicUser" :authorId="article.authorId" /> -->
  <wd-popup v-model="packageVisible" :close-on-click-modal="false" position="bottom">
    <wd-icon
      class="absolute top-[20rpx] right-[30rpx]"
      name="close"
      size="30rpx"
      @click="packageVisible = false"
    />
    <view class="flex flex-col p-[30rpx] modal">
      <view class="text-center text-36rpx font-bold mb-[40rpx]">选择套餐卡使用</view>

      <view
        v-for="privilege in article.privileges"
        :key="privilege.type"
        class="flex items-center justify-between p-[30rpx] border-b border-gray-200"
        @click="activatePackage(privilege)"
      >
        <view class="flex items-center">
          <text class="text-32rpx mr-[10rpx]">
            {{ getPrivilegeName(privilege) }}
          </text>
        </view>
        <view class="flex items-center">
          <wd-icon name="arrow-right" size="30rpx" />
        </view>
      </view>

      <view class="flex items-center justify-between p-[30rpx]" @click="directPay()">
        <text class="text-32rpx">直接支付</text>
        <wd-icon name="arrow-right" size="30rpx" />
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { getCurrentInstance } from "vue";
import {
  IArticleDetail,
  addAuthorAttention,
  getArticleDetail,
  removeAutionAttention,
  canUserShowPush,
  IArticleMatchInfo,
  showAttention,
  checkArticleStatus,
  IMatchSchem
} from "@/api/article";
import articleShare from "@/components/articleShare/article-share.vue";

import {
  IOtherArticleItem,
  deletedArticle,
  getGameplay,
  getOtherAuthorArticles
} from "@/api/author";
import { generateOrder } from "@/api/order";
import {
  getAppQrcode,
  getUserInfo,
  requestGiftRetio,
  getMatchTypes,
  activatePrivilege
} from "@/service/userService";
import { ORDER_STATUS, PAY_TYPE, SCHEME_TYPE } from "@/utils/enum";
import { fromNow } from "@/utils/relative";
import { useUserStore, useScanStore } from "@/store";
import dayjs from "dayjs";
// import { isNil } from 'lodash-es'
import { useMessage } from "wot-design-uni";
import subAuthorMp from "@/components/subAuthorMp/index.vue";
import subMp from "@/components/subMp/index.vue";
import buyAgreement from "@/components/buyAgreement/index.vue";
import qqMail from "@/components/qqmail/index.vue";
import mpModal from "@/components/mpModal/index.vue";
import qqDialog from "@/components/qq/qq-dialog.vue";
import back from "@/components/back/index.vue";
import authorBack from "@/components/authorBack/index.vue";
import { onBridgeReady } from "@/utils/wxPay";
import { cleanUrl } from "@/utils/sqbPay";
import result from "./components/result.vue";
import { IMatchSchemeItem, getMatchPlayOdds } from "@/api/match";
import { isEmpty } from "lodash-es";
import hitIcon from "@/static/icons/hit.svg";
import missIcon from "@/static/icons/miss.svg";
import wx from "weixin-js-sdk";
import { getWxJsConfig } from "@/api/wx";
import { format } from "@/utils/format";

const qqDialogRef = ref();
const subMpRef = ref();
const message = useMessage("delete-article");
const userStore = useUserStore();
const scanStore = useScanStore();
const packageVisible = ref(false);
const {
  // @ts-expect-error
  proxy: { $onLaunched }
} = getCurrentInstance();
const loading = ref(false);
const visible = ref(false);
const comboVisible = ref(false);
const comboActive = ref();
const selectedCardId = ref<number | null>(null);
const articleId = ref<number>();
const article = ref<IArticleDetail>({} as IArticleDetail);
// const payType = ref<PAY_TYPE>(PAY_TYPE.CURRENCY)
const checkVal = ref(true);
const buyAgreementRef = ref();
const shareRef = ref();
const mpVisible = ref(false);
const pushVisible = ref(false);
const attentionVisible = ref(false);
const finishData = ref(false);
const mpAppInfo = ref();
const mpRef = ref<typeof mpModal>();
const giftRetio = ref(0);
const shareType = ref(0);
const privilegeId = ref(null);
const priorityPrivileges = ref({});

const otherArticleList = ref<IOtherArticleItem[]>([]);

const handleMsgClose = () => {
  complaintsShow.value = false;
  uni.showToast({
    title: "已关闭推送消息",
    icon: "success"
  })
};

const goToComplaints = () => {
  complaintsShow.value = false;
    uni.navigateTo({
      url: `/pages/complaints/index`
    });
};

const matchTypeTxt = computed(() => {
  return (sp: SCHEME_TYPE) => {
    switch (sp) {
      case SCHEME_TYPE.MATCH_LOTTERY:
        return "14场";
      case SCHEME_TYPE.ANY_NINE:
        return "任9";
      case SCHEME_TYPE.TEAM_PARLAY:
      case SCHEME_TYPE.SINGLE:
      case SCHEME_TYPE.MULTI_TEAM_PARLAY:
        return "竞足";
      case SCHEME_TYPE.SINGLE_GAME_BET:
      case SCHEME_TYPE.TWO_SELECTIONS_PARLAY:
      case SCHEME_TYPE.MULTIPLE_SELECTIONS_PARLAY:
        return "足球";
    }
  };
});

const complaintsShow = ref(false);
const complaintsReportRef = ref();
const complaintsConfig = () => {
  complaintsReportRef.value.confirm();
};

const handlePrivilegesFreeClick = () => {
  privilegeId.value = priorityPrivileges.value.id;
  submitOrder(
    priorityPrivileges.value.type === 0
      ? PAY_TYPE.PRIVILEGE_TIME
      : priorityPrivileges.value.type === 4
        ? PAY_TYPE.PRIVILEGE_PACKAGE
        : PAY_TYPE.PRIVILEGE_NUMBER
  );
};

const matchTypeClazz = computed(() => {
  return (sp: SCHEME_TYPE) => {
    switch (sp) {
      case SCHEME_TYPE.MATCH_LOTTERY:
        return "text-#60B53F border-#60B53F";
      case SCHEME_TYPE.ANY_NINE:
        return "text-#FF7600 border-#FF7600";
      case SCHEME_TYPE.TEAM_PARLAY:
      case SCHEME_TYPE.SINGLE:
      case SCHEME_TYPE.MULTI_TEAM_PARLAY:
        return "text-#CF302C border-#CF302C";
      case SCHEME_TYPE.SINGLE_GAME_BET:
      case SCHEME_TYPE.TWO_SELECTIONS_PARLAY:
      case SCHEME_TYPE.MULTIPLE_SELECTIONS_PARLAY:
        return "text-#4188DF border-#4188DF";
    }
  };
});

function copyTxt(txt: string) {
  uni.setClipboardData({
    data: txt,
    success: () => {
      uni.showToast({
        // 提示
        title: "复制成功"
      });
    }
  });
}

const getPrivilegeName = (privilegeInfo) => {
  if (privilegeInfo.privilegeName) {
    return privilegeInfo.privilegeName;
  } else {
    return (
      privilegeInfo.num +
      (privilegeInfo.type === 0 ? "天" : "次") +
      (privilegeInfo.matchType !== 0 ? getMatchName(privilegeInfo.matchType) : "") +
      "套餐"
    );
  }
};

function otherContent(sp: SCHEME_TYPE | null, title: string) {
  let schemePlayTxt = "";
  switch (sp) {
    case SCHEME_TYPE.MATCH_LOTTERY:
      schemePlayTxt = "【14场】";
    case SCHEME_TYPE.ANY_NINE:
      schemePlayTxt = "【任9】";
    case SCHEME_TYPE.TEAM_PARLAY:
    case SCHEME_TYPE.SINGLE:
    case SCHEME_TYPE.MULTI_TEAM_PARLAY:
      schemePlayTxt = "【竞足】";
    case SCHEME_TYPE.SINGLE_GAME_BET:
    case SCHEME_TYPE.TWO_SELECTIONS_PARLAY:
    case SCHEME_TYPE.MULTIPLE_SELECTIONS_PARLAY:
      schemePlayTxt = "【足球】";
  }

  return `${schemePlayTxt}${title}`;
}

const getWinColor = (win) => {
  switch (win) {
    case 1:
      return "#d1302e";
    case 2:
      return "black";
    case 3:
      return "green";
    case 4:
    case 5:
    case 6:
      return "#ff7a00";
    case 7:
      return "black";
    case 8:
      return "#ff7a00";
  }
};

const getWinName = (win, index?) => {
  switch (win) {
    case 1:
      return "红";
    case 2:
      return "黑";
    case 3:
      return "走";
    case 4:
      return "红";
    case 5:
      return "红";
    case 6:
      return "红";
    case 7:
      return "黑";
    case 8:
      if (index || index === 0) {
        return article.value.currentResultsName[index];
      } else {
        return "未";
      }
  }
};
const nobackAuthor = ref([68033, 73669, 68377, 77939]);

const goPrivilege = (item) => {
  uni.navigateTo({
    url: `/pages/authorPackageDetail/index?curAuthorId=${item.authorId}&packageId=${item.id}`
  });
};

// 是否使用鱼币支付
const isDirectPay = computed(() => {
  if (!article.value.price) return true;
  const remain = userStore.userInfo.gold || 0;
  return !(article.value.price > remain);
});

const submitComboOrder = async () => {
  if (!checkVal.value) {
    showAgreement();
    return;
  }
  if (!selectedCardId.value) {
    uni.showToast({
      title: "请选择套餐卡",
      icon: "none"
    });
    return;
  }
  const redirectUrl = cleanUrl(window.location.href)
  loading.value = true;
  try {
    const order = await generateOrder(
      article.value.id,
      PAY_TYPE.WECHAT,
      redirectUrl,
      shareType.value,
      selectedCardId.value
    );
    loading.value = false;
    if (order.status === ORDER_STATUS.SUCCESS) {
      await uni.showToast({
        title: "订单生成成功",
        icon: "none"
      });
      await getData();
      comboVisible.value = false;
    } else {
      await uni.showToast({
        title: "订单生成失败",
        icon: "none"
      });
    }
  } catch (error) {
    loading.value = false;
    console.info(error)
    await uni.showToast({
      title: error.data?.message || "订单生成失败",
      icon: "none"
    });
  }
};

const showAgreement = () => {
  buyAgreementRef.value.showDialog();
};
const passAgreement = () => {
  checkVal.value = true;
};
const checkPushShow = async () => {
  const result = await canUserShowPush(article.value.authorId);
  pushVisible.value = result;
  // if (pushVisible.value) {
  //   showQrcode()
  // }
};

const checkAttentionShow = async () => {
  const result = await showAttention(article.value.authorId);
  attentionVisible.value = result;
};

const showQrcode = () => {
  mpVisible.value = true;
};

const publishTime = computed(() => {
  return (datetime: dayjs.ConfigType) => {
    if (!datetime) return "";

    return `发布于${fromNow(datetime)}`;
  };
});

const winClass = computed(() => {
  return (win: number) => {
    switch (win) {
      case 1:
        return "win_red";
      case 2:
      case 7:
        return "win_black";
      case 4:
      case 5:
      case 6:
      case 8:
        return "win_yan";
      case 3:
        return "win_blue";
      default:
        return "win_now";
    }
  };
});
const matchTypes = ref([]);
const getMatchName = (type) => {
  const match = matchTypes.value.find((item) => item.id === type);
  return match ? match.name : "";
};

const getMatchType = async () => {
  matchTypes.value = await getMatchTypes();
};

const goAuthorPage = (authorId: number) => {
  uni.navigateTo({
    url: `/pages/author/info/index?authorId=${authorId}&ts=${Date.now()}`,
  })
}

const goBuyUserInfo = (id: number) => {
  uni.navigateTo({
    url: `/pages/detail/user/index?articleId=${id}`
  });
};

function followTheAuthor() {
  addAuthorAttention(article.value.authorId).then((ret) => {
    if (ret) {
      uni.showToast({ title: "关注成功", icon: "none" });
      getArticleDetail(articleId.value).then((data) => {
        article.value = data;
      });
    }
  });
}

function cancelFollowTheAuthor() {
  removeAutionAttention(article.value.authorId).then((ret) => {
    if (ret) {
      uni.showToast({ title: "已取消关注", icon: "none" });
      getArticleDetail(articleId.value).then((data) => {
        article.value = data;
      });
    }
  });
}

function gotoOther(id: number) {
  uni.navigateTo({
    url: `/pages/detail/index?id=${id}`
  });
}

function goShare() {
  shareRef.value.showDialog(article.value);
}

function goEdit() {
  uni.navigateTo({
    url: `/pages/article/relaese/index?id=${article.value.id}`
  });
}

function goAddFree() {
  uni.navigateTo({
    url: `/pages/article/relaese/index?id=${article.value.id}&type=1`
  });
}

function goAddPaid() {
  uni.navigateTo({
    url: `/pages/article/relaese/index?id=${article.value.id}&type=2`
  });
}

function goDelete() {
  message
    .confirm({
      title: "确定删除该文章吗？",
      msg: "删除后将无法恢复，请谨慎操作！"
    })
    .then(async () => {
      await deletedArticle(article.value.id);
      uni.showToast({
        title: "删除成功",
        icon: "none"
      });
      uni.navigateBack();
    });
}

function hidePayModal() {
  visible.value = false;
}

function showPayModal() {
  if (!checkVal.value) {
    showAgreement();
  } else {
    // visible.value = true
    // 判断是否有可用的特权
    const hasPrivileges = article.value.privileges && article.value.privileges.length > 0;

    if (hasPrivileges) {
      // 显示套餐选择弹窗
      packageVisible.value = true;
      visible.value = false;
    } else {
      // 直接显示支付弹窗
      packageVisible.value = false;
      visible.value = true;
    }
  }
}

// 激活套餐
function activatePackage(privilege) {
  // packageVisible.value = false
  if (privilege.activate === 0) {
    const name = privilege.matchType !== 0 ? getMatchName(privilege.matchType) : "";
    message
      .confirm({
        title: "是否激活" + privilege.num + "天" + name + "包时卡？"
      })
      .then(async () => {
        await activatePrivilege(privilege.id);
        uni.showToast({
          icon: "success",
          title: "激活成功"
        });
        await getData();
      });
  } else {
    const title =
      privilege.type == 1
        ? "包次"
        : privilege.type == 0
          ? "包时"
          : privilege.type == 4
            ? "套餐包"
            : "补单";
    message
      .confirm({
        title: "确定使" + title + "特权解锁？"
      })
      .then(async () => {
        privilegeId.value = privilege.id;
        submitOrder(
          privilege.type === 0
            ? PAY_TYPE.PRIVILEGE_TIME
            : privilege.type === 4
              ? PAY_TYPE.PRIVILEGE_PACKAGE
              : PAY_TYPE.PRIVILEGE_NUMBER
        );
        packageVisible.value = false;
      });
  }
}

// 直接支付
function directPay() {
  packageVisible.value = false;
  visible.value = true; // 显示原来的支付弹窗
}

function privilegeSubmit() {
  message
    .confirm({
      title: article.value.privilegeType == 1 ? "确定使用包次特权解锁？" : "确定使用补单特权解锁？",
      msg: "剩余:" + article.value.privilegeNum + "次"
    })
    .then(async () => {
      submitOrder(PAY_TYPE.PRIVILEGE_NUMBER);
    });
}

const checkQQ = () => {
  setTimeout(() => {
    // 判断是否需要弹窗填写qq
    if (article.value.showQQ == true) {
      // qqDialogRef.value.showQQ()
    }
  }, 500);
};

// 微信分享配置接口
interface ShareConfig {
  title: string;
  desc: string;
  link: string;
  imgUrl: string;
  success?: () => void;
  cancel?: () => void;
}

/* 支付订单 */
async function submitOrder(type: PAY_TYPE) {
  if (loading.value) return; // 上一笔订单正在支付中
  loading.value = true;

  try {
    // const data = await generateOrder(article.value.id, type, shareType.value)
    const redirectUrl = cleanUrl(window.location.href);
    const data = await generateOrder(
      article.value.id,
      type,
      redirectUrl,
      shareType.value,
      privilegeId.value
    );
    if (!data) return;
    const { status, wxJsapiParams, payUrl } = data;
    if (
      type === PAY_TYPE.CURRENCY ||
      type === PAY_TYPE.PRIVILEGE_NUMBER ||
      type === PAY_TYPE.PRIVILEGE_TIME ||
      type === PAY_TYPE.PRIVILEGE_PACKAGE
      // type === PAY_TYPE.PRIVILEGE_FOURTEEN
    ) {
      // 鱼币支付、使用补单、使用特权
      if (status === ORDER_STATUS.SUCCESS) {
        uni.showToast({
          icon: "success",
          title: "支付成功"
        });
        hidePayModal();

        await getData();
        checkQQ();
      } else if (status === ORDER_STATUS.FAIL) {
        uni.showToast({
          icon: "fail",
          title: "支付失败"
        });
      }

      await getArticleDetail(articleId.value).then((d) => {
        article.value = d;
      });
    } else {
      if (status === ORDER_STATUS.PENDING) {
        // location.href = payUrl
        onBridgeReady(
          wxJsapiParams,
          () => {
            setTimeout(() => {
              getArticleDetail(articleId.value).then((d) => {
                article.value = d;
              });
              hidePayModal();
            }, 3000);
          },
          () => {
            console.log("支付失败");
          }
        );

        // window.location.href = payUrl
      }
    }
  } finally {
    loading.value = false;
  }
}

watch(article, async (val) => {
  if (val) {
    uni.setNavigationBarTitle({ title: "" });
  }
});

const getGiftRetio = async () => {
  const data = await requestGiftRetio();
  giftRetio.value = data;
};

const addFreeContents = ref([
  {
    content: "",
    createTime: ""
  }
]);

const addContents = ref([
  {
    content: "",
    createTime: ""
  }
]);

const scheme = ref<(IMatchSchem & { articleMatchInfo: IArticleMatchInfo })[]>([]);

// const articleMatchInfo = ref<IArticleMatchInfo[]>([])

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

const toAuthorInfoPage = () => {
  goAuthorPage(article.value.authorId);
};

const getArticleStatus = async (id) => {
  // 检查文章状态
  // 对下架的文章进行提示 并跳转到作者主页
  const data = await checkArticleStatus(id);

  if (!data.status) {
    const authorId = data.authorId;
    if (authorId) {
      uni.showToast({
        title: "该文章已下架",
        icon: "none"
      });
    } else {
      uni.showToast({
        title: "该文章已删除",
        icon: "none"
      });
    }
    await sleep(2000);
    if (authorId) {
      uni.redirectTo({
        url: `/pages/author/info/index?authorId=${authorId}&ts=${Date.now()}`,
      })
    } else {
      uni.switchTab({
        url: "/pages/index/index"
      });
    }
  }
};

const getData = async () => {
  // const data = await getArticleDetail(articleId.value)
  const [data, gamePlaies] = await Promise.all([getArticleDetail(articleId.value), getGameplay()]);
  let ms: Omit<IMatchSchem, "odds">[] = [];

  const { articleMatchInfo, schemePlay } = data;
  if (data.matchScheme) ms = JSON.parse(data.matchScheme);
  if (!isEmpty(ms)) {
    // const dataType = ms[0].matchPlays[0].dataType || 1
    const ids = ms.map(({ matchId }) => matchId).join(",");
    const gamePlay = gamePlaies.find(({ id }) => id === schemePlay);
    const dataType = gamePlay ? gamePlay.dataType : 1;
    const o = await getMatchPlayOdds(ids, dataType);

    scheme.value = ms.map((m) => ({
      ...m,
      odds: o.find(({ matchId }) => m.matchId === matchId),
      articleMatchInfo:
        articleMatchInfo.find(({ matchId }) => matchId === m.matchId) ?? ({} as IArticleMatchInfo)
    }));
  }

  article.value = data;
  article.value?.privileges.forEach((element) => {
    priorityPrivileges.value = element;
    if (element.type === 4 || element.type === 0) {
    }
  });
  priorityPrivileges.value = priorityPrivileges.value || article.value?.privileges[0];
  addFreeContents.value = JSON.parse(article.value.addFreeContent);
  addContents.value = JSON.parse(article.value.addContent);
  if (isRefresh.value) {
    // 提示刷新成功
    uni.showToast({ title: "刷新成功", icon: "none" });
  }
  isRefresh.value = false;
  uni.stopPullDownRefresh();
};

// 分享到微信
onShareAppMessage(() => {
  return {
    title: article.value?.title,
    content: article.value.contents,
    path: `/pages/detail?id=${articleId.value}`,
    imageUrl: article.value?.authorAvatar
  };
});

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: article.value?.title,
    content: article.value.contents,
    path: `/pages/detail?id=${articleId.value}`,
    imageUrl: article.value?.authorAvatar
  };
});

// 微信分享卡片配置
const initWxShare = () => {
  try {
    // 获取当前页面URL,如果有#号需要去掉#及后面的内容
    const currentUrl = window.location.href;
    console.log("初始化微信分享，当前URL:", currentUrl);
    // 从后端获取签名
    getWxJsConfig(currentUrl)
      .then((res) => {
        console.log("微信配置信息:", res);

        wx.config({
          debug: false, // 开启调试模式，方便在手机上查看
          appId: res.appId, // 公众号的唯一标识
          timestamp: res.timestamp, // 生成签名的时间戳
          nonceStr: res.nonceStr, // 生成签名的随机串
          signature: res.signature, // 签名
          jsApiList: [
            "updateAppMessageShareData", // 分享给朋友
            "updateTimelineShareData", // 分享到朋友圈
            "onMenuShareTimeline", // 旧版分享到朋友圈
            "onMenuShareAppMessage" // 旧版分享给朋友
          ]
        });

        wx.ready(() => {
          console.log("wx.ready 已触发");

          // 分享配置
          const shareConfig: ShareConfig = {
            title: `来看看${article.value.authorName}作者对这场比赛的预测吧`, // 分享标题
            desc: article.value.title, // 分享描述
            link: currentUrl, // 分享链接
            imgUrl:
              article.value.authorAvatar || "https://sacdn.850g.com/football/static/avatar.svg", // 分享图标
            success: () => {
              console.log("分享成功");
            },
            cancel: () => {
              console.log("取消分享");
            }
          };

          console.log("分享配置:", shareConfig);

          // 分享给朋友
          wx.updateAppMessageShareData({
            title: shareConfig.title,
            desc: shareConfig.desc || "",
            link: shareConfig.link,
            imgUrl: shareConfig.imgUrl,
            success: shareConfig.success
          });

          // 分享到朋友圈
          wx.updateTimelineShareData({
            title: shareConfig.title,
            link: shareConfig.link,
            imgUrl: shareConfig.imgUrl,
            success: shareConfig.success
          });

          // 兼容旧版微信
          wx.onMenuShareTimeline({
            title: shareConfig.title,
            link: shareConfig.link,
            imgUrl: shareConfig.imgUrl,
            success: shareConfig.success,
            cancel: shareConfig.cancel
          });

          wx.onMenuShareAppMessage({
            title: shareConfig.title,
            desc: shareConfig.desc || "",
            link: shareConfig.link,
            imgUrl: shareConfig.imgUrl,
            type: "link", // 分享类型,music、video或link，不填默认为link
            dataUrl: "", // 如果type是music或video，则要提供数据链接，默认为空
            success: shareConfig.success,
            cancel: shareConfig.cancel
          });
        });

        wx.error((res) => {
          console.error("微信JS-SDK配置失败:", res);
        });
      })
      .catch((err) => {
        console.error("获取微信配置失败:", err);
      });
  } catch (error) {
    console.error("初始化微信分享失败:", error);
  }
};

// 在数据加载完成后初始化微信分享
watch(
  () => article.value,
  (newVal) => {
    if (newVal && newVal.id) {
      setTimeout(() => {
        initWxShare();
      }, 500);
    }
  },
  { deep: true }
);

const isRefresh = ref(false);
onPullDownRefresh(() => {
  isRefresh.value = true;
  getData();
});

onLoad(async (query) => {
  if (query.id) {
    if (!userStore.isLogined) {
      await $onLaunched;
    }

    await getArticleStatus(query.id);

    shareType.value = query.shareType || 0;

    const userInfoResult = await getUserInfo();
    userStore.setUserInfo(userInfoResult);
    articleId.value = query.id;
    getMatchType();
    // const {} = await getData()
    const [o] = await Promise.all([getOtherAuthorArticles(articleId.value), getData()]);
    otherArticleList.value = o;
    if (query.auto == 1 && userStore.isLogined && !article.value.isAttention) {
      followTheAuthor();
    }
    await checkPushShow();
    await checkAttentionShow();
    finishData.value = true;
    getGiftRetio();
    if (query.scan == 1) {
      if (nobackAuthor.value.includes(Number(article.value.authorId))) {
        scanStore.setScanAuthorId(article.value.authorId);
      } else {
        scanStore.setScanAuthorId(null);
      }
    }

    checkQQ();

    // 初始化微信分享
    if (typeof window !== "undefined") {
      setTimeout(() => {
        console.log("准备初始化微信分享");
        initWxShare();
      }, 1000);
    }
  }
});
</script>

<style lang="scss" scoped>
.scheme-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // height: 200rpx;
  padding: 30rpx 20rpx 30rpx 30rpx;
}

.revice {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  padding: 10rpx;
  text-align: center;
  border-radius: 50%;
}

.win_red {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #d1302e;
  border-radius: 8rpx;
}

.win_black {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8rpx;
}

.win_yan {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #ed8702;
  border-radius: 8rpx;
}

.win_blue {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #70b603;
  border-radius: 8rpx;
}

.win_now {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #70b603;
  text-align: center;
  background: rgba(112, 182, 3, 0.05);
  border: 1px solid rgba(112, 182, 3, 0.4);
  border-radius: 8rpx;
}

.no-buy {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 378rpx;
  padding-top: 90rpx;
  margin-top: 30rpx;
  background-image: url('https://sacdn.850g.com/football/static/no-buy_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.edit {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40%;
  height: 80rpx;
  font-size: 30rpx;
  color: white;
  background-color: rgb(209, 48, 46);
  border-radius: 20rpx;
}

.delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40%;
  height: 80rpx;
  font-size: 30rpx;
  color: rgb(119, 119, 119);
  background-color: rgb(228, 228, 228);
  border-radius: 20rpx;
}

.font {
  flex: none;
  flex-grow: 0;
  align-self: stretch;
  order: 0;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  font-style: normal;
  font-weight: 200;
  line-height: 30rpx;
  color: rgba(0, 0, 0, 0.8);
  text-align: center;
}

.modal {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
}
</style>
