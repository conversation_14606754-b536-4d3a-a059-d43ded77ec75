<route lang="json5">
{
  style: {
    navigationBarTitleText: '购买用户',
  },
}
</route>
<template>
  <view class="flex justify-between px-2 mt-5">
    <view>
      <view class="justify-center font">查看人数</view>
      <view class="justify-center font1">{{ articleData?.checkNum }}</view>
    </view>
    <view>
      <view class="justify-center font">已售</view>
      <view class="justify-center font1">{{ articleData?.buyNum }}</view>
    </view>
    <view>
      <view class="justify-center font">收益总额/元</view>
      <view class="justify-center font1">{{ articleData?.earnings }}</view>
    </view>
  </view>
  <view>
    <view class="flex justify-around">
      <button class="button" :class="{ active: active == 1 }" @click="changeActive(1)">
        已购买人数
      </button>
      <button class="button" :class="{ active: active == 0 }" @click="changeActive(0)">
        查看人数
      </button>
    </view>
  </view>
  <view class="p-20rpx">
    <wd-input
      custom-class="form-item"
      v-model="userName"
      placeholder="请输入昵称"
      prefix-icon="search"
      @confirm="changeSearch"
    />
  </view>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <view class="jump">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view class="jp-item flex" v-for="(item, index) in dataList" :key="index" v-else>
        <view class="px-2 mt-2">
          <view class="flex items-center">
            <img
              class="avatar mr-[30rpx]"
              :src="item.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg'"
            />
            <view>
              <view class="time w-[270rpx] overflow-ellipsis ellipsis whitespace-nowrap">
                {{ item.nickname }}
              </view>
              <view class="time">{{ dayjs(item.createTime).format('MM-DD HH:mm') }}</view>
            </view>
          </view>
        </view>
        <view class="button-group flex justify-end items-center flex-1">
          <text v-if="active === 1 && item.payType > 2" class="privilegest">
            {{
              item.payType === 3
                ? '包次解锁'
                : item.payType === 4
                  ? '包时解锁'
                  : item.payType === 5
                    ? '补单解锁'
                    : item.payType === 7
                      ? '套餐包解锁'
                      : ''
            }}
          </text>
          <text
            v-else-if="active === 1"
            :disabled="item.isRefund === 1"
            class="refund"
            :refund="item.isRefund === 1"
            @click="handlerRefund(item)"
          >
            {{ item.isRefund === 1 ? '已退款' : '一键退款' }}
          </text>
          <text class="extra" v-if="active === 1" @click="reqPushPrivilegeNum(item.userId)">
            设置补单
          </text>
          <text
            plain
            v-if="active === 0"
            size="large"
            :class="{ 'text-red-500': item.isBuy === 1 }"
          >
            {{
              item.isBuy === 1
                ? item.payType === 3
                  ? '包次解锁'
                  : item.payType === 4
                    ? '包时解锁'
                    : item.payType === 5
                      ? '补单解锁'
                      : item.payType === 7
                        ? '套餐包解锁'
                        : '已购买'
                : '未购买'
            }}
          </text>
        </view>
      </view>
      <view class="flex justify-center items-center">
        <wd-icon name="arrow-right" size="22px" color="#999"></wd-icon>
      </view>
    </view>
  </view>
  <wd-loadmore
    custom-class="loadmore"
    :state="loadmoreState"
    @reload="loadmore"
    v-if="dataList.length > pageParams.pageSize && dataList.length < total"
  />
  <wd-message-box selector="comfirm-box-slot" />
  <back />
</template>
<script lang="ts" setup>
import { getArticleUserInfos, getBuyArticleDetail, pushPrivilegeNum } from '@/service/userService'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'
import { useMessage } from 'wot-design-uni'
import { refundOrder } from '@/api/order'
const message = useMessage()
const comfirmMessage = useMessage('comfirm-box-slot')
const loadmoreState = ref('finished')
const userName = ref('')

const total = ref(0)

const dataList = ref([])

const active = ref()

const changeActive = (activeIndex: number) => {
  active.value = activeIndex
  dataList.value = []
  pageParams.value.pageNo = 1
  getData()
}

const articleData = ref({
  articleId: 0,
  checkNum: 0,
  buyNum: 0,
  earnings: 0.0,
  isRefund: 0,
})

const pageParams = ref({
  pageNo: 1,
  pageSize: 10,
})
const isRefresh = ref(false)

const reqPushPrivilegeNum = async (userId) => {
  message
    .prompt({
      title: '设置补单',
      inputPlaceholder: '请输入补单次数',
      inputType: 'number',
      inputError: '补单次数必须为1~1000',
      inputValidate: (value) => {
        return Number(value) > 0 && Number(value) <= 1000
      },
    })
    .then(async (e) => {
      await pushPrivilegeNum(userId, e.value)
      uni.showToast({
        title: '补单成功',
        icon: 'success',
        duration: 2000,
      })
    })
    .catch((error) => {
      console.log(error)
    })
}

const changeSearch = () => {
  isRefresh.value = true
  pageParams.value.pageNo = 1
  dataList.value = []
  getData()
}

onPullDownRefresh(() => {
  isRefresh.value = true
  pageParams.value.pageNo = 1
  dataList.value = []
  getData()
})

const getData = async () => {
  const data = {
    articleId: articleData.value.articleId,
    isBuy: active.value,
    userName: userName.value,
    ...pageParams.value,
  }
  const result = await getArticleUserInfos(data)

  loadmoreState.value = 'finished'
  dataList.value = dataList.value.concat(result.list)
  total.value = result.total

  isRefresh.value = false
  uni.stopPullDownRefresh()
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

const getInfo = async () => {
  const data = await getBuyArticleDetail(articleData.value.articleId)
  articleData.value.checkNum = data.checkNum
  articleData.value.buyNum = data.buyNum
  articleData.value.earnings = data.earnings
  articleData.value.isRefund = data.isRefund
}

const handlerRefund = (item) => {
  if (item.isRefund === 1 || item.payType > 2) {
    return
  }
  comfirmMessage
    .confirm({
      title: '是否确认退款',
    })
    .then(async () => {
      await refundOrder(item.orderId)

      uni.showToast({
        title: '退款成功',
        icon: 'success',
        duration: 2000,
      })

      item.isRefund = 1
    })
    .catch(() => {
      console.log('取消退款')
    })
}

const loadmore = () => {
  setTimeout(() => {
    pageParams.value.pageNo = pageParams.value.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}

onLoad(async (query) => {
  articleData.value.articleId = query.articleId
  active.value = Number(query.type)
  console.log('active.value', active.value)
  if (active.value === null || active.value === undefined) {
    active.value = 0
  }
  getInfo()
  getData()
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-icon-arrow-right) {
  display: none !important;
}

.form-item {
  height: 64rpx;
  padding-left: 10rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;

  &.wd-input.is-not-empty {
    &::after {
      display: none;
    }
  }
}

.jump {
  min-height: 80vh;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 5rpx;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid rgb(236, 234, 234);
    border-radius: 10rpx;

    .title {
      font-size: 28rpx;
      font-weight: 600;
      color: #555;
    }

    .amount {
      font-size: 28rpx;
      font-weight: 600;

      &-add {
        color: #f0883a;
      }

      &-sub {
        color: #34d19d;
      }
    }

    .time {
      font-size: 30rpx;
      line-height: 50rpx;
      color: #999;
      white-space: nowrap;
    }

    .balance {
      font-size: 24rpx;
      line-height: 50rpx;
      color: #999;
    }
  }
}

.font {
  /*nside auto layout */
  flex: none;
  flex-grow: 0;
  align-self: stretch;
  order: 0;
  /* 查看人数 */

  width: 120px;
  height: 42px;

  font-family: 'PingFang SC';
  font-size: 35rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 42px;

  color: rgba(0, 0, 0, 0.5);
  /* identical to box height */
  text-align: center;
}

.font1 {
  /*nside auto layout */
  flex: none;
  flex-grow: 0;
  align-self: stretch;
  order: 1;
  /* 10 */

  width: 120px;
  height: 56px;

  font-family: 'PingFang SC';
  font-size: 45rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 56px;

  color: rgba(0, 0, 0, 0.9);
  /* identical to box height */
  text-align: center;
}

.avatar {
  top: 0rpx;
  left: 20rpx;
  width: 80rpx;
  height: 80rpx;
  vertical-align: middle;
  border-radius: 50%;
}

.button {
  box-sizing: border-box;
  width: 300rpx;
  height: 80rpx;
  font-family: 'PingFang SC';
  font-size: 30rpx;
  font-style: normal;
  font-weight: 40;
  line-height: 80rpx;

  color: rgba(141, 141, 141);
  /* identical to box height */
  text-align: center;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;

  &.active {
    color: rgb(214, 71, 69);
    background-color: rgb(251, 235, 235);
    border-color: rgb(214, 71, 69);
  }
}

:deep(.wd-message-box__title) {
  font-size: 36rpx;
  font-weight: bold;
}

:deep(.wd-message-box__content) {
  color: rgb(219, 21, 45);
}

.button-group {
  .extra {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 40rpx;
    margin-left: 10rpx;
    font-family: 'PingFang SC';
    font-size: 24rpx;
    line-height: 20rpx;
    color: #fff;
    text-align: center;
    background: #d1302e;
    border: 1rpx solid #d1302e;
    border-radius: 12rpx;
  }

  .privilegest {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 40rpx;
    font-family: 'PingFang SC';
    font-size: 24rpx;
    line-height: 20rpx;
    color: #70b603;
    text-align: center;
    border: 1rpx solid #70b603;
    border-radius: 12rpx;
  }

  .refund {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 40rpx;
    font-family: 'PingFang SC';
    font-size: 24rpx;
    line-height: 20rpx;
    color: #d1302e;
    text-align: center;
    background: #fff;
    border: 1rpx solid #d1302e;
    border-radius: 12rpx;

    &::after {
      border-color: #ed8702;
    }

    &[refund='true'] {
      color: #ed8702;

      &::after {
        border-color: #ed8702;
      }
    }
  }
}
</style>
