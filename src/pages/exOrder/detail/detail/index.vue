<route lang="json5">
{
  style: {
    navigationBarTitleText: '补单详情',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view>
    <view v-if="!dataList.length">
      <wd-status-tip image="search" tip="当前搜索无结果" />
    </view>
    <view v-else v-for="(item, index) in dataList" :key="index" class="item">
      <view class="left">
        <view class="title">{{ item.title }}</view>
        <view class="time">{{ format(item.createTime, 'YYYY-MM-DD HH:mm:ss') }}</view>
      </view>
      <view class="right">{{ item.price }}鱼币</view>
    </view>
    <wd-loadmore
      custom-class="loadmore"
      :state="loadmoreState"
      @reload="loadmore"
      v-if="dataList.length < total"
    />
  </view>
  <back />
</template>
<script lang="ts" setup>
import { getExOrderAuthorDetail } from '@/api/author'
import { format } from '@/utils/format'
import back from '@/components/back/index.vue'
const loadmoreState = ref('finished')
const dataList = ref([])
const total = ref(0)
const params = ref({
  authorId: null,
  type: null,
  pageNo: 1,
  pageSize: 10,
})

const getDetail = async () => {
  const res = await getExOrderAuthorDetail(params.value)
  dataList.value = dataList.value.concat(res.list)
  total.value = res.total
}

const loadmore = () => {
  setTimeout(() => {
    params.value.pageNo = params.value.pageNo + 1
    loadmoreState.value = 'loading'
    getDetail()
  }, 200)
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onLoad(async (e) => {
  const authorId = e.authorId
  const type = e.type
  params.value.authorId = authorId
  params.value.type = type
  await getDetail()
})
</script>
<style lang="scss" scoped>
.item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 150rpx;
  border-bottom: 1px solid #f0f0f0;
  margin: 0 40rpx;

  .left {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .title {
      font-weight: bold;
      font-size: 32rpx;
    }
    .time {
      padding-top: 20rpx;
      color: rgb(144, 144, 144);
      font-size: 26rpx;
    }
  }

  .right {
    color: rgb(211, 61, 59);
    font-size: 32rpx;
  }
}
</style>
