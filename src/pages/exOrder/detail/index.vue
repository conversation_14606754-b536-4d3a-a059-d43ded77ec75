<route lang="json5">
{
  style: {
    navigationBarTitleText: '补单明细',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view>
    <view class="p-20rpx">
      <wd-input
        custom-class="search-input"
        v-model="params.nickname"
        placeholder="请输入作者名称"
        prefix-icon="search"
        @input="changeSearch"
      />
    </view>
    <view class="list-container">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view
        v-else
        class="item"
        v-for="(item, index) in dataList"
        :key="index"
        @click="goToDetail(item)"
      >
        <view class="top">
          <view class="left">
            <view class="avatar">
              <image
                :src="item.avatarUrl"
                style="width: 100rpx; height: 100rpx; border-radius: 50%"
              />
            </view>
            <view class="other">
              <view class="name">{{ item.nickname }}</view>
              <view class="time">{{ format(item.createTime, 'YYYY-MM-DD HH:mm:ss') }}</view>
            </view>
          </view>
          <view class="right">
            <view class="text-end">
              <text>补单详情</text>
              <wd-icon name="chevron-right" size="22px"></wd-icon>
            </view>
            <view class="m-t-20rpx color-[rgb(209,48,46)]">剩余:{{ item.num }}次</view>
          </view>
        </view>
      </view>
    </view>
    <wd-loadmore
      custom-class="loadmore"
      :state="loadmoreState"
      @reload="loadmore"
      v-if="dataList.length < total"
    />
  </view>
  <back />
</template>
<script lang="ts" setup>
import { getExOrderDetail } from '@/api/author'
import { format } from '@/utils/format'
import back from '@/components/back/index.vue'
const loadmoreState = ref('finished')

const params = ref({
  nickname: null,
  type: null,
  pageNo: 1,
  pageSize: 10,
})

const goToDetail = (item) => {
  uni.navigateTo({
    url: `/pages/exOrder/detail/detail/index?authorId=${item.authorId}`,
  })
}

const dataList = ref([])
const total = ref(0)

const changeSearch = ({ value }) => {
  console.log(value)
  params.value.username = value
  startSearch()
}

const startSearch = () => {
  params.value.pageNo = 1
  dataList.value = []
  getList()
}

const loadmore = () => {
  setTimeout(() => {
    params.value.pageNo = params.value.pageNo + 1
    loadmoreState.value = 'loading'
    getList()
  }, 200)
}

const getList = async () => {
  const res = await getExOrderDetail(params.value)
  dataList.value = dataList.value.concat(res.list)
  total.value = res.total
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.search-input {
  background-color: rgb(250, 250, 250);
  border: 1px solid #ccc;
  border-radius: 10rpx;
  :deep(.wd-input__prefix) {
    margin-left: 10rpx;
  }
}

:deep(.is-not-empty) {
  &::after {
    background-color: transparent !important;
  }
}

.radio-group-container {
  display: flex;
  justify-content: center;
  padding-top: 20rpx;
  box-shadow: 0px 3px 4px 0px rgb(233, 233, 233);
  .radio-group {
    display: flex;
    justify-content: space-around;
    width: 70%;

    .radio {
      color: rgb(175, 175, 175);
    }

    .active {
      color: rgb(212, 67, 65);

      &::after {
        display: inline-block;
        width: 100%;
        height: 2px;
        content: '';
        background-color: rgb(212, 67, 65);
      }
    }
  }
}

.item {
  padding: 20rpx 20rpx 20rpx 20rpx;
  border-bottom: 1px solid #ccc;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .avatar {
        margin-left: 20rpx;
      }
      .other {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        margin-left: 20rpx;
        .name {
          margin-bottom: 20rpx;
          font-weight: bold;
        }
        .time {
          font-size: 26rpx;
          color: rgb(184, 184, 184);
        }
      }
    }
    .right {
      color: rgb(184, 184, 184);
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20rpx;
    color: rgb(184, 184, 184);
  }
}
</style>
