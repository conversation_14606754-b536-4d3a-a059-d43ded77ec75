<route lang="json5">
{
  style: {
    navigationBarTitleText: '补单记录',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view>
    <view class="p-20rpx">
      <wd-input
        custom-class="search-input"
        v-model="params.nickname"
        placeholder="请输入用户名"
        prefix-icon="search"
        @input="changeSearch"
      />
    </view>
    <view class="list-container">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view v-else class="item" v-for="(item, index) in dataList" :key="index">
        <view class="top">
          <view class="left">
            <view class="avatar">
              <image
                :src="item.avatarUrl"
                style="height: 100rpx; width: 100rpx; border-radius: 50%"
              />
            </view>
            <view class="other">
              <view class="name">{{ item.nickname }}</view>
              <view class="time">
                {{ item.createTime }}
              </view>
            </view>
          </view>
          <view class="right">
            <view class="color-[rgb(209,48,46)]">补单{{ item.exNum }}次</view>
          </view>
        </view>
      </view>
    </view>
    <wd-loadmore
      custom-class="loadmore"
      :state="loadmoreState"
      @reload="loadmore"
      v-if="dataList.length < total"
    />
  </view>
  <Back />
</template>
<script lang="ts" setup>
import { getExOrderRecords } from '@/api/author'
import Back from '@/components/back/index.vue'
const loadmoreState = ref('finished')

const params = ref({
  nickname: null,
  type: null,
  pageNo: 1,
  pageSize: 10,
})

const dataList = ref([])
const total = ref(0)

const changeSearch = ({ value }) => {
  console.log(value)
  params.value.nickname = value
  startSearch()
}

const startSearch = () => {
  params.value.pageNo = 1
  dataList.value = []
  getList()
}

const loadmore = () => {
  setTimeout(() => {
    params.value.pageNo = params.value.pageNo + 1
    loadmoreState.value = 'loading'
    getList()
  }, 200)
}

const getList = async () => {
  const res = await getExOrderRecords(params.value)
  dataList.value = dataList.value.concat(res.list)
  total.value = res.total
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onMounted(() => {
  getList()
})
</script>
<style lang="scss" scoped>
.search-input {
  border-radius: 10rpx;
  border: 1px solid #ccc;
  background-color: rgb(250, 250, 250);
  :deep(.wd-input__prefix) {
    margin-left: 10rpx;
  }
}

:deep(.is-not-empty) {
  &::after {
    background-color: transparent !important;
  }
}

.radio-group-container {
  display: flex;
  justify-content: center;
  box-shadow: 0px 3px 4px 0px rgb(233, 233, 233);
  padding-top: 20rpx;
  .radio-group {
    display: flex;
    justify-content: space-around;
    width: 70%;

    .radio {
      color: rgb(175, 175, 175);
    }

    .active {
      color: rgb(212, 67, 65);

      &::after {
        content: '';
        display: inline-block;
        width: 100%;
        height: 2px;
        background-color: rgb(212, 67, 65);
      }
    }
  }
}

.item {
  padding: 20rpx 20rpx 20rpx 20rpx;
  border-bottom: 1px solid #ccc;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      align-items: center;
      .avatar {
        margin-left: 20rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .other {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 20rpx;
        .name {
          font-weight: bold;
          margin-bottom: 10rpx;
        }
        .time {
          color: rgb(170, 178, 200);
          font-size: 26rpx;
        }
      }
    }
    .right {
      color: rgb(184, 184, 184);
      margin-right: 40rpx;
    }
  }
  .bottom {
    display: flex;
    justify-content: space-around;
    align-items: center;
    color: rgb(184, 184, 184);
    padding: 20rpx;
  }
}
</style>
