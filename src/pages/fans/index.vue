<route lang="json5">
{
  style: {
    navigationBarTitleText: '粉丝列表',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <view class="py-2 flex justify-between">
      <wd-input
        class="px-2 py-2 flex-1 mr-[5rpx]"
        v-model="params.fansName"
        prefix-icon="search"
        clearable
        no-border
        @clear="search"
        @confirm="search"
        placeholder="输入粉丝名称查询"
      />
      <wd-picker
        :columns="columns"
        placeholder="请选择排序"
        value-key="sortType"
        label-key="name"
        v-model="params.sort"
        @confirm="sortChange"
        class="flex-1"
        clearable
        @clear="sortChange"
      />
    </view>
    <view class="attention">
      <text class="attentiontext">
        总数：{{ info.attentionCount }} &nbsp;&nbsp;&nbsp;&nbsp;今日新增：{{
          info.todayAttentionCount
        }}
      </text>
    </view>
    <wd-tabs v-model="params.type" @change="tabChange">
      <block v-for="tab in tabs" :key="tab.type">
        <wd-tab :title="tab.name"></wd-tab>
      </block>
    </wd-tabs>

    <view class="jump">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view
        style="border-bottom: 1rpx solid rgb(236, 234, 234)"
        v-for="(item, index) in dataList"
        :key="index"
        v-else
      >
        <view class="jp-item">
          <view class="flex justify-center items-center">
            <image
              class="avatar"
              :src="item.authorAvatar || 'https://sacdn.850g.com/football/static/avatar.svg'"
            />
            <view>
              <wd-row>
                <text class="nick">{{ item.authorName }}</text>
              </wd-row>
              <wd-row>
                <text class="time">
                  关注时间：{{ dayjs(item.attentionTime).format('YYYY-MM-DD') }}
                </text>
              </wd-row>
            </view>
          </view>
          <view flex justify-center @click="goInfo(item.userId)" class="flex flex-col">
            <text class="publish">
              消费明细
              <wd-icon name="arrow-right" size="14px" color="#999"></wd-icon>
            </text>
            <template v-if="item.userId !== item.authorId">
              <text
                v-if="item.blackStatus"
                @click.stop="toggleBlackListStatus(item, index)"
                class="w-fit py-5rpx px-15rpx text-26rpx border-normal border-#d1302e rounded-12rpx text-#d1302e"
              >
                取消拉黑
              </text>
              <text
                v-else
                @click.stop="toggleBlackListStatus(item, index)"
                class="w-fit py-5rpx px-15rpx text-26rpx border-normal border-#D1302E rounded-12rpx text-#d1302e"
              >
                拉黑
              </text>
            </template>
          </view>
        </view>
        <view class="flex justify-between px-2 bg-white" >
          <view>
            <view class="justify-center font">总消费/元</view>
            <view class="justify-center font1">{{ item.amount }}</view>
          </view>
          <view>
            <view class="justify-center font">消费次数</view>
            <view class="justify-center font1">{{ item.consumerNum }}</view>
          </view>
          <view>
            <view class="justify-center font">今日消费/元</view>
            <view class="justify-center font1">{{ item.todayAmount }}</view>
          </view>
          <view>
            <view class="justify-center font">上次消费时间</view>
            <view class="justify-center font1">{{ item.lastPayTime ? dayjs(item.lastPayTime).format('YYYY-MM-DD') : '-' }}</view>
          </view>
        </view>
      </view>
      <wd-loadmore
        custom-class="loadmore"
        :state="loadmoreState"
        @reload="loadmore"
        v-if="dataList.length < total"
      />
    </view>
  </view>
  <back />
</template>
<script lang="ts" setup>
import { getFansList, getFansCountInfo, toggleBlackList } from '@/service/userService'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'

const dataList = ref([])
const info = ref({
  attentionCount: 0,
  todayAttentionCount: 0,
})
const tabs = ref([
  {
    type: 0,
    name: '全部',
  },
  {
    type: 1,
    name: '新用户',
  },
  {
    type: 2,
    name: '老用户',
  },
])
const columns = ref([
  {
    sortType: 8,
    name: '近3日消费最多',
  },
  {
    sortType: 9,
    name: '近7日消费最多',
  },
  {
    sortType: 0,
    name: '最近关注时间',
  },
  {
    sortType: 1,
    name: '消费从高到低',
  },
  {
    sortType: 2,
    name: '消费从低到高',
  },
  {
    sortType: 3,
    name: '没有消费粉丝',
  },
  {
    sortType: 6,
    name: '3日未消费粉丝',
  },
  {
    sortType: 7,
    name: '7日未消费粉丝',
  },
  {
    sortType: 4,
    name: '消费次数从高到低',
  },
  {
    sortType: 5,
    name: '消费次数从低到高',
  },
])
const loadmoreState = ref('finished')
const total = ref(0)
const params = reactive({
  pageNo: 1,
  pageSzie: 10,
  fansName: '',
  type: 0,
  sort: null,
})

const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  getData()
})

const tabChange = (val) => {
  params.type = val.index
  params.pageNo = 1
  dataList.value = []
  getData()
}
const sortChange = () => {
  params.pageNo = 1
  dataList.value = []
  getData()
}

const search = () => {
  params.pageNo = 1
  dataList.value = []
  getData()
}

const getData = async () => {
  const data = await getFansList(params)
  total.value = data.total
  loadmoreState.value = 'finished'
  if (isRefresh.value) {
    dataList.value = data.list
    // 提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  } else {
    if (Array.isArray(data.list)) {
      dataList.value = [...dataList.value, ...data.list]
    }
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

async function toggleBlackListStatus(
  e: { blackStatus: number; userId: number; [key: string]: any },
  idx: number,
) {
  try {
    uni.showLoading()
    const { blackStatus, userId } = e
    const status = blackStatus ? 0 : 1
    await toggleBlackList(userId, status)
    dataList.value.splice(idx, 1, { ...e, blackStatus: status })
  } finally {
    uni.hideLoading()
  }
}

const getInfo = async () => {
  const data = await getFansCountInfo()
  info.value.attentionCount = data.attentionCount
  info.value.todayAttentionCount = data.todayAttentionCount
}

const goInfo = (id: number) => {
  uni.navigateTo({
    url: `/pages/fans/info/index?userId=${id}`,
  })
}

const loadmore = () => {
  setTimeout(() => {
    params.pageNo = params.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}
onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onMounted(() => {
  getInfo()
  getData()
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-calendar .wd-icon-arrow-right) {
  display: none !important;
}

.jump {
  min-height: 80vh;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 5rpx;
    //border-bottom: 1rpx solid rgb(236, 234, 234);

    &:active {
      background-color: rgb(246, 246, 246);
    }

    .avatar {
      width: 100rpx;
      height: 100rpx;
      margin: 20rpx;
      border-radius: 50%;
    }

    .nick {
      font-size: 30rpx;
      line-height: 50rpx;
    }

    .time {
      font-size: 26rpx;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.5);
    }

    .publish {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 26rpx;
      padding: 5rpx;
      margin: 10rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.5);
    }
  }
}

.attention {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  background: #d1302e;
  border-radius: 8rpx;
}

.attentiontext {
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 39rpx;
  color: #ffffff;
}

.font {
  flex: none;
  flex-grow: 0;
  align-self: stretch;
  order: 0;
  /* 查看人数 */

  width: 170rpx;
  height: 42rpx;

  font-family: PingFang SC;
  font-style: normal;
  font-weight: 300;
  font-size: 24rpx;
  line-height: 30rpx;

  color: rgba(0, 0, 0, 0.7);
  /* identical to box height */
  text-align: center;
}

.font1 {
  flex: none;
  flex-grow: 0;
  align-self: stretch;
  order: 1;
  /* 10 */

  width: 170rpx;
  height: 56rpx;

  font-family: 'PingFang SC';
  font-size: 26rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 56rpx;

  color: rgba(0, 0, 0, 0.9);
  /* identical to box height */
  text-align: center;
}
</style>
