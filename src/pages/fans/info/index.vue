<route lang="json5">
{
  style: {
    navigationBarTitleText: '消费明细',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <view class="py-2 calendar-container">
      <wd-calendar
        type="daterange"
        v-model="curDate"
        allow-same-day
        @confirm="changeDate"
        :required="false"
        placeholder="请选择日期"
      />
      <wd-icon
        v-if="curDate.length > 0"
        name="close"
        size="12"
        class="clear-button"
        @click="clearSelectedDate"
      ></wd-icon>
    </view>
    <view class="jump">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view class="jp-item" v-for="(item, index) in dataList" :key="index" v-else>
        <view class="px-2">
          <view>
            <text class="title">{{ item.articleTitle }}</text>
          </view>
          <view class="mt-2">
            <text class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</text>
          </view>
        </view>
        <view>
          <text class="amount">{{ item.amount }} 鱼币</text>
        </view>
      </view>
    </view>
    <wd-loadmore
      custom-class="loadmore"
      :state="loadmoreState"
      @reload="loadmore"
      v-if="dataList.length > params.pageSzie && dataList.length < total"
    />
  </view>
  <back />
</template>
<script lang="ts" setup>
import { getUserByArticlyList } from '@/service/userService'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'

const dataList = ref([])
const curDate = ref<number[]>([])
const loadmoreState = ref('finished')
const total = ref(0)
const params = reactive({
  pageNo: 1,
  pageSzie: 10,
  date: '',
  endDate: '',
  userId: '',
})
const clearSelectedDate = () => {
  dataList.value = []
  curDate.value = []
  getData()
}

const changeDate = () => {
  params.pageNo = 1
  dataList.value = []
  getData()
}

const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  params.pageNo = 1
  getData()
})

const getData = async () => {
  if (curDate.value.length > 0) {
    params.date = dayjs(curDate.value[0]).format('YYYY-MM-DD')
    params.endDate = dayjs(curDate.value[1]).format('YYYY-MM-DD')
  } else {
    params.date = ''
    params.endDate = ''
  }
  const data = await getUserByArticlyList(params)
  total.value = data.total
  loadmoreState.value = 'finished'
  if (isRefresh.value) {
    dataList.value = data.list
    // 提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  } else {
    if (Array.isArray(data.list)) {
      dataList.value = [...dataList.value, ...data.list]
    }
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

const loadmore = () => {
  setTimeout(() => {
    params.pageNo = params.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})
onLoad(async (query) => {
  params.userId = query.userId
  getData()
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-calendar .wd-icon-arrow-right) {
  display: none !important;
}

.win_red {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #d1302e;
  border-radius: 8rpx;
}

.win_black {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8rpx;
}

.win_blue {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #70b603;
  border-radius: 8rpx;
}

.win_now {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #70b603;
  text-align: center;
  background: rgba(112, 182, 3, 0.05);
  border: 1px solid rgba(112, 182, 3, 0.4);
  border-radius: 8px;
}

.calendar-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.clear-button {
  position: absolute;
  top: 50%;
  right: 10px;
  cursor: pointer;
  transform: translateY(-50%);
}
.jump {
  min-height: 80vh;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 5rpx;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid rgb(236, 234, 234);
    border-radius: 10rpx;

    .title {
      font-size: 32rpx;
      font-weight: 400;
      line-height: 50rpx;
    }

    .status-gray {
      padding: 3rpx 10rpx;
      margin-left: 30rpx;
      font-size: 22rpx;
      color: #999;
      border: 1rpx solid #999;
    }

    .avatar {
      width: 40rpx;
      height: 40rpx;
      vertical-align: middle;
      border-radius: 50%;
    }

    .time {
      font-size: 26rpx;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.5);
    }

    .amount {
      margin-left: 20rpx;
      font-size: 30rpx;
      font-weight: 400;
      line-height: 50rpx;
      color: #d1302e;
    }
  }
}
</style>
