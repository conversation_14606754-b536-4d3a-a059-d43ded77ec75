<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的足迹',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <view class="py-2">
      <wd-calendar
        use-default-slot
        v-model="curDate"
        @confirm="changeDate"
        placeholder="请选择日期"
      >
        <wd-input
          v-model="params.date"
          suffix-icon="fill-arrow-down"
          class="px-2 py-2 rounded-[20rpx]"
          readonly
          no-border
          placeholder="请选择日期"
        />
      </wd-calendar>
    </view>
    <view class="jump">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view
        class="jp-item"
        v-for="(item, index) in dataList"
        :key="index"
        @click="gotoDetail(item.id)"
        v-else
      >
        <view class="px-2">
          <view class="title">
            {{ item.title }}
          </view>
          <view class="mt-2">
            <wd-row>
              <img
                class="avatar"
                :src="item.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg'"
              />
              <text class="at-name">{{ item.nickname }}</text>
              <text class="at-name">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm') }}</text>
            </wd-row>
          </view>
        </view>
        <view class="flex justify-center items-center">
          <wd-icon name="arrow-right" size="22px" color="#999"></wd-icon>
        </view>
      </view>
      <wd-loadmore
        custom-class="loadmore"
        :state="loadmoreState"
        @reload="loadmore"
        v-if="dataList.length < total"
      />
    </view>
  </view>
  <back />
</template>
<script lang="ts" setup>
import { getFootprintList } from '@/service/userService'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'

const dataList = ref([])
const curDate = ref(null)
const loadmoreState = ref('finished')
const total = ref(0)
const params = reactive({
  pageNo: 1,
  pageSzie: 10,
  date: '',
})
const changeDate = () => {
  params.pageNo = 1
  dataList.value = []
  getData()
}

const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  params.pageNo = 1
  getData()
})
const getData = async () => {
  if (curDate.value) {
    params.date = dayjs(curDate.value).format('YYYY-MM-DD')
  } else {
    params.date = ''
  }
  const data = await getFootprintList(params)

  total.value = data.total
  loadmoreState.value = 'finished'
  if (isRefresh.value) {
    dataList.value = data.list
    //提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  } else {
    if (Array.isArray(data.list)) {
      dataList.value = [...dataList.value, ...data.list]
    }
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

const gotoDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/detail/index?id=${id}`,
  })
}

const loadmore = () => {
  setTimeout(() => {
    params.pageNo = params.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onMounted(() => {
  getData()
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-calendar .wd-icon-arrow-right) {
  display: none !important;
}

.jump {
  min-height: 80vh;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 5rpx;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid rgb(236, 234, 234);
    border-radius: 10rpx;

    .title {
      font-size: 32rpx;
      font-weight: 400;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.9);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 600rpx;
    }

    .avatar {
      width: 40rpx;
      height: 40rpx;
      vertical-align: middle;
      border-radius: 50%;
    }

    .at-name {
      margin-left: 20rpx;
      font-size: 26rpx;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.3);
    }
  }
}
</style>
