<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '套餐设置',
  },
}
</route>

<template>
  <view class="package-container bg-#F4F8FA p-30rpx">
    <form @submit="handleSubmit">
      <!-- 套餐名称 -->
      <view class="form-item">
        <text class="required-label">套餐名称</text>
        <wd-input v-model="formData.name" placeholder="请输入套餐名称" />
      </view>

      <!-- 套餐类型 -->
      <view class="form-item">
        <text class="required-label">套餐类型</text>
        <view v-if="isEdit" class="type-display">
          {{ formData.type === 0 ? '包时套餐' : formData.type === 1 ? '包次套餐' : '套餐包' }}
        </view>
        <view v-else class="package-type-selector">
          <view
            class="type-item"
            :class="{ active: formData.type === 0 }"
            @click="handleTypeChange(0)"
          >
            包时套餐
          </view>
          <view
            class="type-item"
            :class="{ active: formData.type === 1 }"
            @click="handleTypeChange(1)"
          >
            包次套餐
          </view>
          <view
            class="type-item"
            :class="{ active: formData.type === 4 }"
            @click="handleTypeChange(4)"
          >
            套餐包
          </view>
        </view>
      </view>

      <!-- 包时/包次套餐设置 -->
      <view class="form-item" v-if="formData.type === 0 || formData.type === 1">
        <text class="required-label">{{ formData.type === 0 ? '天数' : '次数' }}</text>
        <wd-input
          type="number"
          v-model="formData.days"
          :placeholder="`请输入${formData.type === 0 ? '天数' : '次数'}`"
        />
      </view>

      <view class="form-item" v-if="formData.type === 0 || formData.type === 1">
        <text class="required-label">价格(元)</text>
        <wd-input
          type="digit"
          v-model="formData.price"
          placeholder="请输入价格"
          @input="handleSinglePriceInput"
          @blur="formatSinglePrice"
        />
      </view>

      <!-- 比赛类型选择 -->
      <view class="form-item" v-if="formData.type === 0 || formData.type === 1">
        <text class="required-label">比赛类型</text>
        <view v-if="isEdit" class="type-display">
          {{ getMatchTypeName(formData.matchType) }}
        </view>
        <wd-picker
          v-else
          v-model="formData.matchType"
          :columns="[matchTypes]"
          label-key="name"
          value-key="id"
          placeholder="请选择比赛类型"
        ></wd-picker>
      </view>

      <!-- 套餐介绍 -->
      <view class="form-item">
        <text class="label">套餐介绍</text>
        <view class="textarea-container">
          <textarea
            class="custom-textarea"
            v-model="formData.content"
            placeholder="请输入套餐介绍"
            maxlength="300"
          ></textarea>
          <text class="word-count">{{ formData.content.length }}/300</text>
        </view>
      </view>

      <!-- 售价策略 - 仅在套餐包(type=3)时显示 -->
      <view class="form-item" v-if="formData.type === 4">
        <text class="required-label">售价策略</text>
        <text class="sub-label">设置1-3项售价策略</text>
        <view class="price-strategy-table">
          <view class="table-header">
            <text class="th">时长(天)</text>
            <text class="th">售价(元)</text>
          </view>
          <view class="table-row" v-for="(item, index) in formData.privilegePrices" :key="index">
            <view class="td">
              <wd-input
                type="number"
                v-model="item.day"
                placeholder="填写"
                @input="validateInput(item, 'day')"
              />
            </view>
            <view class="td">
              <wd-input
                type="digit"
                v-model="item.price"
                placeholder="填写"
                @input="(val) => handlePriceInput(val, index)"
                @blur="formatPrice(index)"
              />
            </view>
            <view class="delete-btn" @click="removeStrategy(index)">
              <wd-icon name="delete" size="40rpx" />
            </view>
          </view>
        </view>
        <view class="add-btn" @click="addStrategy" v-if="formData.privilegePrices.length < 3">
          添加
        </view>
      </view>

      <!-- 其他信息 - 仅在套餐包(type=3)时显示 -->
      <view class="form-item" v-if="formData.type === 4">
        <text class="label">其他信息</text>
        <view class="other-info">
          <view class="info-row">
            <text class="info-label">更新频率</text>
            <view class="update-frequency">
              <wd-input type="number" v-model="formData.date" class="frequency-input" />
              <text class="frequency-text">日，更新</text>
              <wd-input type="number" v-model="formData.num" class="frequency-input" />
              <text class="frequency-text">篇文章</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 单篇文章价格 仅在套餐包(type=3)时显示 -->
      <view class="form-item" v-if="formData.type === 4">
        <text class="required-label">单篇文章价格</text>
        <wd-input v-model="formData.articlePrice" type="digit" placeholder="请输入单篇文章价格" />
      </view>

      <view class="form-item">
        <text class="label">套餐生效中</text>
        <!-- <text class="text-[red]">(当前状态不允许编辑)</text> -->
        <piaoyi-editor class="w-650rpx" ref="editorRef" v-model="formData.showPicUrl" @changes="changeContent"
                       @ready="freeEditorReady" :photoUrl="photoUrl" :maxlength="10000" />
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container">
        <button class="submit-btn" form-type="submit">确定发布</button>
      </view>
    </form>
  </view>
</template>

<script lang="ts" setup>
import {
  IAddPackageParams,
  addAuthorPackage,
  getAuthorPrivilegeDetail,
  AppAuthorPrivilegeSetRespVo,
} from '@/api/package'
import { getMatchTypes } from '@/service/userService'

// 获取路由参数，判断是否为编辑模式
const id = ref<number | null>(null)
const isEdit = ref(false)
const pageTitle = ref('添加套餐')
const photoUrl = import.meta.env.VITE_UPLOAD_BASEURL
const freeEditorReady = ref(false)

// 存储原始套餐数据（用于编辑模式）
const originalData = ref<Partial<IAddPackageParams> | null>(null)

// 比赛类型数据
const matchTypes = ref([])

const editorRef = ref(null)

// 获取比赛类型名称
function getMatchTypeName(matchTypeId: number) {
  if (!matchTypes.value || matchTypes.value.length === 0) {
    return '未知类型'
  }

  const matchType = matchTypes.value.find((item) => item.id === matchTypeId)
  return matchType ? matchType.name : '未知类型'
}

// 获取比赛类型
async function fetchMatchTypes() {
  try {
    const data = await getMatchTypes()
    matchTypes.value = data
  } catch (error) {
    console.error('获取比赛类型失败:', error)
  }
}

// 表单数据
const formData = ref<IAddPackageParams>({
  type: 0, // 默认为套餐包，0:包时套餐 1:包次套餐 3:套餐包
  price: 0,
  days: 1,
  matchType: 0, // 默认比赛类型
  privilegePrices: [{ day: '', price: '' }],
  date: '',
  num: '',
  name: '',
  content: '',
  articlePrice: 0,
  showPicUrl:''
})

function changeContent(v: { html: string }) {
  formData.value.showPicUrl = v.html
  console.info('编辑器内容变化:', formData.value.showPicUrl)
}

// 处理单一价格输入
function handleSinglePriceInput(value: string) {
  // 移除非数字和小数点字符
  const cleanValue = value.replace(/[^\d.]/g, '')
  // 确保只有一个小数点
  const parts = cleanValue.split('.')
  let formattedValue = parts[0] || ''
  if (parts.length > 1) {
    // 限制小数点后最多两位
    formattedValue += '.' + parts[1].substring(0, 2)
  }
  // 更新值
  formData.value.price = formattedValue
}

// 格式化单一价格为两位小数
function formatSinglePrice() {
  if (formData.value.price) {
    const price =
      typeof formData.value.price === 'string'
        ? parseFloat(formData.value.price)
        : formData.value.price

    if (!isNaN(price)) {
      // 格式化为两位小数
      formData.value.price = price.toFixed(2)
    }
  }
}

// 获取套餐详情
async function getPackageDetail() {
  try {
    uni.showLoading({ title: '加载中...' })
    const detail = await getAuthorPrivilegeDetail(id.value as number)
    uni.hideLoading()

    if (detail) {
      // 保存原始数据，用于确保类型和比赛类型不被修改
      originalData.value = {
        type: detail.type,
        matchType: detail.matchType || 0,
      }

      // 填充表单数据
      formData.value = {
        id: detail.id?.toString(),
        type: detail.type,
        price: detail.price,
        days: detail.days,
        matchType: detail.matchType || 0,
        date: detail.date || '',
        num: detail.num || '',
        name: detail.name || '',
        content: detail.content || '',
        articlePrice: detail.articlePrice || 0,
        privilegePrices:
          detail.type === 4 && detail.privilegePrices
            ? detail.privilegePrices.map((item) => ({
                oldPrivilegeId: item.oldPrivilegeId,
                day: item.day,
                price: item.price,
              }))
            : [{ day: '', price: '' }],
        showPicUrl: detail.showPicUrl || '',
      }
      editorRef.value.setOtherContent(detail.showPicUrl || '')

      // 如果是包时或包次套餐，格式化价格
      if (detail.type === 0 || detail.type === 1) {
        formatSinglePrice()
      }
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '获取套餐详情失败',
      icon: 'none',
    })
    console.error('获取套餐详情失败:', error)
  }
}

// 添加售价策略
function addStrategy() {
  if (formData.value?.privilegePrices.length < 3) {
    formData.value.privilegePrices.push({ day: '', price: '' })
  }
}

// 移除售价策略
function removeStrategy(index: number) {
  if (formData.value?.privilegePrices.length > 1) {
    formData.value.privilegePrices.splice(index, 1)
  } else {
    uni.showToast({
      title: '至少需要一个售价策略',
      icon: 'none',
    })
  }
}

// 验证输入
function validateInput(
  item: { day: string | number; price: string | number },
  field: 'day' | 'price',
) {
  if (field === 'day') {
    // 确保天数为整数
    if (item.day && typeof item.day === 'string') {
      item.day = parseInt(item.day)
    }
  } else if (field === 'price') {
    // 确保价格为数字，限制为两位小数
    if (item.price && typeof item.price === 'string') {
      // 先将输入转为数字
      let price = parseFloat(item.price)
      if (!isNaN(price)) {
        // 限制为两位小数
        price = Math.round(price * 100) / 100
        // 更新值
        item.price = price
      }
    }
  }
}

// 处理价格输入
function handlePriceInput(value: string, index: number) {
  // 移除非数字和小数点字符
  const cleanValue = value.replace(/[^\d.]/g, '')
  // 确保只有一个小数点
  const parts = cleanValue.split('.')
  let formattedValue = parts[0] || ''
  if (parts.length > 1) {
    // 限制小数点后最多两位
    formattedValue += '.' + parts[1].substring(0, 2)
  }
  // 更新值
  formData.value.privilegePrices[index].price = formattedValue
}

// 格式化价格为两位小数
function formatPrice(index: number) {
  const item = formData.value.privilegePrices[index]
  if (item.price) {
    const price = typeof item.price === 'string' ? parseFloat(item.price) : item.price

    if (!isNaN(price)) {
      // 格式化为两位小数
      formData.value.privilegePrices[index].price = price.toFixed(2)
    }
  }
}

// 表单提交
async function handleSubmit() {
  // 表单验证
  if (!formData.value.name.trim()) {
    uni.showToast({
      title: '请输入套餐名称',
      icon: 'none',
    })
    return
  }

  // 验证套餐类型
  if (![0, 1, 3, 4].includes(formData.value.type)) {
    uni.showToast({
      title: '请选择正确的套餐类型',
      icon: 'none',
    })
    return
  }

  try {
    uni.showLoading({
      title: isEdit.value ? '更新中...' : '提交中...',
    })

    // 如果是编辑模式，确保使用原始类型和比赛类型
    if (isEdit.value && originalData.value) {
      formData.value.type = originalData.value.type
      if (formData.value.type === 0 || formData.value.type === 1) {
        formData.value.matchType = originalData.value.matchType
      }
    }

    // 根据套餐类型进行不同的验证和数据处理
    if (formData.value.type === 0 || formData.value.type === 1) {
      // 包时或包次套餐验证
      if (!formData.value.days) {
        uni.hideLoading()
        uni.showToast({
          title: formData.value.type === 0 ? '请输入天数' : '请输入次数',
          icon: 'none',
        })
        return
      }

      if (!formData.value.price) {
        uni.hideLoading()
        uni.showToast({
          title: '请输入价格',
          icon: 'none',
        })
        return
      }

      // 格式化价格
      formatSinglePrice()

      // 对于包时包次套餐，清空privilegePrices数据
      formData.value.privilegePrices = []
    } else {
      // 套餐包验证
      // 验证售价策略
      const invalidStrategy = formData.value.privilegePrices.some(
        (item) => !item.day || !item.price,
      )

      if (invalidStrategy) {
        uni.hideLoading()
        uni.showToast({
          title: '请完善售价策略',
          icon: 'none',
        })
        return
      }

      // 格式化所有价格为两位小数
      formData.value.privilegePrices.forEach((item, index) => {
        formatPrice(index)
      })
    }

    const result = await addAuthorPackage(formData.value)

    uni.hideLoading()

    if (result) {
      uni.showToast({
        title: isEdit.value ? '更新成功' : '添加成功',
        icon: 'success',
      })

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: isEdit.value ? '更新失败' : '添加失败',
      icon: 'none',
    })
    console.error(isEdit.value ? '更新套餐失败:' : '添加套餐失败:', error)
  }
}

// 页面加载时
onLoad((options) => {
  // 获取比赛类型数据
  fetchMatchTypes()

  if (options && options.id) {
    id.value = parseInt(options.id)
    isEdit.value = true
    pageTitle.value = '编辑套餐'
    // 设置页面标题
    uni.setNavigationBarTitle({
      title: pageTitle.value,
    })
    // 获取套餐详情
    getPackageDetail()
  }
})

// 监听套餐类型变化
function handleTypeChange(type: number) {
  // 如果是编辑模式，不允许修改类型
  if (isEdit.value) {
    uni.showToast({
      title: '编辑模式下不可修改套餐类型',
      icon: 'none',
    })
    return
  }

  // 保存需要保留的字段
  const { id, name, content } = formData.value

  // 清空相关数据
  formData.value = {
    id, // 保留id
    type: type,
    price: 0,
    days: 1,
    matchType: 0,
    privilegePrices: [{ day: '', price: '' }],
    date: '',
    num: '',
    name: name || '', // 保留name
    content: content || '', // 保留套餐介绍
  }
}
</script>

<style lang="scss" scoped>
.package-container {
  min-height: 100vh;
}

.form-item {
  padding: 30rpx;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;

  .type-display {
    padding: 20rpx 30rpx;
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
    background-color: #f8f8f8;
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    border-radius: 8rpx;
  }

  .package-type-selector {
    display: flex;
    margin-top: 20rpx;

    .type-item {
      flex: 1;
      padding: 20rpx 0;
      margin-right: 20rpx;
      font-size: 28rpx;
      text-align: center;
      border: 1rpx solid rgba(0, 0, 0, 0.1);
      border-radius: 8rpx;

      &:last-child {
        margin-right: 0;
      }

      &.active {
        color: #d1302e;
        background-color: rgba(209, 48, 46, 0.05);
        border-color: #d1302e;
      }
    }
  }

  .required-label {
    display: block;
    margin-bottom: 20rpx;
    font-size: 30rpx;
    font-weight: bold;

    &::before {
      color: #d1302e;
      content: '* ';
    }
  }

  .label {
    display: block;
    margin-bottom: 20rpx;
    font-size: 30rpx;
    font-weight: bold;
  }

  .sub-label {
    display: block;
    margin-bottom: 20rpx;
    font-size: 24rpx;
    color: rgba(0, 0, 0, 0.5);
  }

  .textarea-container {
    position: relative;
    margin-top: 20rpx;

    .custom-textarea {
      box-sizing: border-box;
      width: 100%;
      height: 200rpx;
      padding: 20rpx;
      font-size: 28rpx;
      border: 1rpx solid rgba(0, 0, 0, 0.1);
      border-radius: 8rpx;
    }

    .word-count {
      position: absolute;
      right: 20rpx;
      bottom: 20rpx;
      font-size: 24rpx;
      color: rgba(0, 0, 0, 0.5);
    }
  }

  .price-strategy-table {
    margin-top: 20rpx;
    overflow: hidden;
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    border-radius: 8rpx;

    .table-header {
      display: flex;
      font-weight: bold;
      background-color: #f5f5f5;

      .th {
        flex: 1;
        padding: 20rpx;
        font-size: 28rpx;
        text-align: center;
      }
    }

    .table-row {
      position: relative;
      display: flex;
      border-top: 1rpx solid rgba(0, 0, 0, 0.1);

      .td {
        flex: 1;
        padding: 10rpx;
      }

      .delete-btn {
        position: absolute;
        top: 50%;
        right: 10rpx;
        padding: 10rpx;
        color: #d1302e;
        transform: translateY(-50%);
      }
    }
  }

  .add-btn {
    padding: 20rpx 0;
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #d1302e;
    text-align: center;
    border: 1rpx dashed #d1302e;
    border-radius: 8rpx;
  }

  .other-info {
    margin-top: 20rpx;

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .info-label {
        width: 150rpx;
        font-size: 28rpx;
      }

      .update-frequency {
        display: flex;
        flex: 1;
        align-items: center;

        .frequency-input {
          width: 100rpx;
        }

        .frequency-text {
          margin: 0 20rpx;
          font-size: 28rpx;
        }
      }
    }
  }
}

.submit-container {
  margin: 60rpx 0;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    font-size: 32rpx;
    line-height: 88rpx;
    color: #fff;
    text-align: center;
    background-color: #d1302e;
    border-radius: 44rpx;
  }
}
</style>
