<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '套餐设置',
  },
}
</route>

<template>
  <view class="package-container bg-#F4F8FA p-30rpx">
    <form @submit="handleSubmit">
      <!-- 套餐名称 -->
      <view class="form-item">
        <text class="required-label">套餐名称</text>
        <wd-input v-model="formData.name" placeholder="请输入套餐名称" />
      </view>

      <!-- 套餐介绍 -->
      <view class="form-item">
        <text class="label">套餐介绍</text>
        <view class="textarea-container">
          <textarea
            class="custom-textarea"
            v-model="formData.content"
            placeholder="请输入套餐介绍"
            maxlength="300"
          ></textarea>
          <text class="word-count">{{ formData.content.length }}/300</text>
        </view>
      </view>

      <!-- 售价策略 -->
      <view class="form-item">
        <text class="required-label">售价策略</text>
        <text class="sub-label">设置1-3项售价策略</text>
        <view class="price-strategy-table">
          <view class="table-header">
            <text class="th">时长(天)</text>
            <text class="th">售价(元)</text>
          </view>
          <view class="table-row" v-for="(item, index) in formData.privilegePrices" :key="index">
            <view class="td">
              <wd-input
                type="number"
                v-model="item.day"
                placeholder="填写"
                @input="validateInput(item, 'day')"
              />
            </view>
            <view class="td">
              <wd-input
                type="digit"
                v-model="item.price"
                placeholder="填写"
                @input="validateInput(item, 'price')"
              />
            </view>
            <view class="delete-btn" @click="removeStrategy(index)">
              <wd-icon name="delete" size="40rpx" />
            </view>
          </view>
        </view>
        <view class="add-btn" @click="addStrategy" v-if="formData.privilegePrices.length < 3">
          添加
        </view>
      </view>

      <!-- 其他信息 -->
      <view class="form-item">
        <text class="label">其他信息</text>
        <view class="other-info">
          <view class="info-row">
            <text class="info-label">更新频率</text>
            <view class="update-frequency">
              <wd-input type="number" v-model="formData.date" class="frequency-input" />
              <text class="frequency-text">日，更新</text>
              <wd-input type="number" v-model="formData.num" class="frequency-input" />
              <text class="frequency-text">篇文章</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container">
        <button class="submit-btn" form-type="submit">确定发布</button>
      </view>
    </form>
  </view>
</template>

<script lang="ts" setup>
import { http } from '@/utils/http'

interface PrivilegePrice {
  oldPrivilegeId?: number
  day: string | number
  price: string | number
}

interface PackageFormData {
  type: number
  id?: string
  price: string | number
  days: string | number
  matchType: number
  privilegePrices: PrivilegePrice[]
  date: string | number
  num: string | number
  name: string
  content: string
  icon?: string
}

// 表单数据
const formData = ref<PackageFormData>({
  type: 0, // 默认包时套餐
  price: 0,
  days: 0,
  matchType: 1, // 默认比赛类型
  privilegePrices: [{ day: '', price: '' }],
  date: '',
  num: '',
  name: '',
  content: '',
})

// 添加售价策略
function addStrategy() {
  if (formData.value.privilegePrices.length < 3) {
    formData.value.privilegePrices.push({ day: '', price: '' })
  }
}

// 移除售价策略
function removeStrategy(index: number) {
  if (formData.value.privilegePrices.length > 1) {
    formData.value.privilegePrices.splice(index, 1)
  } else {
    uni.showToast({
      title: '至少需要一个售价策略',
      icon: 'none',
    })
  }
}

// 验证输入
function validateInput(item: PrivilegePrice, field: 'day' | 'price') {
  if (field === 'day') {
    // 确保天数为整数
    if (item.day && typeof item.day === 'string') {
      item.day = parseInt(item.day)
    }
  } else if (field === 'price') {
    // 确保价格为数字，可以有小数
    if (item.price && typeof item.price === 'string') {
      const price = parseFloat(item.price)
      if (!isNaN(price)) {
        item.price = price
      }
    }
  }
}

// 表单提交
async function handleSubmit() {
  // 表单验证
  if (!formData.value.name.trim()) {
    uni.showToast({
      title: '请输入套餐名称',
      icon: 'none',
    })
    return
  }

  // 验证售价策略
  const invalidStrategy = formData.value.privilegePrices.some((item) => !item.day || !item.price)

  if (invalidStrategy) {
    uni.showToast({
      title: '请完善售价策略',
      icon: 'none',
    })
    return
  }

  try {
    uni.showLoading({
      title: '提交中...',
    })

    const result = await http.post('/app-api/member/privilege/author/save', {
      ...formData.value,
    })

    uni.hideLoading()

    if (result) {
      uni.showToast({
        title: '添加成功',
        icon: 'success',
      })

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '添加失败',
      icon: 'none',
    })
    console.error('添加套餐失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.package-container {
  min-height: 100vh;
}

.form-item {
  padding: 30rpx;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.required-label::before {
  color: #d1302e;
  content: '* ';
}

.label,
.required-label {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.sub-label {
  display: block;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
}

.textarea-container {
  position: relative;
  margin-top: 20rpx;
}

.custom-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
}

.word-count {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.5);
}

.price-strategy-table {
  margin-top: 20rpx;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 8rpx;
}

.table-header {
  display: flex;
  font-weight: bold;
  background-color: #f5f5f5;
}

.th {
  flex: 1;
  padding: 20rpx;
  font-size: 28rpx;
  text-align: center;
}

.table-row {
  position: relative;
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.td {
  flex: 1;
  padding: 10rpx;
}

.delete-btn {
  position: absolute;
  top: 50%;
  right: 10rpx;
  padding: 10rpx;
  color: #d1302e;
  transform: translateY(-50%);
}

.add-btn {
  padding: 20rpx 0;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #d1302e;
  text-align: center;
  border: 1rpx dashed #d1302e;
  border-radius: 8rpx;
}

.other-info {
  margin-top: 20rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-label {
  width: 180rpx;
  font-size: 28rpx;
}

.update-frequency {
  display: flex;
  flex: 1;
  align-items: center;
}

.frequency-input {
  width: 100rpx;
}

.frequency-text {
  margin: 0 20rpx;
  font-size: 28rpx;
}

.submit-container {
  margin: 60rpx 0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  line-height: 88rpx;
  color: #fff;
  text-align: center;
  background-color: #d1302e;
  border-radius: 44rpx;
}
</style>
