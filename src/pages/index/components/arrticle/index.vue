<template>
  <view class="mt-[30rpx]" style="min-height: 50vh">
    <view class="flex justify-between items-center">
      <text class="text-28rpx text-black text-opacity-50">{{ `共${total}篇文章` }}</text>
      <div>
        <wd-button
          :custom-class="
            filterType === ARTICLE_FILTER_TYPE.LATEST
              ? 'filter-btn-article active'
              : 'filter-btn-article'
          "
          @click="changeFilterType(ARTICLE_FILTER_TYPE.LATEST)"
        >
          最新
        </wd-button>
        <!-- <wd-button :custom-class="{
          'filter-btn-article': true,
          active: filterType === ARTICLE_FILTER_TYPE.HISTORY,
        }" @click="changeFilterType(ARTICLE_FILTER_TYPE.HISTORY)"> -->
        <wd-button
          :custom-class="
            filterType === ARTICLE_FILTER_TYPE.HISTORY
              ? 'filter-btn-article active'
              : 'filter-btn-article'
          "
          @click="changeFilterType(ARTICLE_FILTER_TYPE.HISTORY)"
        >
          历史
        </wd-button>
      </div>
    </view>
    <!-- 最新 -->
    <template v-if="filterType === ARTICLE_FILTER_TYPE.LATEST">
      <view
        v-for="{ id, title, top, price, createTime, isNew, refundType, win, winName } in articles"
        :key="id"
        class="flex justify-between p-y-20rpx"
        style="border-bottom: 1px solid rgba(121, 121, 121, 0.2)"
        @click="gotoDetail(id)"
      >
        <!-- 左 -->
        <view class="flex flex-col justify-between">
          <text class="text-black text-opacity-30 leading-39rpx">{{ formater(createTime) }}</text>
          <text
            class="mt-5rpx mb-auto text-32rpx text-black text-opacity-90 leading-45rpx w-[480rpx] overflow-ellipsis ellipsis whitespace-nowrap"
          >
            {{ title }}
          </text>
          <view class="flex gap-x-15rpx">
            <text
              class="flex justify-center items-center w-50rpx leading-40rpx flex text-white text-24rpx bg-#D1302E rounded-md"
              v-if="isNew"
            >
              新
            </text>
            <image
              src="https://sacdn.850g.com/football/static/top.svg"
              class="w-80rpx h-40rpx"
              v-if="top"
            />
          </view>
        </view>
        <!-- 右 -->
        <view class="items-center">
          <view class="text-#D1302E text-34rpx leading-50rpx text-right" v-if="price">
            {{ `${price}鱼币` }}
          </view>
          <view class="text-#D1302E text-34rpx leading-50rpx text-right" v-else>{{ `免费` }}</view>
          <image
            v-if="win === 0 && refundType === 1"
            src="https://sacdn.850g.com/football/static/refund.png"
            class="w-[140rpx] h-[45rpx]"
          />
          <view :class="winClass(win)" v-if="win > 0">{{ winName }}</view>
        </view>
      </view>
    </template>
    <!-- 历史 -->
    <template v-else>
      <view
        v-for="{ id, title, win, createTime, conclusion, winName } in articles"
        :key="id"
        class="p-y-20rpx"
        style="border-bottom: 1px solid rgba(121, 121, 121, 0.2)"
        @click="gotoDetail(id)"
      >
        <text class="text-black text-opacity-30 leading-39rpx">{{ formater(createTime) }}</text>
        <view class="flex justify-between items-center">
          <text class="flex-1 truncate">{{ title }}</text>
          <view :class="winClass(win)" v-if="win > 0">{{ winName }}</view>
        </view>
        <view v-if="conclusion && conclusion != ''" class="text-24rpx">备注:{{ conclusion }}</view>
      </view>
    </template>
  </view>
</template>

<script lang="ts" setup>
import { IArticle } from '@/api/article'
import { ARTICLE_FILTER_TYPE, ARTICLE_SALE_TYPE } from '@/utils/enum'
import { formatDate } from '@/utils/format'
import { ConfigType } from 'dayjs'

const { filterType } = defineProps<{
  articles: IArticle[]
  saleType: ARTICLE_SALE_TYPE
  filterType: ARTICLE_FILTER_TYPE
  total: number
}>()
const emit = defineEmits<{ (e: 'changeType', value?: any): Promise<void> }>()

const winClass = computed(() => {
  return (win: number) => {
    switch (win) {
      case 1:
        return 'win_red'
      case 2:
      case 7:
        return 'win_black'
      case 4:
      case 5:
      case 6:
      case 8:
        return 'win_yan'
      case 3:
        return 'win_blue'
      default:
        return 'win_now'
    }
  }
})

const formater = computed(() => {
  return (date: ConfigType) => formatDate(date)
})

function changeFilterType(t: ARTICLE_FILTER_TYPE) {
  emit('changeType', t)
}

function gotoDetail(id: number) {
  uni.navigateTo({
    url: `/pages/detail/index?id=${id}`,
  })
}
</script>
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared', // 解除样式隔离
  },
}
</script>
<style scoped lang="scss">
.title {
  max-width: 450rpx;
  overflow: hidden;
  font-size: 32rpx;
  color: rgba(0, 0, 0, 0.9);
  text-overflow: ellipsis;
  white-space: nowrap;
}

.win_red {
  width: 130rpx;
  padding: 2rpx 0;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #d1302e;
  border-radius: 8rpx;
}

.win_black {
  width: 130rpx;
  padding: 2rpx 0;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8rpx;
}

.win_yan {
  width: 130rpx;
  padding: 2rpx 0;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #ed8702;
  border-radius: 8rpx;
}

.win_blue {
  width: 130rpx;
  padding: 2rpx 0;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #70b603;
  border-radius: 8rpx;
}

.win_now {
  width: 130rpx;
  padding: 2rpx 0;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #70b603;
  text-align: center;
  background: rgba(112, 182, 3, 0.05);
  border: 1px solid rgba(112, 182, 3, 0.4);
  border-radius: 8rpx;
}

:deep() {
  .filter-btn-article {
    width: 82rpx !important;
    min-width: unset !important;
    height: 40rpx !important;
    font-size: 26rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.5) !important;
    background-color: #fff !important;
    border-radius: 8rpx;

    &.active {
      color: #fff !important;
      background-color: #d1302e !important;
    }
  }
}
</style>
