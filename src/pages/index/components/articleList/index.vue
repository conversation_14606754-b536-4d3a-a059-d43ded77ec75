<template>
  <view>
    <!-- 导航栏 -->
    <view class="relative flex justify-center items-center h-88rpx">
      <view class="absolute left-30rpx">
        <wd-icon name="arrow-left" size="40rpx" @click="goBack" />
      </view>
      <text class="text-34rpx text-black text-opacity-90">
        {{ HOME_ARTICLE_CATEGORY_TXT[playType] }}
      </text>
    </view>
    <!-- 分段器 -->
    <view class="px-30rpx py-20rpx border-1rpx border-b-solid border-b-#797979 border-b-opacity-20">
      <wd-segmented :options="cateegoryOptions" v-model:value="current" @change="handleTabChange" />
    </view>
    <article-new :articles="currentArticles" />
  </view>
</template>

<script setup lang="ts">
import { IHomeArticleItem, getHomeArticleList } from '@/api/article'
import { DEFAULT_PAGE_SIZE, HOME_ARTICLE_CATEGORY_TXT } from '@/utils/constant'
import { HOME_ARTICLE_TYPE, HOME_ARTICLE_PLAY } from '@/utils/enum'
import { isEmpty } from 'lodash-es'
import articleNew from '../articleNew/index.vue'

const emit = defineEmits<{ (e: 'back'): void }>()
const props = defineProps<{ play: HOME_ARTICLE_PLAY }>()

const cateegoryOptions = ref([
  { value: '热卖排序', type: HOME_ARTICLE_TYPE.HOT },
  { value: '开赛时间', type: HOME_ARTICLE_TYPE.MATCH_TIME },
  { value: '最新发布', type: HOME_ARTICLE_TYPE.NEW },
])

const current = ref('热卖排序')
const currentType = ref(HOME_ARTICLE_TYPE.HOT)
const playType = ref(HOME_ARTICLE_PLAY.SINGLE_GAME_BET)

const articles = ref({
  hot: [],
  time: [],
  new: [],
})

const hotPageNo = ref(0)
const hotTotal = ref(0)
const hotTotalPage = computed(() => Math.ceil(hotTotal.value / DEFAULT_PAGE_SIZE))

const timePageNo = ref(0)
const timeTotal = ref(0)
const timeTotalPage = computed(() => Math.ceil(timeTotal.value / DEFAULT_PAGE_SIZE))

const newPageNo = ref(0)
const newTotal = ref(0)
const newTotalPage = computed(() => Math.ceil(newTotal.value / DEFAULT_PAGE_SIZE))

const currentArticles = computed(() => {
  const t = currentType.value
  if (t === HOME_ARTICLE_TYPE.HOT) return articles.value.hot
  if (t === HOME_ARTICLE_TYPE.MATCH_TIME) return articles.value.time
  return articles.value.new
})

const pageNo = computed(() => {
  const t = currentType.value
  if (t === HOME_ARTICLE_TYPE.HOT) return hotPageNo.value
  if (t === HOME_ARTICLE_TYPE.MATCH_TIME) return timePageNo.value
  return newPageNo.value
})

const totalPage = computed(() => {
  const t = currentType.value
  if (t === HOME_ARTICLE_TYPE.HOT) {
    return hotTotalPage.value
  } else if (t === HOME_ARTICLE_TYPE.MATCH_TIME) {
    return timeTotalPage.value
  }
  return newTotalPage.value
})

function goBack() {
  emit('back')
}

function handleData({ total, list }: { total: number; list: IHomeArticleItem[] }) {
  const t = currentType.value
  if (t === HOME_ARTICLE_TYPE.HOT) {
    articles.value.hot = isEmpty(articles.value.hot) ? list : [...articles.value.hot, ...list]
    hotTotal.value = total
    if (total) hotPageNo.value = hotPageNo.value + 1
  } else if (t === HOME_ARTICLE_TYPE.MATCH_TIME) {
    articles.value.time = isEmpty(articles.value.time) ? list : [...articles.value.time, ...list]
    timeTotal.value = total
    if (total) timePageNo.value = timePageNo.value + 1
  } else {
    articles.value.new = isEmpty(articles.value.new) ? list : [...articles.value.new, ...list]
    newTotal.value = total
    if (total) newPageNo.value = newPageNo.value + 1
  }
}

async function getData(loadMore = true) {
  const type = currentType.value
  uni.showLoading()
  let articleRes
  if (loadMore) {
    // 加载更多
    articleRes = await getHomeArticleList(type, pageNo.value + 1, DEFAULT_PAGE_SIZE, playType.value)
  } else {
    articleRes = await getHomeArticleList(type, 1, DEFAULT_PAGE_SIZE, playType.value)
  }

  handleData(articleRes)
  uni.hideLoading()
}

// 切换tab页面
function handleTabChange({ type }: { type: HOME_ARTICLE_TYPE }) {
  let fetchData = false
  currentType.value = type
  switch (type) {
    case HOME_ARTICLE_TYPE.HOT:
      if (hotPageNo.value === 0) fetchData = true
      break
    case HOME_ARTICLE_TYPE.MATCH_TIME:
      if (timePageNo.value === 0) fetchData = true
      break
    case HOME_ARTICLE_TYPE.NEW:
      if (newPageNo.value === 0) fetchData = true
      break
  }

  if (fetchData) getData(false)
}

onReachBottom(() => {
  if (pageNo.value >= totalPage.value) {
    uni.showToast({ title: '没有更多数据', icon: 'none' })
    return
  }
  getData()
})

onLoad(() => {
  playType.value = props.play
  getData(false)
})
</script>
