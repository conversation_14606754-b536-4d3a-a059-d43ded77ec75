<template>
  <template v-if="isEmpty(articles)">
    <view class="flex flex-col justify-center items-center h-200rpx my-20rpx">
      <image :src="emptyImg" class="w-244rpx h-133rpx" />
      <text class="mt-10rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
    </view>
  </template>
  <template v-else>
    <view @click="gotoArticleDetail(id)" v-for="{
      id,
      avatarUrl,
      authorName,
      winCount,
      recentWinCount,
      hitRate,
      title,
      createTime,
      schemePlay,
      matchScheme,
      buyCount,
      price,
    } in articles" :key="id" class="flex flex-col p-30rpx">
      <!-- 上 -->
      <view class="flex justify-between items-center">
        <image class="w-80rpx h-80rpx rounded-full" :src="avatarUrl" />
        <view class="flex ml-20rpx mr-auto">
          <text class="mr-18rpx text-28rpx text-black text-opacity-90">{{ authorName }}</text>
          <view v-if="winCount > 2 && hitRate >= 50"
            class="flex items-center h-40rpx mr-10rpx border-1rpx border-solid border-#D1302E rounded-16rpx">
            <text
              class="flex justify-center h-100% items-center px-11rpx text-28rpx text-white bg-#D1302E rounded-12rpx">
              {{ winCount }}
            </text>
            <text class="ml-5rpx mr-7rpx text-24rpx text-#D1302E">连中</text>
          </view>
          <text v-if="recentWinCount"
            class="flex justify-center items-center h-40rpx px-11rpx text-24rpx text-#D1302E border-1rpx border-solid border-#D1302E rounded-12rpx">
            {{ recentWinCount }}
          </text>
        </view>
        <!-- 命中率 -->
        <view class="flex flex-col justify-between items-center py-10rpx text-#D1302E">
          <text class="text-40rpx">{{ hitRate }}%</text>
          <text class="text-24rpx">准确率</text>
        </view>
      </view>
      <!-- 中(文章信息) -->
      <text class="py-20rpx text-30rpx text-black text-opacity-90 leading-42rpx">{{ title }}</text>
      <!-- 时间，球队信息 -->
      <view class="flex items-center text-24rpx text-black text-opacity-50 leading-34rpx">
        <text>{{ week(createTime) }}</text>
        <text class="ml-20rpx">{{ date(createTime) }}</text>
        <text class="ml-20rpx">{{ ms(matchScheme) }}</text>
      </view>
      <!-- 玩法，发布时间等文章信息 -->
      <view class="flex justify-between mt-20rpx text-24rpx leading-34rpx">
        <view class="text-black text-opacity-50">
          <text class="after:content-['|'] after:ml-10rpx" v-if="schemePlay">
            {{ schemeTypeTxt(schemePlay) }}
          </text>
          <text v-for="game in gameTypes(matchScheme)" :key="game" class="ml-10rpx">
            {{ game }}
          </text>
          <text class="ml-30rpx">{{ time(createTime) }}发布</text>
          <text class="ml-30rpx">{{ buyCount }}人购买</text>
        </view>
        <!-- 文章价格信息 -->
        <view class="flex items-center">
          <img src="https://sacdn.850g.com/football/static/gold.svg" alt="" class="w-40rpx h-40rpx" />
          <text class="text-30rpx text-#D1302E" v-if="price">{{ price }}</text>
          <text class="text-30rpx text-#D1302E" v-else>免费</text>
        </view>
      </view>
    </view>
  </template>
</template>

<script setup lang="ts">
import { IHomeArticleItem } from '@/api/article'
import { isEmpty } from 'lodash-es'
import emptyImg from '@/static/images/empty.png'
import { format, formatWeek } from '@/utils/format'
import { GAME_PLAY_TYPE_TXT, SCHEME_TYPE_TXT } from '@/utils/constant'
import { IMatchSchemeItem } from '@/api/match'
import { fromNow } from '@/utils/relative'
import { SCHEME_TYPE } from '@/utils/enum'

defineProps<{ articles: IHomeArticleItem[] }>()

const week = computed(() => {
  return (date: number) => (date ? formatWeek(date) : '')
})

const date = computed(() => {
  return (date: number) => (date ? format(date, 'MM-DD') : '')
})

const ms = computed(() => {
  return (ms: string) => {
    if (!ms) return ''
    const m: IMatchSchemeItem = JSON.parse(ms)[0]
    return `${m.homeName} VS ${m.awayName}`
  }
})

const gameTypes = computed(() => {
  return (ms: string) => {
    if (!ms) return ''
    const m: IMatchSchemeItem[] = JSON.parse(ms)
    // 只统计主玩法
    return m[0].matchPlays.reduce((p, { resultType, type }) => {
      return type === 0 ? [...p, GAME_PLAY_TYPE_TXT[resultType - 1]] : p
    }, [])
  }
})

const time = computed(() => {
  return (timestamp: number) => fromNow(timestamp)
})

const schemeTypeTxt = computed(() => {
  return (sp: number) => {
    if (sp === SCHEME_TYPE.TWO_SELECTIONS_PARLAY) return '足球二串一'
    if (sp === SCHEME_TYPE.MULTIPLE_SELECTIONS_PARLAY) return '足球多串一'
    return SCHEME_TYPE_TXT[sp]
  }
})

function gotoArticleDetail(id: number) {
  uni.navigateTo({ url: `/pages/detail/index?id=${id}` })
}
</script>
