<template>
  <scroll-view
    :show-scrollbar="false"
    class="article-scroll-view"
    scroll-y
    @scrolltolower="handleScrollToLower"
    @refresherrefresh="handleRefresh"
    refresher-enabled
    :refresher-triggered="refresherTriggered"
  >
    <view class="p-x-30rpx pb-20rpx">
      <wd-input
        :placeholder="`总共${totalCount}篇`"
        v-model="formData.title"
        @input="handleInput"
        custom-class="form-item"
        style="border: none"
      />

      <!-- <view class="flex justify-between mb-20rpx">
        <view class="flex-1 mr-10rpx">
          <wd-input
            prefixIcon="search"
            placeholder="搜索文章标题"
            v-model="formData.title"
            @input="handleInput"
            custom-class="form-item"
          />
        </view>
        <view class="flex-1">
          <wd-calendar use-default-slot type="date" v-model="formData.dateTime" @confirm="filteData">
            <wd-input
              prefixIcon="calendar"
              custom-class="form-item"
              placeholder="年/月/日"
              v-model="displayDate"
              readonly
            />
          </wd-calendar>
        </view>
      </view> -->
      <view
        class="mt-20rpx px-30rpx py-20rpx border-b-solid border-b-#f5f5f5 border-b-opacity-20 last:border-none bg-white relative"
        v-for="{
          id,
          matchIds,
          title,
          price,
          createTime,
          top,
          win,
          winName,
          winExc,
          conclusion,
          viewCount,
          buyCount,
          refundType,
          status,
          homeName,
          awayName,
          authorDivide,
        } in articles"
        :key="id"
        @click.stop="gotoDetail($event, id)"
        style="border-radius: 20rpx"
      >
        <img
          v-if="top"
          src="@/static/images/top-corner-marker.png"
          class="w-46rpx h-46rpx absolute top-0 right-0"
        />
        <!-- top -->
        <view class="flex items-center pb-10rpx border-b-solid border-b-1rpx border-b-#f5f5f5">
          <!-- <view class="flex items-center p-b-20rpx"> -->
          <!-- 置顶 -->
          <!-- <img
              v-if="!top"
              src="@/static/images/top.png"
              class="w-30rpx h-30rpx cursor-pointer"
              @click.stop="handleTopChange(id, top)"
            /> -->
          <!-- 不置顶 -->
          <!-- <img
              v-else
              src="@/static/images/no-top.png"
              class="w-30rpx h-30rpx cursor-pointer"
              @click.stop="handleTopChange(id, top)"
            /> -->
          <!-- 不中即退 -->

          <!-- </view> -->
          <view class="text-#797979 text-20rpx">
            {{ format(createTime) }}
          </view>
          <img
            src="https://sacdn.850g.com/football/static/refund.png"
            class="w-80rpx h-30rpx ml-10rpx"
            v-if="refundType === 1"
          />
        </view>

        <!-- bottom -->
        <view class="border-box-sizing p-20rxp">
          <view class="flex items-center justify-between p-10rpx">
            <!-- 标题 价格 -->
            <text>{{ title }}</text>
            <text class="text-#D1302E text-26rpx">￥ {{ price }}</text>
          </view>
          <view
            class="flex items-center justify-between bg-#f7f7f7 p-20rpx mb-20rpx"
            style="border-radius: 10rpx"
          >
            <view class="flex flex-col items-center">
              <text class="text-#797979 text-20rpx mb-10rpx">查看人数(人)</text>
              <text class="text-20rpx">{{ viewCount }}</text>
            </view>
            <view class="flex flex-col items-center">
              <text class="text-#797979 text-20rpx mb-10rpx">购买人数(人)</text>
              <text class="text-20rpx">{{ buyCount }}</text>
            </view>
            <view class="flex flex-col items-center">
              <text class="text-#797979 text-20rpx mb-10rpx">收款总额</text>
              <text class="text-#f23636 text-20rpx">{{ authorDivide }}</text>
            </view>
          </view>
          <view class="flex items-center justify-between">
            <text
              :class="[win === WIN_TYPE.NO_END ? 'text-#f23636' : 'text-#797979', 'text-20rpx']"
            >
              {{ win === WIN_TYPE.NO_END ? '正在销售中' : '已停止售卖' }}
            </text>
            <view class="flex items-center">
              <wd-button
                v-if="win === WIN_TYPE.NO_END"
                size="small"
                @click.stop="(e: MouseEvent) => showWinMessage(e, id)"
                style="margin-right: 10rpx"
              >
                设置红黑
              </wd-button>
              <wd-button
                class="set-btn"
                v-if="win === WIN_TYPE.NO_END"
                size="small"
                @click.stop="handlePush(id)"
              >
                推送
              </wd-button>
              <!-- <wd-button v-if="win !== WIN_TYPE.NO_END" size="small" @click.stop="updateWinExecute($event, id, 1)">
                下架
              </wd-button> -->
              <!-- <wd-button size="small" style="margin-left: 10rpx" @click.stop="changeStatus($event, id, status)">
                {{ status === 1 ? '下架' : '上架' }}
              </wd-button> -->
            </view>
          </view>
        </view>
      </view>

      <!-- 无数据提示 -->
      <view v-if="!loading && articles.length === 0" class="text-center py-100rpx text-gray-500">
        暂无数据
      </view>

      <!-- 加载中提示 -->
      <view v-if="loading && !refresherTriggered" class="text-center py-30rpx text-gray-500">
        <text>数据加载中...</text>
      </view>
    </view>

    <!-- 设置红黑弹框 -->
    <wd-message-box selector="win-setting-box">
      <wd-form :model="winFrom">
        <view class="mt-2"><text>请根据赛事真实结果和预测内容设置</text></view>
        <wd-radio-group
          v-model="winFrom.win"
          shape="dot"
          checked-color="rgb(209, 48, 46)"
          prop="win"
          class="mt-2"
          :rules="[{ required: true, message: '请选择红黑', trigger: 'blur' }]"
        >
          <wd-radio :value="1">红</wd-radio>
          <wd-radio :value="2">黑</wd-radio>
          <wd-radio :value="3">走水</wd-radio>
          <wd-radio :value="4">2中1</wd-radio>
          <wd-radio :value="5">3中2</wd-radio>
          <wd-radio :value="6">4中3</wd-radio>
          <wd-radio :value="7">被绝杀</wd-radio>
          <wd-radio :value="8">
            <wd-input
              placeholder="自定义设置结果"
              prop="winName"
              v-model="winFrom.winName"
              required="true"
              clearAble
            ></wd-input>
          </wd-radio>
        </wd-radio-group>
        <view class="mt-2">
          <wd-input
            v-model="winFrom.conclusion"
            :disabled="!winFrom.win"
            prop="conclusion"
            placeholder="请输入结语(选填)"
            custom-class="message-box-input"
          />
        </view>
      </wd-form>
    </wd-message-box>
    <!-- 二次确认 -->
    <wd-message-box selector="confirm-setting-box" />

    <!-- 推送弹窗 -->
    <wd-popup v-model="pushVisible" position="bottom" safe-area-inset-bottom>
      <view class="pt-50rpx pb-150rpx p-x-30rpx">
        <scroll-view :show-scrollbar="false" style="max-height: 1000rpx" scroll-y>
          <view class="mb-10rpx text-32rpx text-black text-opacity-90 text-center">
            请选择推送区间
          </view>
          <wd-form ref="pushFormRef" :model="pushData">
            <view
              v-for="(item, index) in pushData"
              :key="index"
              style="margin: 20rpx 0; border-bottom: 1px solid #eee"
            >
              <view v-if="pickTemplate">
                <view class="flex justify-start items-center">
                  <view class="form-label required">选择模版</view>
                  <text
                    v-if="index == pushData.length - 1"
                    class="color-[rgb(255,0,0)] font-size-24rpx m-l-20rpx push-btn"
                    @click="addPushTemplate(index)"
                  >
                    添加
                  </text>
                  <text
                    v-if="index == pushData.length - 1 && index != 0"
                    class="color-[rgb(255,0,0)] font-size-24rpx m-l-20rpx push-btn"
                    @click="removePushTemplate(index)"
                  >
                    删除
                  </text>
                </view>
                <wd-select-picker
                  :columns="pushTemplateList"
                  value-key="id"
                  label-key="name"
                  v-model="item.templateId"
                  type="radio"
                />
              </view>
              <view class="form-label required">推送条件</view>
              <wd-radio-group
                :prop="`consumeStatus`"
                shape="dot"
                v-model="item.consumeStatus"
                inline
              >
                <wd-radio :value="0">全部</wd-radio>
                <wd-radio :value="1">消费条件</wd-radio>
                <wd-radio :value="2">已购本方案</wd-radio>
              </wd-radio-group>
              <template v-if="item.consumeStatus === 1">
                <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                  最低消费次数（选填）:
                </view>
                <view class="flex items-center">
                  <view class="flex-1">
                    <wd-input
                      prop="consumeMinNum"
                      class="form-input"
                      placeholder="请输入起始次数"
                      v-model="item.consumeMinNum"
                    />
                  </view>
                </view>
                <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                  最低消费金额（选填）:
                </view>
                <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                  消费金额（选填）:
                </view>
                <view class="flex items-center">
                  <view class="flex-1">
                    <wd-input
                      prop="consumeMinAmount"
                      class="form-input"
                      placeholder="请输入金额"
                      v-model="item.consumeMinAmount"
                    />
                  </view>
                </view>
              </template>
              <view class="mt-30rpx text-30rpx text-black text-opacity-90">
                推送时间（当天只可选择3个时间）
              </view>
              <view v-for="(_, i) in item.pushTimeList" :key="i" class="flex items-center">
                <wd-datetime-picker
                  v-model="item.pushTimeList[i]"
                  :minDate="new Date()"
                  custom-class="flex-1"
                />
                <wd-button type="icon" icon="add" class="add-btn" @click="addPushTime(index)" />
                <wd-button
                  type="icon"
                  icon="remove"
                  class="remove-btn"
                  @click="removePushTime(index, i)"
                />
              </view>
            </view>
            <view class="submit">
              <wd-button type="primary" custom-class="submit-btn" @click="handleSubmitPush">
                推送
              </wd-button>
            </view>
          </wd-form>
        </scroll-view>
      </view>
    </wd-popup>
  </scroll-view>
</template>

<script lang="ts" setup>
import {
  IAuthorArticle,
  IAuthorArticleParams,
  getAuthorArticles,
  IArticlePushConfig,
  getArticlePushConfig,
  updateArticlePushConfig,
  changeArticleStatus,
} from '@/api/article'
import { updateWin, winExecute } from '@/service/userService'
import { WIN_TYPE, JUDGE_TYPE } from '@/utils/enum'
import { formatDataTime, format as formatDate } from '@/utils/format'
import { useMessage } from 'wot-design-uni'
import { ceil, debounce, isEmpty } from 'lodash-es'
import { changeTopStatus } from '@/api/article'
import { getPushTemplate } from '@/api/mpAccount'
import { useUserStore } from '@/store'
import dayjs from 'dayjs'

const message = useMessage('win-setting-box')
const confirmBox = useMessage('confirm-setting-box')
const userStore = useUserStore()

const format = computed(() => {
  return (dt: number) => formatDataTime(dt)
})

const winColumns = [
  {
    label: '红',
    value: 1,
  },
  {
    label: '黑',
    value: 2,
  },
  {
    label: '走水',
    value: 3,
  },
  {
    label: '2中1',
    value: 4,
  },
  {
    label: '3中2',
    value: 5,
  },
  {
    label: '4中3',
    value: 6,
  },
  {
    label: '被绝杀',
    value: 7,
  },
  {
    label: '',
    value: 8,
  },
]
const getWinColumnsLabel = (value) => {
  return winColumns.find((item) => item.value === value).label
}

const props = defineProps<{ judgeType?: number }>()
const emit = defineEmits<{ (e: 'flash'): void }>()

// 分页相关数据
const articles = ref<IAuthorArticle[]>([])
const loading = ref(false)
const current = ref(1)
const totalPage = ref(0)
const totalCount = ref(0)
const hasMore = computed(() => current.value < totalPage.value)
const refresherTriggered = ref(false)

// 搜索表单
const formData = reactive({
  title: '',
  dateTime: null,
})

// 日期格式化显示
const displayDate = computed({
  get() {
    if (formData.dateTime) return formatDate(formData.dateTime, 'YYYY/MM/DD')
    return null
  },
  set() {},
})

// 获取文章数据
function filteData(search = true) {
  loading.value = true
  current.value = search ? 1 : current.value + 1

  const { title, dateTime } = formData
  let filteParams: Partial<IAuthorArticleParams> = {
    pageNo: current.value,
    pageSize: 10,
    type: (props.judgeType || JUDGE_TYPE.UNJUDGED) as 0 | 1,
  }

  filteParams = title ? { title, ...filteParams } : filteParams
  filteParams = dateTime
    ? { dateTime: formatDate(dateTime, 'YYYY-MM-DD'), ...filteParams }
    : filteParams

  return getAuthorArticles(filteParams)
    .then((a) => {
      const { list, total } = a
      totalCount.value = total
      articles.value = search ? list : [...articles.value, ...list]
      totalPage.value = ceil(total / 10)
      loading.value = false
      return { list, total }
    })
    .catch(() => {
      loading.value = false
    })
    .finally(() => {
      refresherTriggered.value = false
    })
}

// 处理滚动到底部
function handleScrollToLower() {
  if (!loading.value && hasMore.value) {
    filteData(false)
  }
}

// 处理下拉刷新
function handleRefresh() {
  refresherTriggered.value = true
  filteData(true)
}

// 处理输入搜索
const handleInput = debounce(() => filteData(true), 300)

function gotoDetail(e: MouseEvent, id: number) {
  uni.navigateTo({
    url: `/pages/article/setting/index?id=${id}`,
  })
}

// 新增推送相关变量和函数
const pushVisible = ref(false)
const currentArticleId = ref<number | null>(null)
const pushFormRef = ref(null)
const pushData = ref<IArticlePushConfig[]>([
  { pushTimeList: [], templateId: null },
] as IArticlePushConfig[])
const pushTemplateList = ref([])

const currentAccountId = computed(() => userStore.userInfo.captivePushAccount)

const pickTemplate = computed(() => {
  if (currentAccountId.value && pushTemplateList.value.length > 0) {
    return true
  }
  return false
})

// 处理推送文章
function handlePush(id: number) {
  currentArticleId.value = id

  // 获取推送配置
  getArticlePushConfig(id).then((pushConfig) => {
    if (pushConfig && pushConfig.length > 0) {
      for (const singleConfig in pushConfig) {
        const { pushTimeList, ...restPushConfig } = pushConfig[singleConfig]
        const p = isEmpty(pushTimeList)
          ? [addDefaultTime()]
          : pushTimeList.map((t) => new Date(formatDate(t, 'YYYY-MM-DD HH:mm')))
        pushConfig[singleConfig] = {
          pushTimeList: p,
          ...restPushConfig,
        }
      }
      pushData.value = pushConfig
    } else {
      // 设置默认值
      pushData.value = [
        {
          articleId: id,
          consumeStatus: 0,
          consumeMinNum: 0,
          consumeMinAmount: 0,
          consumeMaxNum: null,
          consumeMaxAmount: null,
          templateId: null,
          pushTimeList: [addDefaultTime()],
        },
      ]
    }

    // 如果有推送账号，获取模板列表
    if (currentAccountId.value) {
      getPushTemplate(currentAccountId.value)
        .then((templates) => {
          pushTemplateList.value = templates
          pushVisible.value = true
        })
        .catch(() => {
          pushVisible.value = true
        })
    } else {
      pushVisible.value = true
    }
  })
}

function addDefaultTime() {
  const now = new Date()
  return new Date(formatDate(dayjs(now).add(5, 'minute'), 'YYYY-MM-DD HH:mm'))
}

function addPushTime(index) {
  if (pushData.value[index].pushTimeList.length < 3) {
    pushData.value[index].pushTimeList.push(addDefaultTime())
  }
}

function removePushTime(index: number, i: number) {
  if (pushData.value[index].pushTimeList.length > 1) {
    pushData.value[index].pushTimeList.splice(i, 1)
  }
}

const addPushTemplate = (index) => {
  pushData.value.push({
    articleId: currentArticleId.value,
    pushTimeList: [addDefaultTime()],
    consumeMaxAmount: null,
    consumeMaxNum: null,
    consumeMinAmount: 0,
    consumeMinNum: 0,
    consumeStatus: 0,
    templateId: null,
  })
}

const removePushTemplate = (index) => {
  pushData.value.splice(index, 1)
}

function handleSubmitPush() {
  if (!pushFormRef.value) return
  pushFormRef.value.validate().then(({ valid }) => {
    if (!valid) return
    const id = currentArticleId.value

    // 处理模板ID
    if (pickTemplate.value) {
      for (let i = 0; i < pushData.value.length; i++) {
        const { templateId } = pushData.value[i]
        if (!templateId) {
          uni.showToast({ title: '请选择推送模版', icon: 'none' })
          return
        }
      }
    } else {
      for (let i = 0; i < pushData.value.length; i++) {
        if (pushData.value[i].templateId) {
          pushData.value[i].templateId = null
        }
      }
    }

    // 准备请求数据
    const req = []
    for (let i = 0; i < pushData.value.length; i++) {
      const { pushTimeList, ...rest } = pushData.value[i]
      const p = {
        articleId: id,
        ...rest,
        pushTimeList: pushTimeList.map(
          (t) => new Date(dayjs(t as string).format('YYYY-MM-DD HH:mm')),
        ),
      }
      req.push(p)
    }

    // 提交推送设置
    updateArticlePushConfig(req).then((s) => {
      if (s) {
        uni.showToast({ title: '提交成功', icon: 'none' })
        pushVisible.value = false

        // 通知父组件刷新
        emit('flash')
      }
    })
  })
}

// 处理置顶/取消置顶
async function handleTopChange(id: number, topStatus: number) {
  const data = {
    id: id,
    topStatus: topStatus === 1 ? 0 : 1,
  }

  try {
    await changeTopStatus(data)

    uni.showToast({
      title: data.topStatus === 1 ? '置顶成功' : '取消置顶成功',
      icon: 'none',
    })

    // 更新本地状态，避免重新请求数据
    const index = articles.value.findIndex((item) => item.id === id)
    if (index !== -1) {
      articles.value[index].top = data.topStatus
    }

    // 通知父组件刷新
    emit('flash')
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'none',
    })
    console.error('置顶操作失败:', error)
  }
}

// 添加上下架功能
function changeStatus(e: MouseEvent, id: number, currentStatus: number) {
  e.stopPropagation()
  const newStatus = currentStatus === 0 ? 1 : 0
  const statusText = currentStatus === 1 ? '下架' : '上架'

  if (currentStatus !== 2) {
    const data = {
      id: id,
      status: newStatus,
    }

    const confirm = () => {
      changeArticleStatus(data)
        .then(() => {
          uni.showToast({
            title: statusText + '成功',
            icon: 'none',
          })

          // 更新本地状态，避免重新请求数据
          const index = articles.value.findIndex((item) => item.id === id)
          if (index !== -1) {
            articles.value[index].status = newStatus
          }

          // 通知父组件刷新
          emit('flash')
        })
        .catch(() => {
          uni.showToast({
            title: statusText + '失败',
            icon: 'none',
          })
        })
    }

    if (newStatus === 0) {
      confirmBox.confirm({
        title: '下架',
        msg: '确认要下架该方案吗？',
        beforeConfirm: () => {
          confirm()
          confirmBox.close()
        },
      })
    } else {
      confirm()
    }
  } else {
    uni.showToast({
      title: '该方案已封禁,无法进行上下架操作',
      icon: 'none',
    })
  }
}

interface IWinForm {
  id: number
  win: WIN_TYPE | null
  conclusion: null | string
  winName: null | string
}
const winFrom = ref<IWinForm>({} as IWinForm)

function showWinMessage(e: MouseEvent, id: number) {
  e.stopPropagation()
  winFrom.value = {
    id,
    win: null,
    conclusion: null,
    winName: null,
  }
  message.confirm({
    title: '设置红黑',
    beforeConfirm: ({ resolve }) => {
      if (!winFrom.value.win) {
        uni.showToast({
          title: '请选择赛事结果',
          icon: 'none',
        })
        resolve(false)
        return
      } else {
        if (winFrom.value.win < 8) {
          // winFrom.value.winName = getWinColumnsLabel(winFrom.value.win)
          // winFrom.value.winName = getWinColumnsLabel(winFrom.value.win)
        }
        if (winFrom.value.win === 8 && !winFrom.value.winName) {
          uni.showToast({
            title: '请输入自定义名称',
            icon: 'none',
          })
          resolve(false)
          return
        }
        confirmBox.confirm({
          title: `是否将此方案设置为【${winFrom.value.win < 8 ? getWinColumnsLabel(winFrom.value.win) : winFrom.value.winName}】`,
          beforeConfirm: () => {
            winFrom.value.winName =
              winFrom.value.win < 8 ? getWinColumnsLabel(winFrom.value.win) : winFrom.value.winName
            updateWin(winFrom.value).then(() => {
              confirmBox.close()
              message.close()
              emit('flash')
            })
          },
        })
      }
    },
  })
  return false
}

function updateWinExecute(e: MouseEvent, id: number, winExe: number) {
  e.stopPropagation()
  confirmBox.confirm({
    title: '确定' + (winExe == 1 ? '【收款】?' : '【退款】?'),
    msg: winExe == 1 ? '订单金额将结算到账户余额' : '订单金额将原路退回',
    beforeConfirm: () => {
      winExecute({ id: id, winExc: winExe }).then(() => {
        confirmBox.close()
        emit('flash')
      })
    },
  })
}

// 兼容暴露方法给父组件（为了向后兼容）
defineExpose({
  refresh: () => {
    refresherTriggered.value = true
    return filteData(true)
  },
  filteData,
})

// 初始加载数据
onShow(() => {
  filteData()
})
</script>
<style lang="scss" scoped>
:deep(.wd-message-box__content) {
  max-height: 1000rpx !important;
}

.article-scroll-view {
  width: 100%;
  height: calc(100vh - 530rpx);
}

.form-item {
  height: 64rpx;
  padding-left: 10rpx;
  margin-top: 10rpx;
  border: none;
  border-radius: 12rpx;

  &.wd-input.is-not-empty {
    &::after {
      display: none;
    }
  }
}

// 新增样式
.form-label {
  margin-bottom: 10rpx;
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.9);

  &.required {
    &::after {
      display: inline-block;
      color: #d1302e;
      content: '*';
    }
  }
}

:deep() {
  .submit {
    display: flex;
    justify-content: center;

    .submit-btn {
      width: 305rpx;
      height: 80rpx;
      margin: 30px auto 0;
      border-radius: 12rpx;
    }
  }

  .add-btn,
  .remove-btn {
    width: 40rpx !important;
    height: 40rpx !important;
    border: 1px solid rgba($color: #000000, $alpha: 0.3);
  }

  .add-btn {
    margin: 0 20rpx;
  }
}

.push-btn {
  padding: 6rpx 25rpx;
  border: 1px solid red;
  border-radius: 12rpx;
}

.set-btn {
  color: #d1302e !important;
  background-color: #ffecee !important;
  border: 1px solid #d1302e;
}
</style>
