<template>
  <view>
    <!-- 导航栏 -->
    <view class="relative flex justify-center items-center h-88rpx">
      <view class="absolute left-30rpx">
        <wd-icon name="arrow-left" size="40rpx" @click="goBack" />
      </view>
      <text class="text-34rpx text-black text-opacity-90">作者列表</text>
    </view>
    <!-- 分段器 -->
    <view class="mt-20rpx mb-30rpx px-30rpx">
      <wd-segmented :options="authorOptions" v-model:value="current" @change="handleChange" />
    </view>
    <template v-if="isEmpty(currentAuthors)">
      <view class="flex flex-col justify-center items-center h-200rpx mt-1/2">
        <image :src="emptyImg" class="w-244rpx h-133rpx" />
        <text class="mt-10rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
      </view>
    </template>
    <template v-else>
      <view
        @click="gotoAuthorInfo(authorId)"
        v-for="{ authorId, authorName, avatarUrl, recentWinCount, hitRate } in currentAuthors"
        :key="authorId"
        class="flex justify-between items-center p-30rpx border-b-1rpx border-b-solid border-b-#797979 border-opacity-20"
      >
        <image :src="avatarUrl" class="w-80rpx h-80rpx rounded-full" />
        <view class="flex gap-x-20rpx ml-20rpx mr-auto">
          <text class="text-28rpx text-black text-opacity-90">{{ authorName }}</text>
          <text
            v-if="recentWinCount"
            class="flex items-center h-40rpx px-12rpx border-1px border-solid border-#D1302E rounded-12rpx text-24rpx text-#D1302E"
          >
            {{ recentWinCount }}
          </text>
        </view>
        <view class="flex flex-col justify-between items-center text-#D1302E">
          <text>{{ hitRate }}%</text>
          <text>准确率</text>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { isEmpty } from 'lodash-es'
import { FOCUSED_AUTHOR_TYPE } from '@/utils/enum'
import emptyImg from '@/static/images/empty.png'
import { IFocusedAuthor, getFocusedAuthor } from '@/api/author'

const emit = defineEmits<{ (e: 'back'): void }>()

const authors = ref<{
  focused: IFocusedAuthor[]
  hot: IFocusedAuthor[]
  hit: IFocusedAuthor[]
  all: IFocusedAuthor[]
}>({
  focused: [],
  hot: [],
  hit: [],
  all: [],
})

const authorOptions = ref([
  { value: '关注', type: FOCUSED_AUTHOR_TYPE.FOCUSED },
  { value: '热卖', type: FOCUSED_AUTHOR_TYPE.HOT },
  { value: '高命中', type: FOCUSED_AUTHOR_TYPE.HIGH_HIT },
  { value: '全部', type: FOCUSED_AUTHOR_TYPE.ALL },
])

const current = ref('关注')
const type = ref(FOCUSED_AUTHOR_TYPE.FOCUSED)

/* 当前类型的作者列表 */
const currentAuthors = computed(() => {
  if (type.value === FOCUSED_AUTHOR_TYPE.FOCUSED) return authors.value.focused
  else if (type.value === FOCUSED_AUTHOR_TYPE.HOT) return authors.value.hot
  else if (type.value === FOCUSED_AUTHOR_TYPE.HIGH_HIT) return authors.value.hit
  else return authors.value.all
})

function goBack() {
  emit('back')
}

async function fetchData(t: FOCUSED_AUTHOR_TYPE) {
  uni.showLoading()
  const authorRes = await getFocusedAuthor(t)
  if (t === FOCUSED_AUTHOR_TYPE.FOCUSED) authors.value.focused = authorRes
  else if (t === FOCUSED_AUTHOR_TYPE.HOT) authors.value.hot = authorRes
  else if (t === FOCUSED_AUTHOR_TYPE.HIGH_HIT) authors.value.hit = authorRes
  else authors.value.all = authorRes
  uni.hideLoading()
}

function handleChange(v: { value: string; type: FOCUSED_AUTHOR_TYPE }) {
  type.value = v.type
  if (isEmpty(currentAuthors.value)) fetchData(type.value)
}

function gotoAuthorInfo(id: number) {
  uni.navigateTo({ url: `/pages/author/info/index?authorId=${id}&ts=${Date.now()}` })
}

onLoad(() => {
  fetchData(type.value)
})
</script>
