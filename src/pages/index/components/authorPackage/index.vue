<template>
  <scroll-view
    :show-scrollbar="false"
    class="package-scroll-view"
    scroll-y
    @scrolltolower="handleScrollToLower"
    @refresherrefresh="handleRefresh"
    refresher-enabled
    :refresher-triggered="refresherTriggered"
  >
    <view class="p-x-30rpx pb-20rpx">
      <wd-input
        :placeholder="`共${totalCount}种套餐`"
        v-model="formData.title"
        @input="handleInput"
        custom-class="form-item"
        style="border: none"
      />
      <!-- 套餐列表 -->
      <view
        class="mt-20rpx relative p-30rpx border-b-solid border-b-#f5f5f5 border-b-opacity-20 last:border-none bg-white"
        v-for="{ id, name, price, isBuy, pvCount, buyCount, amount, type, top } in packages"
        :key="id"
        style="border-radius: 20rpx"
        @click="handleView(id)"
      >
        <img
          v-if="top"
          src="@/static/images/top-corner-marker.png"
          class="w-46rpx h-46rpx absolute top-0 right-0"
        />

        <!-- 套餐标题和价格 -->
        <view class="flex items-center p-10rpx justify-between">
          <text>{{ name }}</text>
          <text class="text-#D1302E text-26rpx">{{ type !== 4 ? `￥${price}` : '' }}</text>
        </view>

        <!-- 数据统计 -->
        <view
          class="flex items-center justify-between bg-#f7f7f7 p-20rpx mb-20rpx"
          style="border-radius: 10rpx"
        >
          <view class="flex flex-col items-center">
            <text class="text-#797979 text-20rpx mb-10rpx">查看人数(人)</text>
            <text class="text-20rpx">{{ pvCount }}</text>
          </view>
          <view class="flex flex-col items-center">
            <text class="text-#797979 text-20rpx mb-10rpx">购买人数(人)</text>
            <text class="text-20rpx">{{ buyCount }}</text>
          </view>
          <view class="flex flex-col items-center">
            <text class="text-#797979 text-20rpx mb-10rpx">收款总额(元)</text>
            <text class="text-#f23636 text-20rpx">{{ amount }}</text>
          </view>
        </view>

        <!-- 状态和操作按钮 -->
        <view class="flex items-center justify-between">
          <text :class="[isBuy === 1 ? 'text-#797979' : 'text-#f23636', 'text-20rpx']">
            {{ isBuy === 1 ? '已下架' : '正在销售中' }}
          </text>
          <view class="flex items-center">
            <wd-button size="small" @click.stop="handleStatusChange(id, isBuy)">
              {{ isBuy === 1 ? '上架' : '下架' }}
            </wd-button>
            <wd-button
              class="set-btn"
              size="small"
              style="margin-left: 10rpx"
              @click.stop="handleEdit(id)"
            >
              编辑
            </wd-button>
            <wd-button
              class="set-btn"
              size="small"
              style="margin-left: 10rpx"
              @click.stop="handleDelete(id)"
            >
              删除
            </wd-button>
          </view>
        </view>
      </view>

      <!-- 无数据提示 -->
      <view v-if="!loading && packages.length === 0" class="text-center py-100rpx text-gray-500">
        暂无数据
      </view>

      <!-- 加载中提示 -->
      <view v-if="loading && !refresherTriggered" class="text-center py-30rpx text-gray-500">
        <text>数据加载中...</text>
      </view>
    </view>
  </scroll-view>
</template>

<script lang="ts" setup>
import { ceil } from 'lodash-es'
import {
  getAuthorPackages,
  deleteAuthorPackage,
  changePackageStatus,
  IAuthorPackage,
  IAuthorPackageParams,
} from '@/api/package'

const formData = ref({
  title: '',
})

const emit = defineEmits<{ (e: 'flash'): void }>()

// 分页相关数据
const packages = ref<IAuthorPackage[]>([])
const loading = ref(false)
const current = ref(1)
const totalPage = ref(0)
const totalCount = ref(0)
const hasMore = computed(() => current.value < totalPage.value)
const refresherTriggered = ref(false)

// 获取套餐数据
function fetchData(loadMore = false) {
  loading.value = true
  if (!loadMore) {
    current.value = 1
  } else {
    current.value += 1
  }

  const params: IAuthorPackageParams = {
    pageNo: current.value,
    pageSize: 10,
  }

  return getAuthorPackages(params)
    .then((result) => {
      const { list, total } = result
      packages.value = loadMore ? [...packages.value, ...list] : list
      totalCount.value = total
      totalPage.value = ceil(total / 10)
      loading.value = false
      return { list, total }
    })
    .catch(() => {
      loading.value = false
    })
    .finally(() => {
      refresherTriggered.value = false
    })
}

// 处理滚动到底部
function handleScrollToLower() {
  if (!loading.value && hasMore.value) {
    fetchData(true)
  }
}

// 处理下拉刷新
function handleRefresh() {
  refresherTriggered.value = true
  fetchData(false)
}

// 查看套餐
function handleView(id: number) {
  uni.navigateTo({
    url: `/pages/combo/comboSet/index?id=${id}`,
  })
}

// 修改套餐状态（上架/下架）
function handleStatusChange(id: number, currentStatus: number) {
  const statusText = currentStatus === 1 ? '上架' : '下架'

  uni.showModal({
    title: '提示',
    content: `确定要${statusText}该套餐吗？`,
    success: (res) => {
      if (res.confirm) {
        changePackageStatus(id)
          .then(() => {
            uni.showToast({
              title: `${statusText}成功`,
              icon: 'none',
            })

            // 更新本地状态，避免重新请求数据
            const index = packages.value.findIndex((item) => item.id === id)
            if (index !== -1) {
              packages.value[index].isBuy = currentStatus === 0 ? 1 : 0
            }

            // 通知父组件刷新
            emit('flash')
          })
          .catch((error) => {
            uni.showToast({
              title: `${statusText}失败`,
              icon: 'none',
            })
            console.error('修改套餐状态失败:', error)
          })
      }
    },
  })
}

// 输入框输入事件
function handleInput(value: string) {
  formData.value.title = value
}

// 编辑套餐
function handleEdit(id: number) {
  uni.navigateTo({
    url: `/pages/index/adthorAddPackage?id=${id}`,
  })
}

// 删除套餐
function handleDelete(id: number) {
  uni.showModal({
    title: '提示',
    content: '确定要删除该套餐吗？',
    success: (res) => {
      if (res.confirm) {
        deleteAuthorPackage(id)
          .then(() => {
            uni.showToast({
              title: '删除成功',
              icon: 'none',
            })
            // 删除成功后刷新数据
            fetchData()
            // 通知父组件刷新
            emit('flash')
          })
          .catch((error) => {
            uni.showToast({
              title: '删除失败',
              icon: 'none',
            })
            console.error('删除套餐失败:', error)
          })
      }
    },
  })
}

// 兼容暴露方法给父组件（为了向后兼容）
defineExpose({
  refresh: () => {
    refresherTriggered.value = true
    return fetchData(false)
  },
})

// 初始加载数据
/* onMounted(() => {
  fetchData()
}) */

onShow(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.package-scroll-view {
  width: 100%;
  height: calc(100vh - 530rpx);

  .set-btn {
    color: #d1302e;
    background-color: #ffecee;
    border: 1px solid #d1302e;
  }
}

.form-item {
  height: 64rpx;
  padding-left: 10rpx;
  margin-top: 10rpx;
  border: none;
  border-radius: 12rpx;

  &.wd-input.is-not-empty {
    &::after {
      display: none;
    }
  }
}
</style>
