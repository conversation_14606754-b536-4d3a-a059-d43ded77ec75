<template>
  <view class="pt-20rpx px-30rpx">
    <view
      class="flex items-center mb-15rpx py-10rpx px-30rpx text-26rpx text-black text-opacity-50 bg-white rounded-12rpx"
    >
      {{ `共${package.length}篇套餐` }}
    </view>
    <template v-if="isEmpty(package)">
      <wd-status-tip image="content" tip="暂无内容" />
    </template>
    <template v-else>
      <view class="flex flex-col gap-y-15rpx text-26rpx">
        <view
          v-for="{ id, privilegeName, totalArticleCount, date, children, type } in package"
          :key="id"
          @click="gotoPackageDetail(id)"
          class="flex justify-between items-center p-30rpx rounded-12rpx bg-white"
        >
          <view class="flex flex-col gap-y-10rpx">
            <view class="flex gap-x-10rpx">
              <text class="text-30rpx text-black text-opacity-90">{{ privilegeName }}</text>
              <view v-if="!isEmpty(children)" class="flex items-center gap-x-10rpx text-24rpx">
                <text
                  v-for="{ id, days } in children.slice(0, 3)"
                  :key="id"
                  class="px-15rpx text-white bg-#D1302E rounded-8rpx"
                >
                  {{ `${days}天` }}
                </text>
              </view>
            </view>
            <text class="text-26rpx text-black text-opacity-50" v-if="type === 4">
              {{ `共${totalArticleCount || 0}篇文章 | 每日更新${date || 0}篇文章` }}
            </text>
          </view>
          <wd-icon name="arrow-right" />
        </view>
      </view>
    </template>
  </view>
</template>
<script setup lang="ts">
import { IAuthorPackage } from '@/api/article'
import { isEmpty } from 'lodash-es'

const props = defineProps<{ package: IAuthorPackage[]; curAuthorId: number }>()

function gotoPackageDetail(id: number) {
  uni.navigateTo({
    url: `/pages/authorPackageDetail/index?curAuthorId=${props.curAuthorId}&packageId=${id}`,
  })
}
</script>
