<template>
  <view class="flex gap-x-[8rpx] pb-[30rpx] overflow-x-auto" style="-ms-overflow-style: none; scrollbar-width: none;">
    <view v-for="{ authorId, avatar, articleNum, nickname, qq, wxQrcodeUrl } in authors" :key="authorId"
      class="relative flex flex-col items-center w-[120rpx] shrink-0 ccc" @click="changeAuthor(authorId, qq, wxQrcodeUrl)">
      <image :src="avatar || 'https://sacdn.850g.com/football/static/avatar.svg'"
        class="w-[100rpx] h-[100rpx] rounded-full" />
      <text class="mt-10rpx truncate w-full text-center" :class="authorId == curAuthorId ? 'text-[#E34D59]' : ''"
        style="font-size: 24rpx">
        {{ nickname }}
      </text>
      <view v-if="articleNum"
        class="flex justify-center items-center absolute top-0 right-[-5rpx] w-[44rpx] h-[30rpx] bg-[#E34D59] text-white rounded-2xl text-[20rpx]">
        {{ articleNum }}
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { IAuthor } from '@/api/user'

defineProps<{ authors: IAuthor[]; curAuthorId: number }>()
const emit = defineEmits(['changeAuthor'])

const changeAuthor = (authorId: number, qq: string, wxQrcodeUrl: string) => {
  emit('changeAuthor', authorId, qq, wxQrcodeUrl)
}

// const goAuthor = (id: number) => {
//   uni.navigateTo({ url: `/pages/author/info/index?authorId=${id}` })
// }
</script>
