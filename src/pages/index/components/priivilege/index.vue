<template>
  <view class="flex flex-col">
    <view class="flex justify-between items-center">
      <text class="mt-30rpx mb-10rpx text-black text-opacity-50 text-28rpx">
        {{ `共${package.length || 0}个套餐` }}
      </text>
      <div>
        <!-- <wd-button :custom-class="{
          'filter-btn-privilege': true,
          active: filterType === PRIVILEGE_TYPE.TIME,
        }" @click.stop="changeFilterType(PRIVILEGE_TYPE.TIME)"> -->
        <wd-button
          :custom-class="
            filterType === PRIVILEGE_TYPE.TIME
              ? 'filter-btn-privilege active'
              : 'filter-btn-privilege'
          "
          @click.stop="changeFilterType(PRIVILEGE_TYPE.TIME)"
        >
          包时
        </wd-button>
        <!-- <wd-button :custom-class="{
          'filter-btn-privilege': true,
          active: filterType === PRIVILEGE_TYPE.NUMBER,
        }" @click.stop="changeFilterType(PRIVILEGE_TYPE.NUMBER)"> -->
        <wd-button
          :custom-class="
            filterType === PRIVILEGE_TYPE.NUMBER
              ? 'filter-btn-privilege active'
              : 'filter-btn-privilege'
          "
          @click.stop="changeFilterType(PRIVILEGE_TYPE.NUMBER)"
        >
          包次
        </wd-button>
      </div>
    </view>
    <view
      v-for="{ privilegeName, price, id } in package"
      :key="privilegeName"
      class="flex p-y-20rpx"
      style="border-bottom: 1rpx solid rgba(121, 121, 121, 0.2)"
      @click="gotoPrivilegePurchase(id)"
    >
      <!-- 左 -->
      <view class="flex-1 flex flex-col gap-row-5rpx">
        <text class="text-black text-opacity-90 text-32rpx leading-45rpx">{{ privilegeName }}</text>
        <text
          class="flex justify-center items-center w-80rpx h-40rpx bg-#079B8C bg-opacity-10 rounded-lg text-#70B603 text-24rpx"
          style="border: 1rpx solid #70b603"
        >
          在售
        </text>
      </view>
      <!-- 右 -->
      <text class="flex items-center text-#D1302E text-36rpx">{{ `${price}鱼币` }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { IPackage } from '@/api/article'
import { PRIVILEGE_TYPE } from '@/utils/enum'

const props = defineProps<{ package: IPackage[]; authorId: number; filterType: PRIVILEGE_TYPE }>()
const { authorId } = toRefs(props)

function gotoPrivilegePurchase(id) {
  uni.navigateTo({
    url: `/pages/author/privilege/index?authorId=${authorId.value}&privilegeId=${id}`,
  })
}

const emit = defineEmits(['changeType'])

const changeFilterType = (type: PRIVILEGE_TYPE) => {
  emit('changeType', type)
}
</script>
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared', // 解除样式隔离
  },
}
</script>
<style lang="scss" scoped>
:deep() {
  .filter-btn-privilege {
    width: 82rpx !important;
    min-width: unset !important;
    height: 40rpx !important;
    font-size: 26rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.5) !important;
    background-color: #fff !important;
    border-radius: 8rpx;

    &.active {
      color: #fff !important;
      background-color: #d1302e !important;
    }
  }
}
</style>
