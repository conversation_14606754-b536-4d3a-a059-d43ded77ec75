<template>
  <view class="pt-20rpx px-30rpx">
    <view
      class="flex items-center mb-15rpx py-10rpx px-30rpx text-26rpx text-black text-opacity-50 bg-white rounded-12rpx">
      {{ `共${total}篇方案` }}
    </view>
    <template v-if="isEmpty(articles)">
      <wd-status-tip image="content" tip="暂无内容" />
    </template>
    <view class="flex flex-col gap-y-15rpx text-26rpx">
      <view v-for="{ id, createTime, title, intro, refundType, price, top } in articles" :key="id"
        @click="gotoSchemeDetail(id)" class="flex flex-col bg-white rounded-12rpx relative">
        <img v-if="top" src="@/static/images/top-corner-marker.png" class="w-46rpx h-46rpx absolute top-0 right-0" />
        <!-- 日期 -->
        <text class="py-10rpx pl-30rpx text-black text-opacity-50 border-bottom">
          {{ formatDateTime(createTime) }}
        </text>
        <view class="flex justify-between items-center px-30rpx py-20rpx border-bottom">
          <view class="flex flex-col gap-y-10rpx">
            <text class="text-black text-opacity-90">{{ title }}</text>
            <text class="text-black text-opacity-50">{{ intro }}</text>
          </view>
          <image v-if="refundType === 1" src="https://sacdn.850g.com/football/static/refund.png"
            class="w-[140rpx] h-[45rpx]" />
        </view>
        <view class="flex justify-between py-20rpx px-30rpx text-#D1302E" v-if="saleing">
          <text>正在销售中</text>
          <text>{{ `¥${price}` }}</text>
        </view>
        <view class="flex justify-between py-20rpx px-30rpx text-#D1302E" v-else>
          <text class="text-black text-opacity-50">已结束</text>
          <text>{{ `¥${price}` }}</text>
        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { IArticle } from '@/api/article'
import { formatDateTime } from '@/utils/format'
import { isEmpty } from 'lodash-es'

defineProps<{ articles: IArticle[]; total: number; saleing: boolean }>()

function gotoSchemeDetail(id: number) {
  uni.navigateTo({ url: `/pages/detail/index?id=${id}` })
}
</script>
