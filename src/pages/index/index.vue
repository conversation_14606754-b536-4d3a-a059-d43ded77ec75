<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '神鱼体育',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <author v-if="!!userStore.isAuthor" ref="authorRef" />
  <!-- 稍后删除!的置反逻辑 -->
  <view v-else>
    <!-- 公域 -->
    <reader-new v-if="!userStore.isLogined || userStore.userInfo.isPublicUser" />
    <!-- 私域 -->
    <reader v-else ref="readerRef" />
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import author from './author.vue'
import reader from './reader.vue'
import readerNew from './reader-new.vue'
import { getUserInfo } from '@/service/userService'

const authorRef = ref()
const readerRef = ref()
const userStore = useUserStore()

onReachBottom(() => {
  if (!!userStore.isAuthor && authorRef.value) {
    authorRef.value.loadMore()
  } else if (!userStore.isAuthor && readerRef.value) {
    readerRef.value.loadMore()
  }
})

onShow(async () => {
  if (userStore.isLogined) {
    const data = await getUserInfo()
    userStore.setUserInfo(data)
  }
})

// onPullDownRefresh(() => {
//   if (!!userStore.isAuthor && authorRef.value) {
//     authorRef.value.refresh().then(() => {
//       uni.stopPullDownRefresh()
//     })
//   }
// })
</script>
