<route lang="json5">
{
  style: {
    navigationBarTitleText: '套餐详情',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view v-if="packageDetail">
    <view class="">
      <!-- 头部区域 -->
      <view class="header">
        <view class="author-banner">
          <view class="author-info">
            <view
              :style="
                authorInfo?.authorAvatar ? `background-image: url(${authorInfo.authorAvatar})` : ''
              "
              class="author-avatar"
            ></view>
            <view class="author-detail">
              <view class="author-name">{{ authorInfo?.authorName || '加载中...' }}</view>
              <view
                v-if="authorInfo && (authorInfo.winCount >= 3 || authorInfo.recentWinCount)"
                class="tags-container"
              >
                <view v-if="authorInfo?.winCount >= 3" class="tag red-tag">
                  <view class="tag-text">{{ authorInfo.winCount }}连红</view>
                </view>
                <view v-if="authorInfo?.recentWinCount" class="tag orange-tag">
                  <view class="tag-text">{{ authorInfo.recentWinCount }}</view>
                </view>
              </view>
            </view>
            <!--            <view class="follow-btn">-->
            <!--              <view class="follow-text">{{ isFollowed ? '已关注' : '关注' }}</view>-->
            <!--            </view>-->
          </view>
          <view class="author-desc">
            {{
              authorInfo?.intro ||
              '简介:串串捕手，专业串子推手!基础d串+搏击串+超级串，总有一款适合你!'
            }}
          </view>
        </view>
      </view>

      <view v-if="(userInfo?.id === authorId || packageDetail?.hasBuy) && packageDetail?.showPicUrl != '<p></p>' && packageDetail?.showPicUrl != '<p><br></p>'" class="p-10rpx bg-#fafafa rounded-12rpx overflow-hidden">
        <view class="p-[10rpx] bg-[#59C76AFF] bg-opacity-20"
              style="border-top-left-radius: 12rpx;border-top-right-radius: 12rpx">
          <text class="ml-20rpx">套餐生效中</text>
        </view>
        <view
          class="p-y-[30rpx] bg-white p-x-[10rpx]"
          style="border: 1rpx solid rgba(121, 121, 121, 0.2);border-bottom-left-radius: 12rpx;border-bottom-right-radius: 12rpx"
          v-html="packageDetail?.showPicUrl"
        />
      </view>

      <view class="m-10rpx shadow-md px-20rpx py-30rpx bg-white rounded">
        <view
          class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
          style="border-bottom: 1rpx solid #f3f3f3"
        >
          <view>套餐名称</view>
          <view class="text-#999999">{{ packageDetail?.privilegeName }}</view>
        </view>
        <view
          class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
          style="border-bottom: 1rpx solid #f3f3f3"
        >
          <view>套餐类型</view>
          <view class="text-#999999">{{ packageDetail?.type === 1 ? '包次' : '包天' }}套餐</view>
        </view>
        <view
          class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
          style="border-bottom: 1rpx solid #f3f3f3"
        >
          <view>{{ packageDetail?.type === '1' ? '次数' : '天数' }}</view>
          <view class="text-[#bc473b]">
            {{ packageDetail?.days }}{{ packageDetail?.type === '1' ? '次' : '天' }}
          </view>
        </view>
        <view
          v-if="packageDetail?.type !== 4"
          class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
          style="border-bottom: 1rpx solid #f3f3f3"
        >
          <view>比赛类型</view>
          <view class="text-#999999">{{ matchTypeFormat(packageDetail?.matchType) }}</view>
        </view>
        <view
          class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx"
          style="border-bottom: 1rpx solid #f3f3f3"
        >
          <view>套餐价格</view>
          <view class="text-#999999">{{ packageDetail.price }}元</view>
        </view>
        <view class="flex justify-between border-b border-#999999 py-30rpx px-10rpx text-26rpx">
          <view>套餐介绍</view>
        </view>
        <view
          class="py-30rpx px-20rpx bg-#f3f3f3 rounded-12rpx text-#999 text-26rpx min-h-[150rpx]"
        >
          {{ packageDetail?.content }}
        </view>
      </view>
    </view>
    <!-- 已购买 -->
    <template v-if="packageDetail && packageDetail.hasBuy">
      <view class="px-30rpx">
        <view  v-if="packageDetail.type === 4" class="flex flex-col py-20rpx mt-30rpx">
          <text class="text-30rpx text-black text-opacity-90">目录</text>
          <text class="text-26rpx text-black text-opacity-50">
            {{
              `共${packageDetail.totalArticle ? packageDetail.totalArticle.length : 0}文章 每日更新${
                packageDetail.date || 0
              }篇文章`
            }}
          </text>
        </view>
        <view v-if="!isEmpty(packageDetail.totalArticle)" class="flex flex-col border-top">
          <view
            v-for="{ id, title, intro } in packageDetail.totalArticle"
            :key="id"
            class="flex justify-between items-center py-20rpx border-bottom"
            @click="gotoSchemeDetail(id)"
          >
            <view class="flex flex-col gap-y-10rpx">
              <text class="text-30rpx text-black text-opacity-90">{{ title }}</text>
              <text class="text-26rpx text-black text-opacity-50">{{ intro }}</text>
            </view>
            <wd-icon name="arrow-right" />
          </view>
        </view>
      </view>
    </template>
    <!-- 未购买 -->
    <template v-else-if="packageDetail && !packageDetail.hasBuy">
      <view class="flex flex-col pt-[30rpx] px-30rpx pb-48px bg-white">
        <view class="no-buy">
          <text class="text-[#D1302E] font-normal" style="font-size: 36rpx">购买后可查看套餐</text>
          <text
            class="font-normal mt-[20rpx] mb-[10rpx]"
            style="font-size: 30rpx; color: rgba(0, 0, 0, 0.5)"
          >
            *本页面所有内容及图片仅代表发布者个人观点*
          </text>
          <text class="font-normal" style="font-size: 30rpx; color: rgba(0, 0, 0, 0.5)">
            *请读者仅作参考！*
          </text>
        </view>
      </view>
      <view class="m-30rpx pb-40rpx text-#D1302E text-26rpx">
        *温馨提示：此观点仅代表作者，不代表平台*
      </view>
      <!-- 购买 -->
      <view class="fixed bottom-0 w-full pt-20rpx bg-white">
        <view class="flex pl-[30rpx] items-center">
          <wd-checkbox v-model="checkVal" checked-color="#d1302e" />
          <text class="font-size-[26rpx] text-[#666]" @click="showAgreement">
            请仔细阅读并勾选
            <text class="text-[#d1302e]">《用户购买协议》</text>
          </text>
        </view>
        <view class="flex justify-between items-center h-[100rpx] pl-[30rpx] pr-[40rpx] bg-white">
          <text class="font-normal" style="font-size: 30rpx; color: rgba(0, 0, 0, 0.5)">
            需支付：
          </text>
          <text class="font-normal text-[#D1302E]" style="font-size: 36rpx">
            {{ packageDetail.price }}鱼币
          </text>
          <view class="flex items-center ml-auto">
            <view
              class="flex justify-center items-center w-[160rpx] h-[70rpx] text-white rounded-lg bg-[#D1302E]"
              style="font-size: 32rpx; border: 1px solid #d1302e"
              @click="showPayModal"
            >
              立刻支付
            </view>
          </view>
          <!-- </template> -->
        </view>
      </view>
    </template>

  </view>
  <buyAgreement ref="buyAgreementRef" @pass="passAgreement" />
  <wd-popup v-model="packageVisible" :close-on-click-modal="false" position="bottom">
    <wd-icon
      class="absolute top-[20rpx] right-[30rpx]"
      name="close"
      size="30rpx"
      @click="packageVisible = false"
    />
    <view class="flex flex-col p-[30rpx] modal">
      <view class="text-center text-36rpx font-bold mb-[40rpx]">选择套餐卡使用</view>
      <view class="flex items-center justify-between p-[30rpx]" @click="directPay">
        <text class="text-32rpx">直接支付</text>
        <wd-icon name="arrow-right" size="30rpx" />
      </view>
    </view>
  </wd-popup>
  <!-- 支付弹框 -->
  <wd-popup v-model="visible" :close-on-click-modal="false" position="bottom">
    <view class="flex flex-col relative pt-[50rpx] p-x-[50rpx] pb-[64rpx] modal">
      <!-- 关闭按钮 -->
      <wd-icon
        class="absolute top-[20rpx] right-[50rpx]"
        name="close"
        size="30rpx"
        @click="hidePayModal"
      />
      <!-- 价格 -->
      <view
        class="flex justify-center items-baseline pb-30rpx before:text-40rpx text-45rpx text-black text-opacity-90"
        style="font-weight: 600"
      >
        {{ packageDetail.price }}鱼币
      </view>
      <view
        class="flex justify-center items-center pb-[30rpx] font-normal before:text-[40rpx]"
        style="
          font-size: 32rpx;
          color: rgba(0, 0, 0, 0.9);
          border-bottom: 1px solid rgba(121, 121, 121, 0.2);
        "
      >
        当前账户鱼币：
        <image class="w-40rpx h-40rpx" src="https://sacdn.850g.com/football/static/gold.svg" />
        <text class="text-[#D1302E] text-32rpx font-bold">
          {{ userInfo.gold || 0 }}
        </text>
      </view>
      <view class="flex justify-between mt-[40rpx] mb-[50rpx] font-normal" style="font-size: 30rpx">
        <text
          class="w-full text-center overflow-ellipsis ellipsis whitespace-nowrap"
          style="color: rgba(0, 0, 0, 0.9)"
        >
          <!-- {{ article.authorName }} {{ article.title }} 套餐购买 -->
          {{ `${packageDetail.privilegeName}套餐购买` }}
        </text>
      </view>
      <!-- <view v-if="isDirectPay" @click="submitOrder(PAY_TYPE.CURRENCY)"
        class="flex justify-center items-center w-full h-100rpx rounded-xl bg-#D1302E text-32rpx text-white">
        鱼币支付（{{ packageDetail.price }}鱼币）
      </view> -->
      <!-- 微信支付 -->
      <view class="flex justify-between items-center" @click="submitOrder(PAY_TYPE.WECHAT)">
        <image class="w-[40rpx] h-[40rpx]" src="https://sacdn.850g.com/football/static/wx.svg" />
        <text class="ml-20rpx mr-auto">微信支付</text>
        <view
          class="flex justify-center items-center w-300rpx h-100rpx rounded-xl bg-#D1302E text-32rpx text-white"
        >
          充值并支付{{ packageDetail.price }}鱼币
        </view>
      </view>
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import { isEmpty } from 'lodash-es'
import buyAgreement from '@/components/buyAgreement/index.vue'
import { IPackageDetail, getPackageDetailById } from '@/api/article'
import { useUserStore } from '@/store'
import { ORDER_STATUS, PAY_TYPE } from '@/utils/enum'
import { cleanUrl } from '@/utils/sqbPay'
import { generateOrder } from '@/api/privilegeOrder'
import { getUserInfo } from '@/service/userService'
import { onBridgeReady } from '@/utils/wxPay'
import { getArticleInfo } from '@/api/author'

const buyAgreementRef = ref()
const packageDetail = ref<IPackageDetail>()
const visible = ref(false)
const checkVal = ref(true)
const packageVisible = ref(false)
const loading = ref(false)
const authorId = ref()
const authorInfo = ref()
const id = ref()

const { userInfo, setUserInfo } = useUserStore()

function gotoSchemeDetail(id: number) {
  uni.navigateTo({ url: `/pages/detail/index?id=${id}` })
}

function showAgreement() {
  buyAgreementRef.value.showDialog()
}

function passAgreement() {
  checkVal.value = true
}

function showPayModal() {
  if (!checkVal.value) {
    showAgreement()
  } else {
    packageVisible.value = true
  }
}

// 直接支付
function directPay() {
  packageVisible.value = false
  visible.value = true // 显示原来的支付弹窗
}

function hidePayModal() {
  visible.value = false
}

async function updateUserInfo() {
  const data = await getUserInfo()
  setUserInfo(data)
}

async function getData() {
  packageDetail.value = await getPackageDetailById(id.value)
  authorInfo.value = await getArticleInfo(authorId.value)
}

const matchTypeFormat = (val) => {
  // 0.全部 1.14场 2.任9 3.单关 4.二串一 5.多串一

  if (val === 0) {
    return '全部'
  } else if (val === 1) {
    return '14场'
  } else if (val === 2) {
    return '任9'
  } else if (val === 3) {
    return '单关'
  } else if (val === 4) {
    return '二串一'
  } else if (val === 5) {
    return '多串一'
  }
}

/* 支付订单 */
async function submitOrder(type: PAY_TYPE) {
  if (loading.value) return // 上一笔订单正在支付中
  loading.value = true

  try {
    const redirectUrl = cleanUrl(window.location.href)
    const data = await generateOrder(authorId.value, packageDetail.value.id, type, redirectUrl)
    if (!data) return
    const { status, payUrl } = data
    if (type === PAY_TYPE.CURRENCY) {
      // 如果是现金支付
      if (status === ORDER_STATUS.SUCCESS) {
        uni.showToast({
          icon: 'success',
          title: '订单支付成功',
        })
        hidePayModal()
        await updateUserInfo()
      } else if (status === ORDER_STATUS.FAIL) {
        uni.showToast({
          icon: 'fail',
          title: '订单支付失败',
        })
      }
      getData()
    } else {
      if (status === ORDER_STATUS.PENDING) {
        // 微信支付参数
        const wxJsapiParams = data.wxJsapiParams
        onBridgeReady(
          wxJsapiParams,
          () => {
            setTimeout(() => {
              getData()
              hidePayModal()
            }, 3000)
          },
          () => {
            console.log('支付失败')
          },
        )
      }
    }
  } finally {
    loading.value = false
  }
}

onLoad(async ({ id: _id, authorId: aid }) => {
  try {
    uni.showLoading()
    authorId.value = aid
    id.value = _id
    // packageDetail.value = await getPackageDetailById(id)
    // uni.setNavigationBarTitle({ title: packageDetail.value.privilegeName })
    await getData()
  } finally {
    uni.hideLoading()
  }
})
</script>

<style lang="scss" scoped>
.no-buy {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 378rpx;
  padding-top: 90rpx;
  margin-top: 30rpx;
  background-image: url('https://sacdn.850g.com/football/static/no-buy_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.header {
  position: relative;
  width: 100%;

  .author-banner {
    height: 230rpx;
    padding: 35rpx 20rpx 0 23rpx;
    background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/a76dfba3464c4107abec66790df127cf_mergeImage.png);
    background-size: 100% 100%;

    .author-info {
      display: flex;
      align-items: flex-start;

      .author-avatar {
        width: 120rpx;
        height: 120rpx;
        background-position: center;
        background-size: cover;
        border: 3rpx solid #ffffff;
        border-radius: 60rpx;
      }

      .author-detail {
        margin: 8rpx 0 16rpx 21rpx;

        .author-name {
          margin-right: 33rpx;
          font-family: PingFangSC-Semibold;
          font-size: 30rpx;
          font-weight: 600;
          line-height: 42rpx;
          color: #ffffff;
          text-align: left;
          white-space: nowrap;
        }

        .tags-container {
          display: flex;
          justify-content: space-between;
          width: 222rpx;
          margin: 20rpx 0 0 1rpx;

          .tag {
            padding: 1rpx 9rpx;
            background-color: #ffb206;
            border-radius: 6rpx;

            .tag-text {
              display: flex;
              align-items: center;
              justify-content: center;
              font-family: PingFangSC-Medium;
              font-size: 24rpx;
              font-weight: 500;
              color: #ffffff;
            }
          }

          .red-tag {
            background-color: #ffb206;
          }

          .orange-tag {
            margin-left: 10rpx;
            background-color: #ff8c89;
          }
        }
      }

      .follow-btn {
        position: relative;
        padding: 6rpx 21rpx 5rpx 21rpx;
        margin: 9rpx 0 63rpx 223rpx;

        &::after {
          position: absolute;
          top: 0;
          left: 0;
          width: 120rpx;
          height: 48rpx;
          content: '';
          border: 1rpx solid #ffffff;
          border-radius: 25rpx;
        }

        .follow-text {
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          font-weight: 500;
          line-height: 37rpx;
          color: #ffffff;
          text-align: left;
          white-space: nowrap;
        }
      }
    }

    .author-desc {
      width: 687rpx;
      height: 99rpx;
      margin: 17rpx 6rpx 0 14rpx;
      font-family: PingFangSC-Medium;
      font-size: 24rpx;
      font-weight: 500;
      line-height: 33rpx;
      color: #ffffff;
      text-align: left;
    }
  }
}
</style>
