<template>
  <view class="flex flex-col pb-120rpx">
    <template v-if="mode === HOME_PAGE_MODE.SEARCH">
      <!-- 搜索模式 -->
      <view class="flex items-center gap-x-60rpx px-30rpx py-12rpx search-bar">
        <view class="flex justify-end w-60rpx">
          <wd-icon
            name="arrow-left"
            size="40rpx"
            color="rgba(0,0,0,0.9)"
            @click="changePageMode(HOME_PAGE_MODE.MAIN)"
          />
        </view>
        <wd-input
          :focus="true"
          placeholder="搜索球队名称 曼城 I 利物浦"
          prefix-icon="search1"
          placeholderClass="place-search"
          custom-class="search"
        />
      </view>
    </template>
    <!-- 作者列表模式 -->
    <template v-else-if="mode === HOME_PAGE_MODE.AUTHOR">
      <author-list @back="changePageMode(HOME_PAGE_MODE.MAIN)" />
    </template>
    <!-- 文章列表模式 -->
    <template v-else-if="mode === HOME_PAGE_MODE.ARTICLE">
      <article-list :play="gamePlay" @back="changePageMode(HOME_PAGE_MODE.MAIN)" />
    </template>
    <template v-else>
      <view class="flex items-center gap-x-60rpx px-30rpx py-12rpx">
        <text class="text-30rpx text-black text-opacity-90">足球</text>
        <view
          @click="changePageMode(HOME_PAGE_MODE.SEARCH)"
          style="background-color: rgba(0, 0, 0, 0.02)"
          class="flex-1 flex gap-x-10rpx items-center h-64rpx pl-16rpx border-1rpx border-solid border-black border-opacity-10 rounded-12rpx"
        >
          <wd-icon name="search1" size="32rpx" color="rgba(0,0,0,0.5)" />
          <text class="text-28rpx text-black text-opacity-50">搜索球队名称 曼城 I 利物浦</text>
        </view>
      </view>
      <!-- 分类 -->
      <view
        class="flex justify-between mt-26rpx mb-30rpx px-40rpx text-24rpx text-black text-opacity-90"
      >
        <view
          class="flex flex-col items-center gap-y-10rpx"
          @click="gotoArticles(HOME_ARTICLE_PLAY.SINGLE_GAME_BET)"
        >
          <image
            class="w-80rpx h-80rpx"
            src="https://sacdn.850g.com/football/static/icons/home/<USER>"
          />
          <text>单关推荐</text>
        </view>
        <view
          class="flex flex-col items-center gap-y-10rpx"
          @click="gotoArticles(HOME_ARTICLE_PLAY.TEAM_PARLAY)"
        >
          <image
            class="w-80rpx h-80rpx"
            src="https://sacdn.850g.com/football/static/icons/home/<USER>"
          />
          <text>2串1专区</text>
        </view>
        <view
          class="flex flex-col items-center gap-y-10rpx"
          @click="gotoArticles(HOME_ARTICLE_PLAY.ON_SITE_RECOMMENDATION)"
        >
          <image
            class="w-80rpx h-80rpx"
            src="https://sacdn.850g.com/football/static/icons/home/<USER>"
          />
          <text>临场推荐</text>
        </view>
        <view
          class="flex flex-col items-center gap-y-10rpx"
          @click="gotoArticles(HOME_ARTICLE_PLAY.REFUND)"
        >
          <image
            class="w-80rpx h-80rpx"
            src="https://sacdn.850g.com/football/static/icons/home/<USER>"
          />
          <text>不中即退</text>
        </view>
        <view
          class="flex flex-col items-center gap-y-10rpx"
          @click="gotoArticles(HOME_ARTICLE_PLAY.FREE)"
        >
          <image
            class="w-80rpx h-80rpx"
            src="https://sacdn.850g.com/football/static/icons/home/<USER>"
          />
          <text>免费专区</text>
        </view>
      </view>
      <!-- 赛事 -->
      <template v-if="isEmpty(matchs)">
        <view class="flex flex-col justify-center items-center h-200rpx mb-20rpx">
          <image :src="emptyImg" class="w-244rpx h-133rpx" />
          <text class="mt-10rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
        </view>
      </template>
      <template v-else>
        <scroll-view scroll-x :show-scrollbar="false">
          <view class="flex gap-x-30rpx mx-30rpx pb-30rpx">
            <template v-for="match in matchs" :key="match.id">
              <view
                @click="gotoMatchInfo(match.id)"
                class="flex shadow-lg flex-col flex-none justify-between w-330rpx h-200rpx pt-20rpx pb-24rpx px-20rpx border-1rpx border-solid border-black border-opacity-10 rounded-12rpx"
              >
                <!-- 上 -->
                <view class="flex justify-between items-center">
                  <text class="text-26rpx text-black text-opacity-50">
                    {{ match.competitionName }}
                  </text>
                  <view class=""></view>
                  <text
                    class="h34rpx px-9rpx border-2rpx border-solid border-#D1302E rounded-12rpx text-20rpx text-#D1302E leading-34rpx"
                  >
                    {{ MATCH_STATUS_TXT[match.statusId] }}
                  </text>
                </view>
                <!-- 中(主队) -->
                <view class="flex justify-between items-center">
                  <image class="w-40rpx h-40rpx" :src="match.homeTeamLogo" />
                  <text class="ml-10rpx mr-auto text-24rpx text-black text-opacity-90">
                    {{ match.homeTeamName }}
                  </text>
                  <text class="text-26rpx text-black text-opacity-90">
                    {{ matchDate(match.matchTime) }}
                  </text>
                </view>
                <!-- 下(客队) -->
                <view class="flex justify-between items-center">
                  <image class="w-40rpx h-40rpx" :src="match.awayTeamLogo" />
                  <text class="ml-10rpx mr-auto text-24rpx text-black text-opacity-90">
                    {{ match.awayTeamName }}
                  </text>
                  <text class="text-26rpx text-black text-opacity-90">
                    {{ matchTime(match.matchTime) }}
                  </text>
                </view>
              </view>
            </template>
          </view>
        </scroll-view>
      </template>
      <!-- 去开通 -->
      <!-- <view
        class="flex justify-between items-center h-54rpx mt-20rpx mb-30rpx mx-30rpx pl-10rpx pr-20rpx bg-#FFF5E0 text-24rpx rounded-12rpx"
      >
        <text class="text-#623F08">开通盈彩会员，享一手比赛情报数据，两分钟吃透比赛</text>
        <text class="text-#D1302E underline underline-offset-2">去开通</text>
      </view> -->
      <wd-gap bg-color="#F4F8FA" height="20rpx" />
      <!-- 推荐专家 -->
      <view class="flex flex-col">
        <view
          class="flex justify-between items-center px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
        >
          <text
            class="relative flex flex-col items-center h-full py-20rpx text-30rpx text-black text-opacity-50"
            :class="isActive(isRecommend)"
            @click="changeExpertType(true)"
          >
            推荐专家
          </text>
          <text
            class="relative flex flex-col items-center py-20rpx ml-28rpx mr-auto text-30rpx text-black text-opacity-50"
            :class="isActive(!isRecommend)"
            @click="changeExpertType(false)"
          >
            我的关注
          </text>
          <text
            class="text-26rpx text-black text-opacity-50"
            @click="changePageMode(HOME_PAGE_MODE.AUTHOR)"
          >
            全部>
          </text>
        </view>
        <!-- 专家列表 -->
        <template v-if="isEmpty(authors)">
          <view
            class="flex flex-col justify-center items-center h-243.5rpx"
            @click="changeExpertType(true)"
          >
            <image :src="emptyImg" class="w-244rpx h-133rpx" />
            <text class="mt-10rpx font-normal text-30rpx text-black text-opacity-30">
              您还未关注作者,点击跳转到推荐专家页面
            </text>
          </view>
        </template>
        <template v-else>
          <scroll-view :show-scrollbar="false" class="px-40rpx py-30rpx box-border" scroll-x>
            <view class="flex flex-nowrap">
              <view
                @click="gotoAuthorInfo(authorId)"
                v-for="{ authorId, authorName, avatarUrl, winCount, hitRate } in authors"
                :key="authorId"
                class="flex flex-col items-center gap-y-14rpx min-w-165rpx pb-15rpx"
              >
                <template v-if="avatarUrl">
                  <image :src="avatarUrl" class="w-80rpx h-80rpx rounded-full" />
                </template>
                <template v-else>
                  <image
                    src="https://sacdn.850g.com/football/static/icons/default-avatar2.svg"
                    class="w-80rpx h-80rpx rounded-full"
                  />
                </template>
                <text class="text-24rpx text-black text-opacity-90 h-40rpx flex items-center">
                  {{ authorName }}
                </text>
                <view
                  v-if="winCount > 2 && hitRate >= 50"
                  class="flex items-center h-40rpx border-1rpx border-solid border-#D1302E rounded-16rpx"
                >
                  <text
                    class="flex justify-center h-100% items-center px-11rpx text-28rpx text-white bg-#D1302E rounded-12rpx"
                  >
                    {{ winCount }}
                  </text>
                  <text class="ml-5rpx mr-7rpx text-24rpx text-#D1302E">连红</text>
                </view>
              </view>
            </view>
          </scroll-view>
        </template>
      </view>
      <wd-gap bg-color="#F4F8FA" height="20rpx" />
      <!-- 文章列表 -->
      <view
        class="px-30rpx py-20rpx border-b-1rpx border-b-solid border-b-#797979 border-opacity-20"
      >
        <wd-segmented
          :options="articleOptions"
          v-model:value="current"
          @change="handleSegmentChange"
          custom-class="segment"
        />
      </view>
      <template v-if="currentType === HOME_ARTICLE_TYPE.HOT">
        <!-- 热卖排序 -->
        <article-new :articles="articles.hot" />
      </template>
      <template v-else-if="currentType === HOME_ARTICLE_TYPE.ACCURACY">
        <!-- 准确率排行 -->
        <article-new :articles="articles.accuracy" />
      </template>
      <template v-if="currentType === HOME_ARTICLE_TYPE.MATCH_TIME">
        <!-- 开赛时间排序 -->
        <article-new :articles="articles.time" />
      </template>
    </template>
  </view>
</template>

<script setup lang="ts">
import { isEmpty } from 'lodash-es'
import { getHomeMatchList, IHomeMatchItem } from '@/api/match'
import { DEFAULT_PAGE_SIZE, MATCH_STATUS_TXT } from '@/utils/constant'
import { format } from '@/utils/format'
import emptyImg from '@/static/images/empty.png'
import { HOME_ARTICLE_PLAY, HOME_ARTICLE_TYPE, HOME_PAGE_MODE } from '@/utils/enum'
import { getHomeArticleList, IHomeArticleItem } from '@/api/article'
import articleNew from './components/articleNew/index.vue'
import authorList from './components/authorList/index.vue'
import articleList from './components/articleList/index.vue'

import {
  getFocusedAuthor,
  getRecommendAuthor,
  IFocusedAuthor,
  IRecommendAuthor,
} from '@/api/author'

const mode = ref(HOME_PAGE_MODE.MAIN)
const gamePlay = ref(HOME_ARTICLE_PLAY.SINGLE_GAME_BET)

const matchs = ref<IHomeMatchItem[]>([])
const articles = ref({
  hot: [],
  accuracy: [],
  time: [],
})

const recommendAuthors = ref<IRecommendAuthor[]>([])
const focusedAuthors = ref<IFocusedAuthor[]>([])

const articleOptions = ref([
  { value: '热卖排序', type: HOME_ARTICLE_TYPE.HOT },
  { value: '准确率排行', type: HOME_ARTICLE_TYPE.ACCURACY },
  { value: '开赛时间排序', type: HOME_ARTICLE_TYPE.MATCH_TIME },
])

const current = ref('开赛时间排序')

const currentType = computed(() => {
  const c = articleOptions.value.find(({ value }) => value === current.value)
  return c ? c.type : -1
})

// 是否为推荐的专家列表 false 则为我关注的专家列表
const isRecommend = ref(true)

const hotPageNo = ref(0)
const hotTotal = ref(0)

const accuracyPageNo = ref(0)
const accuracyTotal = ref(0)

const timePageNo = ref(0)
const timeTotal = ref(0)

const authors = computed(() => (isRecommend.value ? recommendAuthors.value : focusedAuthors.value))

const hotTotalPage = computed(() => Math.ceil(hotTotal.value / DEFAULT_PAGE_SIZE))
const accuracyTotalPage = computed(() => Math.ceil(accuracyTotal.value / DEFAULT_PAGE_SIZE))
const timeTotalPage = computed(() => Math.ceil(timeTotal.value / DEFAULT_PAGE_SIZE))

const currentPageNo = computed(() => {
  if (currentType.value === HOME_ARTICLE_TYPE.HOT) return hotPageNo.value
  if (currentType.value === HOME_ARTICLE_TYPE.ACCURACY) return accuracyPageNo.value
  return timePageNo.value
})

const totalPage = computed(() => {
  if (currentType.value === HOME_ARTICLE_TYPE.HOT) return hotTotalPage.value
  if (currentType.value === HOME_ARTICLE_TYPE.ACCURACY) return accuracyTotalPage.value
  return timeTotalPage.value
})

function changePageMode(m: HOME_PAGE_MODE) {
  if (m !== mode.value) mode.value = m
}

async function changeExpertType(recommend: boolean) {
  if (recommend !== isRecommend.value) {
    isRecommend.value = recommend
    if (isRecommend.value && isEmpty(recommendAuthors.value)) {
      const authorRes = await getRecommendAuthor()
      recommendAuthors.value = authorRes
    } else if (!isRecommend.value && isEmpty(focusedAuthors.value)) {
      const focusedAuthorRes = await getFocusedAuthor()
      focusedAuthors.value = focusedAuthorRes
    }
  }
}

function gotoArticles(catigory: HOME_ARTICLE_PLAY) {
  if (gamePlay.value !== catigory) gamePlay.value = catigory
  mode.value = HOME_PAGE_MODE.ARTICLE
}

const matchDate = computed(() => {
  return (timestamp: number) => (timestamp ? format(timestamp * 1000, 'MM-DD') : '')
})

const matchTime = computed(() => {
  return (timestamp: number) => (timestamp ? format(timestamp * 1000, 'HH:mm') : '')
})

const isActive = computed(() => {
  return (active: boolean) => {
    return active
      ? "after:content-[''] after:absolute after:bottom-0 after:w-40rpx after:h-6rpx after:bg-#D1302E text-opacity-90"
      : ''
  }
})

function gotoAuthorInfo(id: number) {
  uni.navigateTo({ url: `/pages/author/info/index?authorId=${id}&ts=${Date.now()}` })
}

function gotoMatchInfo(id: number) {
  uni.navigateTo({ url: `/pages/matchDetail/index?matchId=${id}` })
}

function handleData(data: { total: number; list: IHomeArticleItem[] }) {
  const type = currentType.value
  const { total, list } = data
  if (type === HOME_ARTICLE_TYPE.HOT) {
    articles.value.hot = isEmpty(articles.value.hot) ? list : [...articles.value.hot, ...list]
    hotTotal.value = total
    if (total) hotPageNo.value = hotPageNo.value + 1
  } else if (type === HOME_ARTICLE_TYPE.ACCURACY) {
    articles.value.accuracy = isEmpty(articles.value.accuracy)
      ? list
      : [...articles.value.accuracy, ...list]
    accuracyTotal.value = total
    if (total) accuracyPageNo.value = accuracyPageNo.value + 1
  } else {
    articles.value.time = isEmpty(articles.value.time) ? list : [...articles.value.time, ...list]
    timeTotal.value = total
    if (total) timePageNo.value = timePageNo.value + 1
  }
}

async function getData(loadMore = true) {
  const type = currentType.value
  uni.showLoading()
  let articleRes
  if (loadMore) {
    // 加载更多
    articleRes = await getHomeArticleList(type, currentPageNo.value + 1)
  } else {
    // 切换类型
    articleRes = await getHomeArticleList(type, 1)
  }
  handleData(articleRes)
  uni.hideLoading()
}

function handleSegmentChange({ type }: { value: string; type: HOME_ARTICLE_TYPE }) {
  let fetchData = false
  switch (type) {
    case HOME_ARTICLE_TYPE.HOT:
      if (hotPageNo.value === 0) fetchData = true
      break
    case HOME_ARTICLE_TYPE.ACCURACY:
      if (accuracyPageNo.value === 0) fetchData = true
      break
    case HOME_ARTICLE_TYPE.MATCH_TIME:
      if (timePageNo.value === 0) fetchData = true
      break
  }

  if (fetchData) getData(false)
}

onReachBottom(() => {
  // 触底加载更多数据
  if (currentPageNo.value >= totalPage.value) {
    uni.showToast({ title: '没有更多数据', icon: 'none' })
    return
  }
  if (mode.value === HOME_PAGE_MODE.MAIN) getData()
})

onLoad(async () => {
  uni.showLoading()
  const [matchList, articleRes, authorRes] = await Promise.all([
    getHomeMatchList(),
    getHomeArticleList(currentType.value, 1),
    getRecommendAuthor(),
  ])

  matchs.value = matchList
  recommendAuthors.value = authorRes
  handleData(articleRes)
  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
:deep() {
  .search-bar {
    .search {
      flex: 1;
      height: 64rpx;
      padding-left: 16rpx;
      background-color: rgba($color: #000000, $alpha: 0.02) !important;
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 12rpx;

      &::after {
        display: none !important;
      }
    }
  }
}

.place-search {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.5);
}

.expert {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.segment {
  :deep(.wd-segmented__item-label) {
    font-size: 22rpx;
  }
}
</style>
