<template>
  <view class="bg-#F5F5F5 pt-30rpx">
    <!-- <view class="h-[370rpx] w-full">
      <wd-swiper :list="swipeList" autoplay value-key="pic" @click="swipeClick"></wd-swiper>
    </view> -->
    <view class="px-30rpx rounded-xl">
      <view class="focused-title">
        <text class="nav-item" :class="{ isActive: type === ARTICLE_TYPE.MY_FOCUSED }">
          我关注的
        </text>
        <!-- <text class="nav-item" :class="{ isActive: type === ARTICLE_TYPE.HOT }" @click="changeType(ARTICLE_TYPE.HOT)"
          v-if="false">
          热门推荐
        </text> -->
      </view>
      <!-- <view class="pt-[36rpx] pb-[30rpx]"> -->
      <view class="pb-[30rpx] mt-20rpx">
        <author-list :authors="authors" :curAuthorId="curAuthorId" @changeAuthor="changeAuthor" />
        <!-- <view class="flex justify-between mt-30rpx">
          <wd-button :custom-class="{ 'filter-btn': true, active: saleType === ARTICLE_SALE_TYPE.ON_SALE }"
            @click="changeSaleType(ARTICLE_SALE_TYPE.ON_SALE)">
            在售
          </wd-button>
          <wd-button :custom-class="{ 'filter-btn': true, active: saleType === ARTICLE_SALE_TYPE.PACKAGE }"
            @click="changeSaleType(ARTICLE_SALE_TYPE.PACKAGE)">
            套餐
          </wd-button>
        </view> -->
        <!-- <article-list
          :saleType="saleType"
          :filterType="filterType"
          :total="focusedCount"
          :articles="articles"
          @changeType="changeFilterType"
          v-if="saleType === ARTICLE_SALE_TYPE.ON_SALE"
        />
        <privilege
          :package="packageList"
          :authorId="curAuthorId"
          :filter-type="privilegeType"
          @changeType="changePrivilegeType"
          v-else
        /> -->
      </view>
    </view>
    <view class="content">
      <wd-tabs v-model="activeTab" custom-class="tab" @click="handleTabChange">
        <wd-tab title="在售方案" name="sale">
          <scheme :articles="saleArticles" :total="saleCount" :saleing="true" />
        </wd-tab>
        <wd-tab title="历史方案" name="history">
          <scheme :articles="historyArticles" :total="historyCount" :saleing="false" />
        </wd-tab>
        <wd-tab title="作者套餐" name="package">
          <author-user-package :package="packageList" :curAuthorId="curAuthorId" />
        </wd-tab>
      </wd-tabs>
    </view>
    <!-- 显示关注公众号二维码 -->
<!--    <wd-popup v-model="copyQQVisible" closable>-->
<!--      <view class="w-[450rpx] text-center font-size-[32rpx]">-->
<!--        <view class="m-[30rpx]">添加作者QQ</view>-->
<!--        <view class="m-[40rpx]">-->
<!--          <text class="text-[#666666]">{{ authorQq }}</text>-->
<!--        </view>-->

<!--        <view class="m-[20rpx]">-->
<!--          <wd-button @click="toCopyQQ">复制</wd-button>-->
<!--        </view>-->
<!--      </view>-->
<!--    </wd-popup>-->
<!--    <wd-fab :expandable="false" @click="toQQ" position="right-bottom" class="mt-[-190rpx] ml-[-10rpx]" :draggable="true"-->
<!--      v-if="!userStore.userInfo?.isAuthor && authorQq">-->
<!--      <template #trigger>-->
<!--        <view class="revice" @click="toQQ">-->
<!--          <image src="https://sacdn.850g.com/football/static/button/qq.png" class="w-full h-full" />-->
<!--        </view>-->
<!--      </template>-->
<!--    </wd-fab>-->
    <wd-fab :expandable="false" position="right-bottom" class="mt-[-80rpx]" :draggable="true"
      v-if="userStore.userInfo?.isAuthor">
      <template #trigger>
        <view class="revice" @click="toRelaese">
          <svg t="1732157299191" class="icon" viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
            p-id="3020" width="90" height="90">
            <path
              d="M512 1024C229.230592 1024 0 794.769408 0 512S229.230592 0 512 0s512 229.230592 512 512-229.230592 512-512 512z m-42.667008-554.667008H256c-23.564288 0-42.667008 19.10272-42.667008 42.667008s19.10272 42.667008 42.667008 42.667008h213.332992V768c0 23.564288 19.10272 42.667008 42.667008 42.667008s42.667008-19.10272 42.667008-42.667008V554.667008H768c23.564288 0 42.667008-19.10272 42.667008-42.667008S791.564288 469.332992 768 469.332992H554.667008V256c0-23.564288-19.10272-42.667008-42.667008-42.667008S469.332992 232.435712 469.332992 256v213.332992z"
              fill="#d1302e" p-id="3021"></path>
          </svg>
        </view>
      </template>
    </wd-fab>
<!--    <qqMail v-if="wxQrCode" :qrCode="wxQrCode"/>-->
  </view>
</template>
<script lang="ts" setup>
import {
  ARTICLE_DEFAULT_PAGE,
  IArticle,
  // IPackage,
  // getAuthorPrivilege,
  getSchemeListByAuthorId,
  getHotArticleList,
  getPackageListByAuthorId,
  // IAuthorPackage,
  IPackageDetail,
} from '@/api/article'
import { ceil, isEmpty, isNil } from 'lodash-es'
import {
  IAuthor,
  getHotAuthorList,
  getMyAuthorListToFollow,
  // IBanner,
  // getBannerList,
} from '@/api/user'
import authorList from './components/authors/index.vue'
// import articleList from './components/arrticle/index.vue'
// import privilege from './components/priivilege/index.vue'
import scheme from './components/scheme/index.vue'
import authorUserPackage from './components/authorUserPackage/index.vue'
import qqMail from '@/components/qqmail/index.vue'
import { ARTICLE_FILTER_TYPE, ARTICLE_SALE_TYPE, ARTICLE_TYPE, PRIVILEGE_TYPE } from '@/utils/enum'
import { useUserStore } from '@/store'
// import { DEFAULT_DATETIME } from '@/utils/format'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
defineExpose({ loadMore })
const userStore = useUserStore()
const {
  // @ts-ignore
  proxy: { $onLaunched },
} = getCurrentInstance()
defineOptions({
  name: 'Home',
})

const type = ref(ARTICLE_TYPE.MY_FOCUSED)
const filterType = ref(ARTICLE_FILTER_TYPE.LATEST)
// const saleType = ref(ARTICLE_SALE_TYPE.ON_SALE)
const privilegeType = ref(PRIVILEGE_TYPE.TIME)

const curAuthorId = ref(0)

const focusedAuthors = ref<IAuthor[]>([])

const saleArticles = ref<IArticle[]>([])
const salePageNo = ref(1)
const saleCount = ref(0)
const saleTotalPage = ref(0)

const historyArticles = ref<IArticle[]>([])
const historyPageNo = ref(1)
const historyCount = ref(0)
const historyTotalPage = ref(0)

const hotAuthors = ref<IAuthor[]>([])
const hotArticles = ref<IArticle[]>([])
const hotPageNo = ref(1)
const hotTotalPage = ref(0)
const copyQQVisible = ref(false)
const authorQq = ref('')
const wxQrCode = ref('')

// 套餐
const packageList = ref<IPackageDetail[]>([])
// const swipeList = ref<IBanner[]>([
//   {
//     pic: 'https://sacdn.850g.com/football/config/banner/index.png',
//     title: '神鱼体育',
//   },
// ])

onShow(() => {
  salePageNo.value = 1
  historyPageNo.value = 1
  hotPageNo.value = 1
})

const activeTab = ref('sale')

const toQQ = () => {
  copyQQVisible.value = true
}
const toCopyQQ = () => {
  uni.setClipboardData({
    data: authorQq.value,
    success() {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    },
  })
}
const toRelaese = () => {
  uni.navigateTo({
    url: '/pages/article/relaese/index',
  })
}

// const swipeClick = (e) => {
//   if (e.item.toUrl) {
//     if (e.item.toUrl.indexOf('http') !== -1) {
//       location.href = e.item.toUrl
//     } else {
//       uni.navigateTo({
//         url: e.item.toUrl,
//       })
//     }
//   }
// }

const authors = computed(() =>
  type.value === ARTICLE_TYPE.MY_FOCUSED ? focusedAuthors.value : hotAuthors.value,
)
// const articles = computed(() => {
//   return type.value === ARTICLE_TYPE.MY_FOCUSED ? saleArticles.value : hotArticles.value
//   return activeTab.value === 'sale' ? saleArticles.value : historyArticles.value
// })

// const pageNo = computed(() =>
//   type.value === ARTICLE_TYPE.MY_FOCUSED ? salePageNo.value : hotPageNo.value,
// )

// const totalPage = computed(() =>
//   type.value === ARTICLE_TYPE.MY_FOCUSED ? saleTotalPage.value : hotTotalPage.value,
// )

// function changeSaleType(t: ARTICLE_SALE_TYPE) {
//   if (t !== saleType.value) saleType.value = t
//   if (t === ARTICLE_SALE_TYPE.ON_SALE && isEmpty(saleArticles)) {
//     getSchemeListByAuthorId(curAuthorId.value, 1).then((ae) => {
//       if (!isNil(ae)) {
//         saleArticles.value = ae.list
//         salePageNo.value = 1
//         saleCount.value = ae.total
//         saleTotalPage.value = ceil(ae.total / ARTICLE_DEFAULT_PAGE)
//       }
//     })
//   } else if (t === ARTICLE_SALE_TYPE.PACKAGE) {
//     getAuthorPrivilege(curAuthorId.value, privilegeType.value).then((p) => {
//       packageList.value = p
//     })
//   }
// }

// const changePrivilegeType = (t: PRIVILEGE_TYPE) => {
//   privilegeType.value = t
//   changeSaleType(ARTICLE_SALE_TYPE.PACKAGE)
// }

// function changeFilterType(t: ARTICLE_FILTER_TYPE) {
//   if (filterType.value === t) return
//   getSchemeListByAuthorId(curAuthorId.value, 1, t).then((ae) => {
//     filterType.value = t
//     if (!isNil(ae)) {
//       saleArticles.value = ae.list
//       salePageNo.value = 1
//       saleCount.value = ae.total
//       saleTotalPage.value = ceil(ae.total / ARTICLE_DEFAULT_PAGE)
//     }
//   })
// }

function changeAuthor(authorId: number, qq: string, wxQrcodeUrl:string) {
  if (authorId === curAuthorId.value) return
  wxQrCode.value = wxQrcodeUrl
  curAuthorId.value = authorId
  authorQq.value = qq
  filterType.value = ARTICLE_FILTER_TYPE.LATEST
  privilegeType.value = PRIVILEGE_TYPE.TIME

  saleArticles.value = []
  salePageNo.value = 0
  saleCount.value = 0
  saleTotalPage.value = 0

  historyArticles.value = []
  historyPageNo.value = 0
  historyCount.value = 0
  historyTotalPage.value = 0

  packageList.value = []
  getData()

  // Promise.all([
  //   getSchemeListByAuthorId(curAuthorId.value, 1, ARTICLE_FILTER_TYPE.LATEST).then((ae) => {
  //     if (!isNil(ae)) {
  //       saleArticles.value = ae.list
  //       salePageNo.value = 1
  //       saleCount.value = ae.total
  //       saleTotalPage.value = ceil(ae.total / ARTICLE_DEFAULT_PAGE)
  //     }
  //   }),
  //   getAuthorPrivilege(curAuthorId.value, privilegeType.value).then((p) => {
  //     packageList.value = p
  //   }),
  // ])
}

function initHotData() {
  Promise.all([getHotAuthorList(), getHotArticleList(1)]).then(([as, ae]) => {
    if (!isNil(as)) {
      hotAuthors.value = as
    }

    if (!isNil(ae)) {
      hotArticles.value = ae.list
      hotPageNo.value = 1
      hotTotalPage.value = ceil(ae.total / ARTICLE_DEFAULT_PAGE)
    }
  })
}

function initFocusedData() {
  getMyAuthorListToFollow().then((data) => {
    focusedAuthors.value = data || []
    if (focusedAuthors.value.length > 0) {
      curAuthorId.value = focusedAuthors.value[0].authorId
      getSchemeListByAuthorId(curAuthorId.value, 1, filterType.value).then((ae) => {
        saleArticles.value = ae.list
        salePageNo.value = 1
        saleCount.value = ae.total
        saleTotalPage.value = ceil(ae.total / ARTICLE_DEFAULT_PAGE)
      })
    } else {
      saleArticles.value = []
      salePageNo.value = 1
      saleCount.value = 0
      saleTotalPage.value = 0
    }
  })
}

// function changeType(t: ARTICLE_TYPE) {
//   if (t !== type.value) type.value = t
//   if (pageNo.value === 0) {
//     t === ARTICLE_TYPE.MY_FOCUSED ? initFocusedData() : initHotData()
//   }
// }

// function bannerList() {
//   getBannerList().then((data) => {
//     if (data && data.length > 0) {
//       swipeList.value = data
//     }
//   })
// }

function loadMore() {
  if (activeTab.value === 'package') return

  const pageNo = activeTab.value === 'sale' ? salePageNo.value : historyPageNo.value
  const totalPage = activeTab.value === 'sale' ? saleTotalPage.value : historyTotalPage.value

  if (pageNo >= totalPage) {
    uni.showToast({
      icon: 'none',
      title: '没有更多了',
    })
    return
  }

  getData(true)

  // if (type.value === ARTICLE_TYPE.MY_FOCUSED) {
  //   getSchemeListByAuthorId(curAuthorId.value, pageNo.value + 1, filterType.value).then((ae) => {
  //     salePageNo.value = pageNo.value + 1
  //     saleArticles.value = [...saleArticles.value, ...ae.list]
  //   })
  // } else {
  //   getHotArticleList(pageNo.value + 1).then((hae) => {
  //     hotPageNo.value = pageNo.value + 1
  //     hotArticles.value = [...hotArticles.value, ...hae.list]
  //   })
  // }
}

// onReachBottom(() => {
//   if (pageNo.value >= totalPage.value) {
//     uni.showToast({
//       icon: 'none',
//       title: '没有更多了',
//     })
//     return
//   }

//   if (type.value === ARTICLE_TYPE.MY_FOCUSED) {
//     getFocusedArticleList(curAuthorId.value, pageNo.value + 1, filterType.value).then((ae) => {
//       focusedPageNo.value = pageNo.value + 1
//       focusedArticles.value = [...focusedArticles.value, ...ae.list]
//     })
//   } else {
//     getHotArticleList(pageNo.value + 1).then((hae) => {
//       hotPageNo.value = pageNo.value + 1
//       hotArticles.value = [...hotArticles.value, ...hae.list]
//     })
//   }
// })

async function getData(loadMore = false) {
  try {
    uni.showLoading()
    if (isEmpty(focusedAuthors.value)) {
      focusedAuthors.value = await getMyAuthorListToFollow()
      wxQrCode.value = focusedAuthors.value[0]?.wxQrcodeUrl || ''
    }

    if (!isEmpty(focusedAuthors.value)) {
      curAuthorId.value = curAuthorId.value || focusedAuthors.value[0].authorId

      if (['sale', 'history'].includes(activeTab.value)) {
        // 在售方案与历史方案
        const pageNo = activeTab.value === 'sale' ? salePageNo.value : historyPageNo.value
        const t = activeTab.value === 'sale' ? 0 : 1
        const { list, total } = await getSchemeListByAuthorId(curAuthorId.value, pageNo, t)
        if (activeTab.value === 'sale') {
          saleArticles.value = loadMore ? [...saleArticles.value, ...list] : list
          salePageNo.value = salePageNo.value + 1
          saleCount.value = total
          saleTotalPage.value = ceil(total / DEFAULT_PAGE_SIZE)
        } else {
          historyArticles.value = loadMore ? [...historyArticles.value, ...list] : list
          historyPageNo.value = historyPageNo.value + 1
          historyCount.value = total
          historyTotalPage.value = ceil(total / DEFAULT_PAGE_SIZE)
        }
      } else {
        if (isEmpty(packageList.value)) {
          packageList.value = await getPackageListByAuthorId(curAuthorId.value)
        }
      }
    }
  } finally {
    uni.hideLoading()
  }
}

function handleTabChange({ name }: { name: string }) {
  if (
    (name === 'sale' && isEmpty(saleArticles.value)) ||
    (name === 'history' && isEmpty(historyArticles.value)) ||
    (name === 'package' && isEmpty(packageList.value))
  ) {
    getData()
  }
}

onPullDownRefresh(async () => {
  type.value === ARTICLE_TYPE.MY_FOCUSED ? await initFocusedData() : await initHotData()
  uni.showToast({ title: '刷新成功', icon: 'none' })
  uni.stopPullDownRefresh()
})

onShow(async () => {
  if (!userStore.isLogined) {
    await $onLaunched
  }
  // bannerList()
  getData()
  // type.value === ARTICLE_TYPE.MY_FOCUSED ? initFocusedData() : initHotData()
})
</script>
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared', // 解除样式隔离
  },
}
</script>
<style scoped lang="scss">
.revice {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 50%;
}

.focused-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 400;
  line-height: 45rpx;
  color: rgba(0, 0, 0, 0.9);

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 60rpx;
    padding: 0 10rpx;

    &.isActive {
      font-weight: 700;

      &::after {
        width: 40rpx;
        height: 6rpx;
        content: '';
        background: #d1302e;
        border-radius: 8rpx;
      }
    }
  }
}

:deep() {
  .filter-btn {
    width: 300rpx !important;
    min-width: unset !important;
    height: 80rpx !important;
    padding: unset !important;
    margin: unset;
    font-size: 30rpx !important;
    font-weight: 400 !important;

    color: rgba(0, 0, 0, 0.5) !important;
    background: rgba(0, 0, 0, 0.02);
    background-color: rgba(0, 0, 0, 0.02) !important;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12rpx !important;

    &.active {
      color: #d1302e !important;
      background: rgba(209, 48, 46, 0.1) !important;
      border: 1px solid #d1302e;
    }
  }
}

.modal {
  :global(.wd-popup) {
    background-color: red;
  }
}

.wd-popup {
  background-color: red;
}

.content {
  padding: 40rpx 0 160rpx;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  box-shadow: 8rpx -8rpx 100rpx rgba(0, 0, 0, 0.2);
}

.tab {
  background-color: unset;

  :deep(.wd-tabs__nav) {
    background-color: unset;
  }
}
</style>
