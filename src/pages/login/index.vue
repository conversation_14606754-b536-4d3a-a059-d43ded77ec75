<route lang="json5" type="login">
{
  style: {
    navigationBarTitleText: '登录',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="flex flex-col justify-between items-center pt-100rpx p-x-30rpx">
    <text class="mb-32rpx text-60rpx">登录</text>
    <view class="w-full">
      <wd-form ref="formRef" :model="data" :rules="rules" class="text-center">
        <wd-input
          prop="username"
          clearable
          v-model="data.username"
          size="large"
          placeholder="请输入账号"
        />
        <wd-input
          prop="password"
          v-model="data.password"
          size="large"
          clearable
          placeholder="请输入密码"
          showPassword
        />
        <view class="submit mt-80rpx">
          <wd-button type="primary" @click="submit" size="large" custom-class="w-full">
            立即登录
          </wd-button>
        </view>
      </wd-form>
    </view>
    <!-- 底部 -->
    <view class="mt-400rpx">
      <view class="flex justify-center items-center mb-40rpx">
        <view @click="goToWxLogin">
          <image
            class="w-80rpx h-80rpx"
            src="https://sacdn.850g.com/football/static/wechatLogin.svg"
          />
        </view>
      </view>
      <view class="flex items-center text-22rpx">
        <wd-checkbox v-model="confirm" shape="square">我已阅读并同意</wd-checkbox>
        <text class="text-blue" @click="gotoUserProtocal">《用户服务协议》</text>
        和
        <text class="text-blue" @click="gotoPrivacy">《隐私政策》</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { loginByUsername } from '@/api/user'
import { useUserStore } from '@/store'
import { getWXH5LoginCode } from '@/utils/wxh5Login'
import { getMiniLoginCode } from '@/utils/miniLogin'
import { checkTabPage } from '@/utils/tabpages'

const formRef = ref()
const confirm = ref(false)
const userStore = useUserStore()

const data = ref({
  username: '',
  password: '',
})

const rules = reactive({
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
})

const submit = () => {
  if (!confirm.value) {
    uni.showModal({
      title: '用户协议与隐私政策',
      content: '请阅读并同意用户服务协议和隐私政策',
      confirmText: '同意',
      cancelText: '不同意',
      cancelColor: 'rgb(150,150,150)',
      success: (res) => {
        if (res.confirm) {
          confirm.value = true
        }
      },
    })
    return
  }
  formRef.value.validate().then(async ({ valid }) => {
    if (valid) {
      const result = await loginByUsername(data.value)
      userStore.setToken(result.accessToken.accessToken, result.accessToken.refreshToken, null)
      localStorage.setItem('isRootAccount', result.parentAccessToken)
      if (!redirect.value || redirect.value == '') {
        redirect.value = '/pages/myInfo/index'
      } else {
        // redirect.value = decodeURIComponent(redirect.value)
      }

      if (checkTabPage(redirect.value)) {
        uni.switchTab({ url: redirect.value })
      } else {
        uni.redirectTo({ url: redirect.value })
      }
    }
  })
}

function gotoUserProtocal() {
  uni.navigateTo({ url: '/pages/agreement/index' })
}

function gotoPrivacy() {
  uni.navigateTo({ url: '/pages/privacy/index' })
}

const redirect = ref('')

const goToWxLogin = async () => {
  if (!confirm.value) {
    uni.showModal({
      title: '用户协议与隐私政策',
      content: '请阅读并同意用户服务协议和隐私政策',
      confirmText: '同意',
      cancelText: '不同意',
      cancelColor: 'rgb(150,150,150)',
      success: (res) => {
        if (res.confirm) {
          confirm.value = true
        }
      },
    })
    return
  }

  const systemInfo = uni.getSystemInfoSync()
  console.log('systemInfo', systemInfo)
  //如果是小程序，则使用小程序登录
  if (systemInfo.uniPlatform === 'mp-weixin') {
    getMiniLoginCode('/pages/myInfo/index')
  } else {
    if (!redirect.value || redirect.value == '') {
      redirect.value = location.origin + '/pages/myInfo/index'
    } else if (redirect.value && !redirect.value.startsWith('http')) {
      redirect.value = location.origin + redirect.value
    }
    console.log('redirect', redirect.value)
    getWXH5LoginCode(redirect.value)
  }
}

onLoad((e) => {
  redirect.value = e.redirect || ''
})
</script>
