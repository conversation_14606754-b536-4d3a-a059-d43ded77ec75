<template>
  <template v-if="list.length > 0">
    <view
      v-for="item in list"
      :key="item.id"
      class="p-30rpx"
      style="border-bottom: 1rpx solid rgba(121, 121, 121, 0.2)"
    >
      <!-- 赛事分类 -->
      <view class="flex justify-between text-26rpx">
        <text>{{ item.categoryName }}</text>
        <!-- 当前比赛时间 -->
        <text :class="matchedTimeClazz(item.statusId)">
          {{ matchedTime(item.statusId, item.matchTime) }}
        </text>
        <text>阵容</text>
      </view>
      <!-- 比赛日期与阵容 -->
      <view class="flex justify-between items-center mt-10rpx mb-40rpx">
        <text class="text-26rpx text-opacity-50">{{ mTime(item.matchTime) }}</text>
        <wd-icon
          name="star-filled"
          color="#ff9000"
          size="40rpx"
          v-if="item.attention"
          @click="cancelAttention(item.id)"
        />
        <wd-icon
          name="star"
          color="rgba(0, 0, 0, 0.5)"
          size="40rpx"
          v-else
          @click="attention(item.id)"
        />
      </view>
      <view @click="gotoMatchDetail(item.id)">
        <!-- 主客队logo，比分 -->
        <view class="flex justify-center items-center">
          <image class="w-80rpx h-80rpx mr-20rpx" :src="item.homeTeamLogo" />
          <text class="w-120rpx truncate text-black text-opacity-90 text-30rpx">
            {{ item.homeTeamName }}
          </text>
          <text class="m-x-50rpx text-48rpx text-black text-opacity-90">
            {{ score(item.homeScore, item.awayScore) }}
          </text>
          <text class="w-120rpx truncate text-black text-opacity-90 text-30rpx">
            {{ item.awayTeamName }}
          </text>
          <image class="w-80rpx h-80rpx ml-20rpx" :src="item.awayTeamLogo" />
        </view>
        <!-- 比赛详情(主客队红黄牌，角球情况。以及半场比赛得分) -->
        <view v-if="showDetail(item.statusId)" class="flex justify-center mt-24rpx">
          <!-- 主场红牌 -->
          <view class="flex flex-col items-center">
            <view class="w-30rpx h-30rpx mb-10rpx bg-#f22" />
            <text class="text-26rpx text-black text-opacity-90">{{ item.homeRedCard || '-' }}</text>
          </view>
          <!-- 主场黄牌 -->
          <view class="flex flex-col items-center m-x-30rpx">
            <view class="w-30rpx h-30rpx mb-10rpx bg-#FFC412" />
            <text class="text-26rpx text-black text-opacity-90">
              {{ item.homeYellowCard || '-' }}
            </text>
          </view>
          <!-- 主场角球 -->
          <view class="flex flex-col items-center">
            <image class="w-30rpx h-30rpx mb-10rpx" src="/static/images/corner.png" />
            <text class="text-26rpx text-black text-opacity-90">
              {{ item.homeCornerKick || '-' }}
            </text>
          </view>
          <!-- 半场比分 -->
          <text class="m-x-78rpx text-30rpx text-black text-opacity-50">
            {{ score(item.homeHalfScore, item.awayHalfScore) }}
          </text>
          <!-- 客场红牌 -->
          <view class="flex flex-col items-center">
            <view class="w-30rpx h-30rpx mb-10rpx bg-#f22" />
            <text class="text-26rpx text-black text-opacity-90">{{ item.awayRedCard || '-' }}</text>
          </view>
          <!-- 主场黄牌 -->
          <view class="flex flex-col items-center m-x-30rpx">
            <view class="w-30rpx h-30rpx mb-10rpx bg-#FFC412" />
            <text class="text-26rpx text-black text-opacity-90">
              {{ item.awayYellowCard || '-' }}
            </text>
          </view>
          <!-- 主场角球 -->
          <view class="flex flex-col items-center">
            <image class="w-30rpx h-30rpx mb-10rpx" src="/static/images/corner.png" />
            <text class="text-26rpx text-black text-opacity-90">
              {{ item.awayCornerKick || '-' }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </template>
</template>

<script setup lang="ts">
import { IMatchItem, addMatchAttention, cancelMatchAttention } from '@/api/match'
import { MATCH_STATUS } from '@/utils/enum'
import { DATE_TIME_FORMATER_CN, format } from '@/utils/format'
import dayjs, { ConfigType } from 'dayjs'
import { isFunction } from 'wot-design-uni/components/common/util'

const { onAttention } = defineProps<{ list: IMatchItem[]; onAttention?: (id: number) => void }>()

const matchedTimeClazz = computed(() => {
  return (status: MATCH_STATUS) => {
    switch (status) {
      case MATCH_STATUS.NOT_STARTED:
        return 'text-black text-opacity-50'
      case MATCH_STATUS.EXCEPTION:
      case MATCH_STATUS.FULL_TIME:
        return ''
      default:
        return 'text-#D1302E'
    }
  }
})

const matchedTime = computed(() => {
  return (status: MATCH_STATUS, time: ConfigType) => {
    switch (status) {
      case MATCH_STATUS.NOT_STARTED:
        return '未开始'
      case MATCH_STATUS.FULL_TIME:
        return ''
      case MATCH_STATUS.EXCEPTION:
        return '比赛异常'
      default:
        return `${dayjs().diff(time, 'minute')}’`
    }
  }
})

const mTime = computed(() => {
  return (time: ConfigType) => {
    return format(time, DATE_TIME_FORMATER_CN)
  }
})

const score = computed(() => {
  return (home: number, away: number) => `${home}:${away}`
})

const showDetail = computed(() => {
  return (status: MATCH_STATUS) => {
    return ![
      MATCH_STATUS.EXCEPTION,
      MATCH_STATUS.NOT_STARTED,
      MATCH_STATUS.DELAYED,
      MATCH_STATUS.CANCELLED,
      MATCH_STATUS.PENDING,
    ].includes(status)
  }
})

async function attention(id: number) {
  try {
    const ret = await addMatchAttention(id)
    if (ret) {
      isFunction(onAttention) && onAttention(id)
    } else {
      throw new Error('关注赛事异常')
    }
  } catch {
    throw new Error('关注赛事异常')
  }
}

async function cancelAttention(id: number) {
  try {
    const ret = await cancelMatchAttention(id)
    if (ret) {
      isFunction(onAttention) && onAttention(id)
    } else {
      throw new Error('关注赛事异常')
    }
  } catch {
    throw new Error('关注赛事异常')
  }
}

function gotoMatchDetail(id: number) {
  uni.navigateTo({ url: `/pages/matchDetail/index?matchId=${id}` })
}
</script>
