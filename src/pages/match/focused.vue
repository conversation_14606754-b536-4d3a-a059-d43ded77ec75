<template>
  <view>
    <!-- 我关注的 -->
    <view
      v-if="focused.length === 0"
      class="flex flex-col items-center pt-110rpx pb-64rpx bg-#F4F8FA"
    >
      <image :src="emptyImg" class="w-488rpx h-266rpx" />
      <text class="mt-30rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
    </view>
    <!-- 我关注的列表 -->
    <competition :list="focused" :onAttention="afterCancelAttention" />
    <!-- 热门推荐(如果我关注的列表为空则显示热门推荐，否则不显示) -->
    <view v-if="focused.length === 0">
      <view
        class="p-30rpx text-black text-opacity-90 text-32rpx"
        style="border-bottom: 1px solid rgba(121, 121, 121, 0.2)"
      >
        热门推荐
      </view>
      <competition :list="recommend" :onAttention="afterAttention" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { getCurrentInstance } from 'vue'
import { isEmpty } from 'lodash-es'
import { useUserStore } from '@/store'
import { IMatchItem, getAttentionList, getRecommendList } from '@/api/match'
import competition from './components/competition.vue'
import emptyImg from '@/static/images/empty.png'

const userStore = useUserStore()

const {
  // @ts-ignore
  proxy: { $onLaunched },
} = getCurrentInstance()

const focused = ref<IMatchItem[]>([])
const pageNo = ref<number>()
const recommend = ref<IMatchItem[]>([]) // 热门推荐

function getRecommends() {
  getRecommendList().then((data) => {
    recommend.value = data
  })
}

/* 取消关注 */
function afterCancelAttention(id: number) {
  if (focused.value.length === 1) {
    focused.value = []
    getRecommends()
  } else {
    pageNo.value = pageNo.value % 10 === 0 ? pageNo.value - 1 : pageNo.value
    focused.value = focused.value.filter((item) => item.id !== id)
  }
}

/* 关注指定赛事 */
function afterAttention(id: number) {
  getAttentionList().then(({ list }) => (focused.value = list))
}

onLoad(async () => {
  if (!userStore.isLogined) {
    await $onLaunched
  }

  getAttentionList().then(({ list }) => {
    if (isEmpty(list)) {
      getRecommends()
    } else {
      pageNo.value = 1
      focused.value = list
    }
  })
})
</script>
