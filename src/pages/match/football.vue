<template>
  <view class="pb-180rpx">
    <view class="header">
      <view
        v-for="({ date, displayDate, week }, key) in dateList"
        :key="date"
        class="flex flex-col justify-center items-center h-full text-26rpx"
        :class="key === active ? 'border-0 border-b-1rpx border-#D1302E border-solid' : ''"
        @click="findMatchByDate(key)"
      >
        <text :class="key === active ? 'text-#D1302E' : 'text-black text-opacity-90'">
          {{ displayDate }}
        </text>
        <text :class="key === active ? 'text-#D1302E' : 'text-black text-opacity-50'">
          {{ week }}
        </text>
      </view>
      <image :src="moreIcon" class="w-40rpx h-40rpx" @click="showCategoryModal" />
    </view>
    <competition :list="list" :onAttention="afterAttention" />
    <wd-popup v-model="visible" position="top">
      <view class="pt-45px">
        <view class="modal-header">
          <wd-icon name="arrow-up" size="40rpx" color="#999999" @click="hideModal" />
        </view>
        <view class="flex flex-wrap gap-30rpx pt-37rpx p-x-30rpx" v-if="categories.length > 0">
          <text
            v-for="{ id, name, num } in categories"
            :key="id"
            class="w-210rpx h-80rpx rounded-xl text-30rpx text-center leading-80rpx box-border"
            :class="filterClazz(id)"
            @click="filteCategory(id)"
          >
            {{ `${name}(${num})` }}
          </text>
        </view>
        <!-- 底部按钮 -->
        <view class="flex justify-center gap-x-20rpx text-32rpx mb-33rpx mt-60rpx">
          <text
            class="flex justify-center items-center w-305rpx h-80rpx bg-black bg-opacity-5 text-black text-opacity-50 rounded-xl"
            @click="checkAll"
          >
            全选
          </text>
          <text
            class="flex justify-center items-center w-305rpx h-80rpx bg-#D1302E text-white rounded-xl"
            @click="confirm"
          >
            确定
          </text>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import dayjs, { ConfigType } from 'dayjs'
import { formatDate, formatWeek } from '@/utils/format'
import competition from './components/competition.vue'
import moreIcon from '@/static/images/more.svg'
import { IMatchItem, getCategoryNum, getMatchListByDate } from '@/api/match'
import { isEmpty } from 'lodash-es'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'

interface IDateItem {
  date: string
  week: string
  displayDate: string
}

interface ICategoryItem {
  id: number
  name: string
  num: number
}

const active = ref(1)
const dateList = ref<IDateItem[]>([])
const list = ref<IMatchItem[]>([])
const pageNo = ref(0)
const totalPage = ref(0)
const categories = ref<ICategoryItem[]>([])
const visible = ref(false)

const checked = ref<number[]>([])

const filterClazz = computed(() => {
  return (id: number) =>
    checked.value.includes(id)
      ? 'bg-#D1302E bg-opacity-10 text-#D1302E border-1rpx'
      : 'bg-black bg-opacity-5 text-black text-opacity-50'
})

function findMatchByDate(k: number) {
  if (k === active.value) return

  active.value = k
}

function createDateList() {
  const now = dayjs()
  const list: IDateItem[] = []
  let day: ConfigType = null
  let week = ''

  for (let i = -1; i < 4; i++) {
    day = now.add(i, 'day')
    switch (i) {
      case -1:
        week = '昨天'
        break
      case 0:
        week = '今天'
        break
      default:
        week = formatWeek(day)
    }
    list.push({
      date: formatDate(day),
      week,
      displayDate: day.format('MM.DD'),
    })
  }

  dateList.value = list
}

function showCategoryModal() {
  visible.value = true
}

function hideModal() {
  visible.value = false
}

function checkAll() {
  checked.value = categories.value.map(({ id }) => id)
}

function filteCategory(id: number) {
  const index = checked.value.findIndex((e) => e === id)
  if (index < 0) {
    checked.value.push(id)
  } else {
    checked.value.splice(index, 1)
  }
}

function afterAttention() {
  const currentSelectedDay = dateList.value[active.value].date
  getMatchListByDate(currentSelectedDay).then(({ list: l }) => {
    list.value = l
  })
}

onReachBottom(() => {
  if (pageNo.value === totalPage.value) {
    uni.showToast({
      title: '没有更多',
    })
    return
  }
  // const day = dateList.value[active.value].date
  const day = '2024-09-29'
  getMatchListByDate(day, pageNo.value + 1).then(({ list: l }) => {
    pageNo.value = pageNo.value + 1
    list.value = [...list.value, ...l]
  })
})

function confirm() {
  visible.value = false
}

function initData(date: string) {
  Promise.all([getMatchListByDate(date), getCategoryNum(date)]).then(([{ list: l, total }, b]) => {
    if (!isEmpty(l)) {
      list.value = l
      pageNo.value = 1
      totalPage.value = Math.ceil(total / DEFAULT_PAGE_SIZE)
    }
  })
}

onLoad(() => {
  createDateList()
  // const day = dateList.value[active.value].date
  const day = '2024-09-29'
  initData(day)
})
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 100rpx;
  background: #fff;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100rpx;
  padding: 0 30rpx;
  background: #fff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.1);
}
</style>
