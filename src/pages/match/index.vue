<route lang="json5">
{
  style: {
    navigationBarTitleText: '赛事',
  },
}
</route>
<template>
  <view>
    <wd-tabs v-model="tab">
      <block v-for="item in MATCH_TABS" :key="item">
        <wd-tab :title="item">
          <focused class="content" v-if="item === MATCH_TABS[0]" />
          <football class="" v-else-if="item === MATCH_TABS[1]" />
        </wd-tab>
      </block>
    </wd-tabs>
  </view>
</template>

<script lang="ts" setup>
import focused from './focused.vue'
import football from './football.vue'
import { MATCH_TABS } from '@/utils/constant'

const tab = ref(0)
</script>
