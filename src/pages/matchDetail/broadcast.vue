<template>
  <view class="flex flex-col gap-row-30rpx pt-30rpx pb-100rpx">
    <view
      v-for="(live, index) in lives"
      :key="`${live.type}-${index}`"
      class="flex justify-start p-x-30rpx gap-col-20rpx"
    >
      <image
        class="w-30rpx h-30rpx mt-15rpx"
        src="/static/images/goal.png"
        v-if="live.type === 1"
      />
      <image
        class="w-30rpx h-30rpx mt-15rpx"
        src="/static/images/corner.png"
        v-if="live.type === 2"
      />
      <view class="w-30rpx h-30rpx mt-15rpx bg-#FFC412" v-else-if="live.type === 3" />
      <view class="w-30rpx h-30rpx mt-15rpx bg-#F22" v-else-if="live.type === 4" />
      <image
        class="w-30rpx h-30rpx mt-15rpx"
        src="/static/images/end.png"
        v-else-if="live.type === 11 || live.type === 12"
      />
      <text
        class="flex-1 mt-9rpx text-30rpx text-black text-opacity-90"
        v-if="live.type === 11 || live.type === 12"
      >
        {{ live.data }}
      </text>
      <text class="flex-1 mt-9rpx text-30rpx text-black text-opacity-50" v-else>
        {{ live.data }}
      </text>
      <image :src="live.logo" class="flex-none w-60rpx h-60rpx ml-auto" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { IMatchDetailItem } from '@/api/match'

export interface IBroadcastItem extends IMatchDetailItem {
  logo: string
}

defineProps<{ lives: IBroadcastItem[] }>()
</script>
