<template>
  <view>
    <chat-list ref="chatRef" />
    <view class="p-x-30rpx">
      <wd-input v-model="msg" placeholder="请输入聊天内容">
        <template #suffix>
          <image src="/static/images/plane.png" class="w-40rpx h-40rpx" @click="send" />
        </template>
      </wd-input>
    </view>
  </view>
</template>

<script setup lang="ts">
import { sendMatchChat } from '@/api/match'
import chatList from './components/chatList.vue'

const chatRef = ref<typeof chatList>()
const matchId = ref<number>()
const msg = ref('')
async function send() {
  // 暂停消息获取

  // 发送消息
  try {
    chatRef.value && chatRef.value.pause()
    await sendMatchChat(matchId.value, msg.value)
    msg.value = ''
  } finally {
    chatRef.value && chatRef.value.resume()
    // 恢复消息获取
  }
}

onLoad(({ matchId: mId }) => {
  matchId.value = parseInt(mId)
})
</script>
