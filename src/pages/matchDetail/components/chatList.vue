<template>
  <view class="flex flex-col gap-row-30rpx min-h-500rpx p-30rpx">
    <view v-for="chat in data" :key="chat.id" class="flex text-30rpx">
      <text class="text-black text-opacity-90">{{ chat.nickname }}：</text>
      <text class="text-black text-op-50">{{ chat.msg }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { IChatItem, getMatchChatList } from '@/api/match'
import { useIntervalFn } from '@vueuse/core'
import { isEmpty } from 'lodash-es'

const matchId = ref<number>()
const id = ref<number | null>(null)
const data = ref<IChatItem[]>([])

async function getMatchChatListFn() {
  if (!matchId.value) return
  getMatchChatList(matchId.value, id.value).then((res) => {
    if (!isEmpty(res)) data.value = res
  })
}

const { pause, resume } = useIntervalFn(getMatchChatListFn, 3000, { immediateCallback: true })

defineExpose({ pause, resume })

onShow(resume)
onHide(pause)

onLoad(({ matchId: mid }) => {
  matchId.value = parseInt(mid)
})
</script>
