<template>
  <view class="mb-20rpx">
    <!-- 标题 -->
    <view class="text-black text-26rpx leading-36rpx text-center">{{ title }}</view>
    <!-- 进度条 -->
    <view class="flex justify-between items-center mx-50rpx mt-8rpx">
      <text class="text-black text-opacity-90 text-24rpx leading-33rpx">{{ home }}</text>
      <view class="flex h-8rpx w-560rpx">
        <view :class="homeProgressClazz" :style="homePercentage" />
        <view :class="awayProgressClazz" />
      </view>
      <text class="text-black text-opacity-90 text-24rpx leading-33rpx">{{ away }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps<{ title: string; home: number; away: number }>()

const homePercentage = computed(() => {
  const { home, away } = props
  let percentage = home / (home + away)
  percentage = Math.round(percentage * 100)
  return `width: ${percentage}%`
})

const homeProgressClazz = computed(() =>
  props.home < props.away ? 'h-full bg-#ff5050 bg-opacity-30' : 'h-full bg-#ff5050',
)

const awayProgressClazz = computed(() =>
  props.away <= props.home ? 'flex-1 bg-#ff5050 bg-opacity-30' : 'flex-1 bg-#ff5050',
)
</script>
