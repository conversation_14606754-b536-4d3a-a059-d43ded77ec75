<route lang="json5">
{
  style: {
    navigationBarTitleText: '赛事详情',
  },
}
</route>
<template>
  <view class="bg-opacity-0" :class="fromThird ? 'pb-120px' : ''">
    <template v-if="inProcess">
      <view class="h-420rpx">
        <iframe :src="matchVideo" class="w-full h-full" />
      </view>
    </template>
    <view class="header" v-else>
      <template v-if="matchInfo">
        <!-- 比赛时间 -->
        <view class="text-28rpx text-white leading-39rpx text-center">
          {{ matchDatetime(matchInfo.matchTime) }}
        </view>
        <view class="flex justify-between w-full px-80rpx mt-10rpx mb-17rpx text-white box-border">
          <!-- 左 -->
          <view class="flex flex-1 flex-col items-center pt-32rpx">
            <image :src="matchInfo.homeTeamLogo" class="w-120rpx h-120rpx" />
            <text class="mt-20rpx text-30rpx leading-42rpx">{{ matchInfo.homeTeamName }}</text>
          </view>
          <!-- 中 -->
          <view class="flex flex-none flex-col items-center flex-1 mx-60rpx truncate">
            <text class="text-30rpx leading-42rpx text-center w-full truncate">
              {{ matchInfo.roundName }}
            </text>
            <text
              class="mt-40rpx text-56rpx leading-78rpx"
              v-if="matchInfo.statusId > 1 && matchInfo.statusId < 9"
            >
              {{ liveScore }}
            </text>
            <text class="mt-40rpx text-56rpx leading-78rpx" v-else>VS</text>
          </view>
          <!-- 右 -->
          <view class="flex flex-1 flex-col items-center pt-32rpx">
            <image :src="matchInfo.awayTeamLogo" class="w-120rpx h-120rpx" />
            <text class="mt-20rpx text-30rpx leading-42rpx">{{ matchInfo.awayTeamName }}</text>
          </view>
        </view>
        <!-- 底部 -->
        <view class="flex justify-center gap-x-10rpx text-white">
          <text
            class="flex justify-center items-center w-130rpx h-50rpx rounded-md bg-black bg-opacity-40 text-26rpx"
          >
            {{ matchInfo.statusDesc }}
          </text>
          <template v-if="matchInfo.statusId === 1 && !fromThird">
            <!-- 已关注 -->
            <view
              v-if="!!matchInfo.attention"
              class="flex justify-center items-center gap-x-10rpx w-130rpx h-50rpx rounded-md bg-black bg-opacity-40 text-white text-26rpx"
              @click="handleNotification(false)"
            >
              <wd-icon name="notification-filled" size="30rpx" color="#ED8702" />
              <text class="text-26rpx text-#ED8702">提醒</text>
            </view>
            <!-- 未关注 -->
            <view
              v-else
              class="flex justify-center items-center gap-x-10rpx w-130rpx h-50rpx rounded-md bg-black bg-opacity-40 text-26rpx"
              @click="handleNotification(true)"
            >
              <wd-icon name="notification-filled" size="30rpx" />
              <text class="text-26rpx">提醒</text>
            </view>
          </template>
        </view>
      </template>
    </view>
    <view class="mt-[-10rpx] pt-10rpx rounded-t-lg bg-white">
      <!-- tab -->
      <view
        class="flex items-center h-80rpx pl-40rpx border-b-solid border-b-1rpx border-b-black border-b-opacity-10"
      >
        <text
          :class="activeClazz(MATCH_DETAIL_TYPE.STATUS)"
          @click="changeTab(MATCH_DETAIL_TYPE.STATUS)"
        >
          赛况
        </text>
        <text
          :class="activeClazz(MATCH_DETAIL_TYPE.LINEUP)"
          @click="changeTab(MATCH_DETAIL_TYPE.LINEUP)"
          class="ml-40rpx"
          v-if="matchInfo.hasFormation"
        >
          阵容
        </text>
        <text
          :class="activeClazz(MATCH_DETAIL_TYPE.SCHEME)"
          @click="changeTab(MATCH_DETAIL_TYPE.SCHEME)"
          class="ml-40rpx"
          v-if="matchInfo.hasArticle"
        >
          方案
        </text>
      </view>
      <!-- 正文 -->
      <situations :match="matchInfo" v-if="tab === MATCH_DETAIL_TYPE.STATUS" />
      <template v-if="matchInfo.hasFormation && tab === MATCH_DETAIL_TYPE.LINEUP">
        <lineup :match-id="id" :match="matchInfo" />
      </template>
      <template v-if="matchInfo.hasArticle && tab === MATCH_DETAIL_TYPE.SCHEME">
        <scheme :match-id="id" />
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import situations from './situations.vue'
import lineup from './lineup.vue'
import scheme from './scheme.vue'
import {
  IMatchItem,
  addMatchAttention,
  cancelMatchAttention,
  getMatchDetailById,
} from '@/api/match'
import { MATCH_DETAIL_TYPE, MATCH_STATUS } from '@/utils/enum'
import { formatDataTime } from '@/utils/format'
import { useIntervalFn } from '@vueuse/core'

const tab = ref<MATCH_DETAIL_TYPE>(MATCH_DETAIL_TYPE.STATUS)
const id = ref(-1)
const matchInfo = ref<IMatchItem>()
const fromThird = ref(false)

const activeClazz = computed(() => {
  return (type: MATCH_DETAIL_TYPE) => {
    if (type === tab.value) {
      return "flex items-center relative h-full after:absolute after:block after:content-[''] after:w-full  after:h-6rpx after:bg-#D1302E after:bottom-0 text-30rpx text-opacity-90"
    }

    return 'text-28rpx text-black text-opacity-50'
  }
})

const matchDatetime = computed(() => {
  return (datetime: number) => formatDataTime(datetime * 1000)
})

const liveScore = computed(() => `${matchInfo.value.homeScore}:${matchInfo.value.awayScore}`)

const inProcess = computed(() => {
  if (!matchInfo.value) return false
  return [
    MATCH_STATUS.FIRST_HALF, // 上半场
    MATCH_STATUS.HALF_TIME, // 中场
    MATCH_STATUS.SECOND_HALF, // 下半场
    MATCH_STATUS.EXTRA_TIME, // 加时赛
    MATCH_STATUS.EXTRA_TIME_DEPRECATED, // 加时赛(弃用)
    MATCH_STATUS.PENALTY_SHOOTOUT, // 点球决战
  ].includes(matchInfo.value.statusId)
})

const matchVideo = computed(() =>
  matchInfo.value
    ? `https://widgets-livetracker.nami.com/zh/football?profile=3ry7uo4x1h7ixre&id=${matchInfo.value.id}`
    : '',
)

function changeTab(t: MATCH_DETAIL_TYPE) {
  if (tab.value !== t) tab.value = t
}

/*
 * 关注或取消关注比赛 为true则设置关注，false则取消关注
 */
async function handleNotification(notification: boolean) {
  if (id.value < 0) return
  uni.showLoading()
  pause()
  if (notification) {
    await addMatchAttention(id.value)
  } else {
    await cancelMatchAttention(id.value)
  }
  resume()
  uni.hideLoading()
  uni.showToast({
    title: notification ? '接收提醒成功' : '取消提醒成功',
    icon: 'none',
  })
}

async function fetchMatchData() {
  if (id.value > 0) {
    const match = await getMatchDetailById(id.value)
    matchInfo.value = match
  }
}

const { resume, pause } = useIntervalFn(fetchMatchData, 10000, {
  immediate: true,
  immediateCallback: true,
})

onLoad(({ matchId, from }) => {
  if (from === 'third') fromThird.value = true
  id.value = parseInt(matchId)
  resume()
})

onUnload(pause)
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 420rpx;
  padding: 40rpx 0;
  background-image: url('https://sacdn.850g.com/football/static/match-top_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
