<template>
  <view>
    <template v-if="!!lineup">
      <!-- 阵容图表 -->
      <view
        class="mt-30rpx after:content-[''] after:block after:h-20rpx after:mt-30rpx after:bg-#F4F8FA"
      >
        <view class="lineup text-white text-20rpx leading-42rpx">
          <!-- 教练，裁判 -->
          <template v-if="!!match">
            <!-- top 主队名称，阵容与裁判信息 -->
            <view class="flex justify-between items-start">
              <view class="flex flex-col">
                <text>{{ match.homeTeamName }}</text>
                <text>{{ match.homeFormation }}</text>
              </view>
              <view v-if="!!match.refereeName" class="flex items-center gap-x-5rpx">
                <image
                  src="https://sacdn.850g.com/football/static/icons/match/coach.svg"
                  class="w-24rpx h-24rpx"
                />
                <text>{{ match.refereeName }}</text>
              </view>
            </view>
            <!-- 客队名称与阵容 -->
            <view class="flex flex-col">
              <text>{{ match.awayTeamName }}</text>
              <text>{{ match.awayFormation }}</text>
            </view>
          </template>
          <!-- 首发阵容 -->
          <!-- 主队阵容 -->
          <template v-if="!isEmpty(lineup.first.home)">
            <view
              v-for="{ id, name, shirtNumber, captain, incidents, x, y } in lineup.first.home"
              :key="id"
              class="lineup-pos bg-#F34B4A"
              :style="`top: ${y}%; left: ${x}%;`"
            >
              <text class="text-white text-24rpx">{{ shirtNumber }}</text>
              <text class="lineup-pos__name ellipsis">{{ name }}</text>
              <!-- 队长标志 -->
              <image
                v-if="!!captain"
                src="https://sacdn.850g.com/football/static/icons/match/captain.svg"
                class="lineup-pos__captain"
              />
              <!-- 换人与红黄牌及两黄变红 -->
              <view v-if="incidents && !isEmpty(incidents.cards)" class="lineup-pos__cards">
                <template v-for="{ type, time } in incidents.cards" :key="time">
                  <!-- 换人 -->
                  <view
                    v-if="type === LIVE_INFO_TYPE.SUBSTITUTION"
                    class="flex items-center gap-x-4rpx"
                  >
                    <image
                      src="https://sacdn.850g.com/football/static/icons/match/substitution-down.svg"
                      class="w-24rpx h-24rpx"
                    />
                    <text class="text-white text-22rpx leading-22rpx">{{ time }}’</text>
                  </view>
                  <!-- 红牌 -->
                  <image
                    src="https://sacdn.850g.com/football/static/icons/match/red-card.svg"
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.RED_CARD"
                  />
                  <!-- 黄牌 -->
                  <image
                    src="https://sacdn.850g.com/football/static/icons/match/yellow-card.svg"
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.YELLOW_CARD"
                  />
                  <!-- 两黄变红 -->
                  <image
                    src="https://sacdn.850g.com/football/static/icons/match/two-yellow.svg"
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.TWO_YELLOWS_TURN_RED"
                  />
                </template>
              </view>
              <!-- 进球(普通进球，点球，乌龙) -->
              <view v-if="incidents && !isEmpty(incidents.scores)" class="lineup-pos__scores">
                <template v-for="{ type, time } in incidents.scores" :key="time">
                  <!-- 普通进球 -->
                  <image
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.GOAL"
                    src="https://sacdn.850g.com/football/static/icons/match/goal.svg"
                  />
                  <!-- 点球 -->
                  <image
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.PENALTY_KICK"
                    src="https://sacdn.850g.com/football/static/icons/match/penalty-kick.svg"
                  />
                  <!-- 乌龙球 -->
                  <image
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.OWN_GOAL"
                    src="https://sacdn.850g.com/football/static/icons/match/own-goal.svg"
                  />
                </template>
              </view>
            </view>
          </template>
          <!-- 客队阵容 -->
          <template v-if="!isEmpty(lineup.first.away)">
            <view
              v-for="{ id, name, shirtNumber, captain, incidents, x, y } in lineup.first.away"
              :key="id"
              class="lineup-pos bg-#747ECE"
              :style="`top: ${y}%; left: ${x}%;`"
            >
              <text class="text-white text-24rpx">{{ shirtNumber }}</text>
              <text class="lineup-pos__name ellipsis">{{ name }}</text>
              <!-- 队长标志 -->
              <image
                v-if="!!captain"
                src="https://sacdn.850g.com/football/static/icons/match/captain.svg"
                class="lineup-pos__captain"
              />
              <!-- 换人与红黄牌及两黄变红 -->
              <view v-if="incidents && !isEmpty(incidents.cards)" class="lineup-pos__cards">
                <template v-for="{ type, time } in incidents.cards" :key="time">
                  <!-- 换人 -->
                  <view
                    v-if="type === LIVE_INFO_TYPE.SUBSTITUTION"
                    class="flex items-center gap-x-4rpx"
                  >
                    <image
                      src="https://sacdn.850g.com/football/static/icons/match/substitution-down.svg"
                      class="w-24rpx h-24rpx"
                    />
                    <text class="text-white text-22rpx leading-22rpx">{{ time }}’</text>
                  </view>
                  <!-- 红牌 -->
                  <image
                    src="https://sacdn.850g.com/football/static/icons/match/red-card.svg"
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.RED_CARD"
                  />
                  <!-- 黄牌 -->
                  <image
                    src="https://sacdn.850g.com/football/static/icons/match/yellow-card.svg"
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.YELLOW_CARD"
                  />
                  <!-- 两黄变红 -->
                  <image
                    src="https://sacdn.850g.com/football/static/icons/match/two-yellow.svg"
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.TWO_YELLOWS_TURN_RED"
                  />
                </template>
              </view>
              <!-- 进球(普通进球，点球，乌龙) -->
              <view v-if="incidents && !isEmpty(incidents.scores)" class="lineup-pos__scores">
                <template v-for="{ type, time } in incidents.scores" :key="time">
                  <!-- 普通进球 -->
                  <image
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.GOAL"
                    src="https://sacdn.850g.com/football/static/icons/match/goal.svg"
                  />
                  <!-- 点球 -->
                  <image
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.PENALTY_KICK"
                    src="https://sacdn.850g.com/football/static/icons/match/penalty-kick.svg"
                  />
                  <!-- 乌龙球 -->
                  <image
                    class="w-24rpx h-24rpx"
                    v-if="type === LIVE_INFO_TYPE.OWN_GOAL"
                    src="https://sacdn.850g.com/football/static/icons/match/own-goal.svg"
                  />
                </template>
              </view>
            </view>
          </template>
        </view>
      </view>
      <!-- 替补阵容 -->
      <view class="flex flex-col pb-150rpx">
        <text
          class="pt-30rpx pb-20rpx pl-30rpx text-30rpx text-black text-opacity-90 leading-42rpx"
        >
          替补阵容
        </text>
        <!-- 队伍名称，队标 -->
        <view class="flex">
          <view
            class="flex-1 flex items-center h-110rpx pl-30rpx border-solid border-1rpx border-black border-opacity-10"
          >
            <image :src="match.homeTeamLogo" class="w-50rpx h-50rpx" />
            <text class="ml-20rpx text-26rpx text-black text-opacity-90">
              {{ match.homeTeamName }}
            </text>
          </view>
          <view
            class="flex-1 flex items-center h-110rpx pl-30rpx border-solid border-1rpx border-black border-opacity-10"
          >
            <image :src="match.awayTeamLogo" class="w-50rpx h-50rpx" />
            <text class="ml-20rpx text-26rpx text-black text-opacity-90">
              {{ match.awayTeamName }}
            </text>
          </view>
        </view>
        <!-- 主客队教练信息 -->
        <view class="flex">
          <view
            class="flex-1 flex items-center min-w-0 h-110rpx pl-30rpx border-solid border-1rpx border-black border-opacity-10"
          >
            <image :src="match.homeCoachLogo" class="w-50rpx h-50rpx" v-if="match.homeCoachLogo" />
            <image
              src="https://sacdn.850g.com/football/static/icons/default-avatar.svg"
              class="w-50rpx h-50rpx"
              v-else
            />
            <view
              class="flex flex-col flex-1 justify-between min-w-0 h-full ml-20rpx pt-20rpx pb-15rpx box-border"
            >
              <text class="text-26rpx text-black text-opacity-90 leading-36rpx">
                {{ match.homeCoachName }}
              </text>
              <text class="text-black text-opacity-50 text-24rpx leading-34rpx">主教练</text>
            </view>
          </view>
          <view
            class="flex-1 flex items-center min-w-0 h-110rpx pl-30rpx border-solid border-1rpx border-black border-opacity-10"
          >
            <image :src="match.awayCoachLogo" class="w-50rpx h-50rpx" v-if="match.awayCoachLogo" />
            <image
              src="https://sacdn.850g.com/football/static/icons/default-avatar.svg"
              class="w-50rpx h-50rpx"
              v-else
            />
            <view
              class="flex flex-col flex-1 justify-between min-w-0 h-full ml-20rpx pt-20rpx pb-15rpx box-border"
            >
              <text class="text-26rpx text-black text-opacity-90 leading-36rpx truncate">
                {{ match.awayCoachName }}
              </text>
              <text class="text-black text-opacity-50 text-24rpx leading-34rpx">主教练</text>
            </view>
          </view>
        </view>
        <!-- 替补队员列表 -->
        <view
          v-for="(_, i) in Math.max(lineup.substitute.home.length, lineup.substitute.away.length)"
          :key="i"
          class="flex"
        >
          <view
            class="w-1/2 flex justify-between items-center h-110rpx px-20rpx border-solid border-1rpx border-black border-opacity-10 box-border"
          >
            <template v-if="lineup.substitute.home.length > i">
              <text
                class="relative flex justify-center items-center w-50rpx h-50rpx bg-#F34B4A rounded-full text-white text-24rpx"
              >
                {{ lineup.substitute.home[i].shirtNumber }}
              </text>
              <text class="flex-1 ml-20rpx mr-auto text-black text-opacity-90 text-26rpx truncate">
                {{ lineup.substitute.home[i].name }}
              </text>
              <view
                v-if="!!lineup.substitute.home[i].incidents"
                class="flex flex-col justify-around items-end h-full"
              >
                <!-- 换人 -->
                <view
                  v-if="!isEmpty(lineup.substitute.home[i].incidents.substitution)"
                  class="flex items-center"
                >
                  <image
                    src="https://sacdn.850g.com/football/static/icons/match/substitution-up.svg"
                    class="w-24rpx h-24rpx"
                  />
                  <text class="text-black text-opacity-50 text-26rpx leading-36rpx">
                    {{ lineup.substitute.home[i].incidents.substitution[0].time }}’
                  </text>
                </view>
                <!-- 进球(普通进球，点球进球，乌龙球) -->
                <view
                  v-if="!isEmpty(lineup.substitute.home[i].incidents.scores)"
                  class="flex gap-x-10rpx"
                >
                  <template
                    v-for="{ type, time } in lineup.substitute.home[i].incidents.scores"
                    :key="time"
                  >
                    <!-- 普通进球 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/goal.svg"
                      v-if="type === LIVE_INFO_TYPE.GOAL"
                    />
                    <!-- 点球进球 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/penalty-kick.svg"
                      v-else-if="type === LIVE_INFO_TYPE.PENALTY_KICK"
                    />
                    <!-- 乌龙球 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/own-goal.svg.svg"
                      v-else-if="type === LIVE_INFO_TYPE.OWN_GOAL"
                    />
                  </template>
                </view>
                <!-- 红黄牌及两黄变红 -->
                <view
                  v-if="!isEmpty(lineup.substitute.home[i].incidents.cards)"
                  class="flex gap-x-10rpx"
                >
                  <template
                    v-for="{ type, time } in lineup.substitute.home[i].incidents.cards"
                    :key="time"
                  >
                    <!-- 黄牌 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/yellow-card.svg"
                      v-if="type === LIVE_INFO_TYPE.YELLOW_CARD"
                    />
                    <!-- 红牌 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/red-card.svg"
                      v-else-if="type === LIVE_INFO_TYPE.RED_CARD"
                    />
                    <!-- 两黄变红 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/two-yellow.svg"
                      v-else-if="type === LIVE_INFO_TYPE.TWO_YELLOWS_TURN_RED"
                    />
                  </template>
                </view>
              </view>
            </template>
          </view>
          <view
            class="w-1/2 flex justify-between items-center h-110rpx px-20rpx border-solid border-1rpx border-black border-opacity-10 box-border"
          >
            <template v-if="lineup.substitute.away.length > i">
              <text
                class="relative flex justify-center items-center w-50rpx h-50rpx bg-#747ECE rounded-full text-white text-24rpx"
              >
                {{ lineup.substitute.away[i].shirtNumber }}
              </text>
              <text class="flex-1 ml-20rpx mr-auto text-black text-opacity-90 text-26rpx truncate">
                {{ lineup.substitute.away[i].name }}
              </text>
              <view
                v-if="!!lineup.substitute.away[i].incidents"
                class="flex flex-col justify-around items-end h-full"
              >
                <!-- 换人 -->
                <view
                  v-if="!isEmpty(lineup.substitute.away[i].incidents.substitution)"
                  class="flex items-center"
                >
                  <image
                    src="https://sacdn.850g.com/football/static/icons/match/substitution-up.svg"
                    class="w-24rpx h-24rpx"
                  />
                  <text class="text-black text-opacity-50 text-26rpx leading-36rpx">
                    {{ lineup.substitute.away[i].incidents.substitution[0].time }}’
                  </text>
                </view>
                <!-- 进球(普通进球，点球进球，乌龙球) -->
                <view
                  v-if="!isEmpty(lineup.substitute.away[i].incidents.scores)"
                  class="flex gap-x-10rpx"
                >
                  <template
                    v-for="{ type, time } in lineup.substitute.away[i].incidents.scores"
                    :key="time"
                  >
                    <!-- 普通进球 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/goal.svg"
                      v-if="type === LIVE_INFO_TYPE.GOAL"
                    />
                    <!-- 点球进球 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/penalty-kick.svg"
                      v-else-if="type === LIVE_INFO_TYPE.PENALTY_KICK"
                    />
                    <!-- 乌龙球 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/own-goal.svg.svg"
                      v-else-if="type === LIVE_INFO_TYPE.OWN_GOAL"
                    />
                  </template>
                </view>
                <!-- 红黄牌及两黄变红 -->
                <view
                  v-if="!isEmpty(lineup.substitute.away[i].incidents.cards)"
                  class="flex gap-x-10rpx"
                >
                  <template
                    v-for="{ type, time } in lineup.substitute.away[i].incidents.cards"
                    :key="time"
                  >
                    <!-- 黄牌 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/yellow-card.svg"
                      v-if="type === LIVE_INFO_TYPE.YELLOW_CARD"
                    />
                    <!-- 红牌 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/red-card.svg"
                      v-else-if="type === LIVE_INFO_TYPE.RED_CARD"
                    />
                    <!-- 两黄变红 -->
                    <image
                      class="w-24rpx h-24rpx"
                      src="https://sacdn.850g.com/football/static/icons/match/two-yellow.svg"
                      v-else-if="type === LIVE_INFO_TYPE.TWO_YELLOWS_TURN_RED"
                    />
                  </template>
                </view>
              </view>
            </template>
          </view>
        </view>
      </view>
    </template>
    <template v-else>
      <view class="flex flex-col items-center mt-100rpx">
        <image
          src="https://sacdn.850g.com/football/static/icons/no-match.svg"
          mode="scaleToFill"
          class="w-180rpx h-180rpx"
        />
        <text class="text-28rpx leading-40rpx text-black text-opacity-50">暂无阵容信息</text>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { IIncident, ILineup, IIncidentData, IMatchItem, getLineupById } from '@/api/match'
import { LIVE_INFO_TYPE } from '@/utils/enum'
import { isEmpty } from 'lodash-es'

const props = defineProps<{ matchId: number; match: IMatchItem }>()

const lineup = ref<ILineup>()

onMounted(async () => {
  const lineupList = await getLineupById(props.matchId)
  if (!isEmpty(lineupList)) {
    lineup.value = lineupList.reduce(
      (prev, l) => {
        const { first, lineupType, incidents, x, y } = l
        const i: IIncident[] = JSON.parse(incidents)
        // 收集换人，红黄牌(右上角),进球(右下角),队长(左下角)
        let _incidents: IIncidentData
        if (i && first === 1) {
          // 首发
          _incidents = i.reduce(
            (prev, c) => {
              if (c.type === LIVE_INFO_TYPE.SUBSTITUTION) {
                // 换人 (换人事件放在最前面)
                prev.cards = [c, ...prev.cards]
              } else if (
                [
                  LIVE_INFO_TYPE.RED_CARD,
                  LIVE_INFO_TYPE.YELLOW_CARD,
                  LIVE_INFO_TYPE.TWO_YELLOWS_TURN_RED,
                ].includes(c.type)
              ) {
                // 红黄牌 或 两黄变红
                prev.cards = [...prev.cards, c]
              } else if (
                [
                  LIVE_INFO_TYPE.GOAL,
                  LIVE_INFO_TYPE.PENALTY_KICK,
                  LIVE_INFO_TYPE.OWN_GOAL,
                ].includes(c.type)
              ) {
                // 进球  点球 或乌龙球
                prev.scores = [...prev.scores, c]
              }

              return prev
            },
            { cards: [], scores: [] },
          )
        } else if (i && first === 0) {
          // 替补
          _incidents = i.reduce(
            (prev, c) => {
              if (c.type === LIVE_INFO_TYPE.SUBSTITUTION) {
                // 换人
                prev.substitution = [c]
              } else if (
                [
                  LIVE_INFO_TYPE.RED_CARD,
                  LIVE_INFO_TYPE.YELLOW_CARD,
                  LIVE_INFO_TYPE.TWO_YELLOWS_TURN_RED,
                ].includes(c.type)
              ) {
                // 红黄牌 或 两黄变红
                prev.cards = [...prev.cards, c]
              } else if (
                [
                  LIVE_INFO_TYPE.GOAL,
                  LIVE_INFO_TYPE.PENALTY_KICK,
                  LIVE_INFO_TYPE.OWN_GOAL,
                ].includes(c.type)
              ) {
                // 进球  点球 或乌龙球
                prev.scores = [...prev.scores, c]
              }

              return prev
            },
            { cards: [], scores: [], substitution: [] },
          )
        }

        _incidents = i ? _incidents : null

        if (first === 1 && lineupType === 1) {
          // 首发主队
          prev.first.home = [...prev.first.home, { ...l, incidents: _incidents, y: y / 2 }]
        } else if (first === 1 && lineupType === 2) {
          // 首发客队
          prev.first.away = [
            ...prev.first.away,
            { ...l, incidents: _incidents, x: 100 - x, y: 100 - y / 2 },
          ]
        } else if (first === 0 && lineupType === 1) {
          // 替补主队
          prev.substitute.home = [...prev.substitute.home, { ...l, incidents: _incidents }]
        } else if (first === 0 && lineupType === 2) {
          // 替补客队
          prev.substitute.away = [...prev.substitute.away, { ...l, incidents: _incidents }]
        }
        return prev
      },
      {
        first: {
          home: [],
          away: [],
        },
        substitute: {
          home: [],
          away: [],
        },
      },
    )
  }
})
</script>

<style lang="scss" scoped>
.lineup {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 690rpx;
  height: 1160rpx;
  padding: 30rpx;
  margin: 0 auto;
  background-image: url('https://sacdn.850g.com/football/static/lineup_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-pos {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50rpx;
    height: 50rpx;
    border: 2rpx solid #fff;
    border-radius: 50%;
    transform: translate(-50%, -50%);

    &__name {
      position: absolute;
      bottom: -7rpx;
      width: 100rpx;
      font-size: 20rpx;
      line-height: 28rpx;
      color: #fff;
      text-align: center;
      transform: translate(-25%, 100%);
    }

    &__captain {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 24rpx;
      height: 24rpx;
      transform: translateX(-50%);
    }

    &__cards {
      position: absolute;
      top: -6rpx;
      right: 12rpx;
      display: flex;
      column-gap: 5rpx;
      transform: translateX(100%);
    }

    &__scores {
      position: absolute;
      right: 12rpx;
      bottom: -6rpx;
      display: flex;
      column-gap: 5rpx;
      transform: translateX(100%);
    }
  }
}
</style>
