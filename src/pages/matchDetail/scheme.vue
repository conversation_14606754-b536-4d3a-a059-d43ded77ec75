<!-- 方案 -->
<template>
  <view class="mt-30rpx">
    <template v-if="isEmpty(schemes)">
      <view class="flex flex-col items-center mt-100rpx">
        <image
          src="https://sacdn.850g.com/football/static/icons/no-match.svg"
          mode="scaleToFill"
          class="w-180rpx h-180rpx"
        />
        <text class="text-28rpx leading-40rpx text-black text-opacity-50">暂无方案信息</text>
      </view>
    </template>
    <template v-else>
      <view>
        <view
          @click="gotoArticleDetail(id)"
          v-for="{
            id,
            avatarUrl,
            authorName,
            hitRate,
            winCount,
            recentWinCount,
            title,
            createTime,
            buyCount,
            price,
          } in schemes"
          :key="id"
          class="flex flex-col p-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
        >
          <!-- top(用户信息，准确率等) -->
          <view class="flex justify-between items-center">
            <!-- 用户头像 -->
            <template v-if="avatarUrl">
              <image :src="avatarUrl" class="w-80rpx h-80rpx rounded-full" />
            </template>
            <template v-else>
              <wd-icon size="80rpx" name="user" />
            </template>
            <!-- 用户信息 -->
            <view class="flex ml-20rpx mr-auto">
              <text class="mr-20rpx text-28rpx text-black text-opacity-90">{{ authorName }}</text>
              <!-- 连红 -->
              <view
                v-if="winCount > 1 && hitRate >= 50"
                class="flex items-center h-40rpx mr-10rpx border-1rpx border-solid border-#D1302E rounded-12rpx"
              >
                <text
                  class="flex justify-center items-center h-full px-11rpx text-28rpx text-white bg-#D1302E rounded-12rpx"
                >
                  {{ winCount }}
                </text>
                <text class="ml-5rpx mr-7rpx text-24rpx text-#D1302E">连红</text>
              </view>
              <!-- 进几中几 -->
              <text
                v-if="recentWinCount"
                class="flex justify-center items-center h-40rpx px-11rpx text-24rpx text-#D1302E border-1rpx border-solid border-#D1302E rounded-12rpx"
              >
                {{ recentWinCount }}
              </text>
            </view>
            <!-- 准确率 -->
            <view class="flex flex-col justify-between text-#D1302E">
              <text class="text-40rpx">{{ `${hitRate || 0}%` }}</text>
              <text class="text-24rpx">准确率</text>
            </view>
          </view>
          <!-- middle(方案内容) -->
          <text class="mt-20rpx mb-4rpx text-30rpx text-black text-opacity-90 leading-42rpx">
            {{ title }}
          </text>
          <!-- bottom(发布时间，购买人数，价格等等) -->
          <view class="flex justify-between items-center">
            <view class="flex gap-x-30rpx text-26rpx text-black text-opacity-50">
              <text>{{ publishTime(createTime) }}</text>
              <text>{{ `${buyCount ? buyCount + 20 : 0}人购买` }}</text>
            </view>
            <text class="text-30rpx text-#D1302E">{{ price }}鱼币</text>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { IArticleScheme, getArticlesByMatchId } from '@/api/article'
import { getLastPage } from '@/utils'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
import { fromNow } from '@/utils/relative'
import { isEmpty } from 'lodash-es'

const props = defineProps<{ matchId: number }>()

const fromThird = ref(false)
const schemes = ref<IArticleScheme[]>([])

const pageNo = ref(0)
const total = ref(0)

// const articleInfo = computed(() => {
//   return (tme: number, count: number) => { }
// })
const publishTime = computed(() => {
  return (time: number) => fromNow(time)
})

function gotoArticleDetail(id: number) {
  const { route } = getLastPage()
  if (route.includes('third') || fromThird.value) {
    uni.navigateTo({ url: '/pages/official/index' })
  } else {
    uni.navigateTo({ url: `/pages/detail/index?id=${id}` })
  }
}

onMounted(async () => {
  if (props.matchId) {
    const schemeList = await getArticlesByMatchId(
      props.matchId,
      pageNo.value + 1,
      DEFAULT_PAGE_SIZE,
    )
    console.log('list', schemeList.list)
    schemes.value = schemeList.list
    total.value = schemeList.total
    pageNo.value = pageNo.value + 1
  }
})

onLoad(({ from }) => {
  if (from === 'third') fromThird.value = true
})
</script>
