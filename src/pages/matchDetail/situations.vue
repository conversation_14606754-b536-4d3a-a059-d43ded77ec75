<template>
  <view v-if="!!match" class="pt-20rpx">
    <!-- 对战信息 -->
    <view
      class="flex justify-between items-center h-80rpx px-30rpx"
      style="background-color: rgba(0, 0, 0, 0.02)"
    >
      <!-- 主队 -->
      <view class="flex justify-end items-center flex-1 h-full">
        <text class="flex-1 text-28rpx leading-40rpx text-black text-opacity-90 text-end">
          {{ match.homeTeamName }}
        </text>
        <image :src="match.homeTeamLogo" mode="scaleToFill" class="w-60rpx h-60rpx ml-20rpx" />
      </view>
      <!-- VS -->
      <image src="https://sacdn.850g.com/football/static/vs.png" class="w-56rpx h-56rpx mx-50rpx" />
      <!-- 客队 -->
      <view class="flex justify-start items-center flex-1 h-full">
        <image :src="match.awayTeamLogo" mode="scaleToFill" class="w-60rpx h-60rpx" />
        <text
          class="flex-1 ml-20rpx text-28rpx leading-40rpx text-black text-opacity-90 text-start"
        >
          {{ match.awayTeamName }}
        </text>
      </view>
    </view>
    <!-- 统计信息 -->
    <view
      class="pt-30rpx after:content-[''] after:block after:h-20rpx after:mt-14rpx after:bg-#F4F8FA"
    >
      <!-- 进球 -->
      <match-progress title="进球" :home="match.homeScore || 0" :away="match.awayScore || 0" />
      <!-- 射门 -->
      <match-progress title="射门" :home="match.homeShoot || 0" :away="match.awayShoot || 0" />
      <!-- 射正 -->
      <match-progress title="射正" :home="match.homeShootIn || 0" :away="match.awayShootIn || 0" />
      <!-- 角球 -->
      <match-progress
        title="角球"
        :home="match.homeCornerKick || 0"
        :away="match.awayCornerKick || 0"
      />
      <!-- 黄牌 -->
      <match-progress
        title="黄牌"
        :home="match.homeYellowCard || 0"
        :away="match.awayYellowCard || 0"
      />
      <!-- 红牌 -->
      <match-progress title="红牌" :home="match.homeRedCard || 0" :away="match.awayRedCard || 0" />
    </view>
    <!-- 赛事事件 -->
    <view class="p-30rpx">
      <view class="flex justify-between items-center mb-20rpx">
        <text class="text-30rpx text-black text-opacity-90 leading-42rpx">比赛概况</text>
        <view class="flex items-center gap-x-13rpx">
          <text class="text-28rpx text-black text-opacity-50 leading-40rpx">只看进球</text>
          <wd-switch v-model="showScoreOnly" />
        </view>
      </view>
      <!-- 事件内容 -->
      <view v-if="!isEmpty(filterLiveInfo)" class="flex flex-col gap-y-20rpx">
        <template
          v-for="({ type, time, data, position }, index) in filterLiveInfo"
          :key="`${index}-${time}`"
        >
          <!-- 开赛 -->
          <template v-if="type === LIVE_INFO_TYPE.KICK_OFF">
            <view class="flex items-center">
              <image
                src="https://sacdn.850g.com/football/static/icons/match/whistle.svg"
                class="w-30rpx h-30rpx"
              />
              <text
                class="flex-1 ml-20rpx p-20rpx bg-#F4F8FA text-black text-opacity-90 text-28rpx leading-40rpx"
              >
                {{ data }}
              </text>
            </view>
            <text
              class="flex justify-center items-center w-200rpx h-60rpx bg-#70B603 bg-opacity-10 text-26rpx text-#70B603 rounded-md"
            >
              比赛开始
            </text>
          </template>
          <!-- 中场 -->
          <template v-else-if="type === LIVE_INFO_TYPE.HALF_TIME">
            <view class="flex items-center">
              <image
                src="https://sacdn.850g.com/football/static/icons/match/document.svg"
                class="w-30rpx h-30rpx"
              />
              <text
                class="flex-1 ml-20rpx p-20rpx bg-#F4F8FA text-black text-opacity-90 text-28rpx leading-40rpx"
              >
                {{ data }}
              </text>
            </view>
            <text
              class="flex justify-center items-center w-200rpx h-60rpx bg-#ED8702 bg-opacity-10 text-26rpx text-#ED8702 rounded-md"
            >
              {{ `中场休息 ${match.homeHalfScore}-${match.awayHalfScore}` }}
            </text>
          </template>
          <!-- 比赛结束(附带结束语) -->
          <template v-else-if="type === LIVE_INFO_TYPE.END_OF_MATCH && !time">
            <text
              class="flex justify-center items-center w-200rpx h-60rpx bg-#D1302E bg-opacity-10 text-26rpx text-#D1302E rounded-md"
            >
              {{ `比赛结束 ${match.homeScore}-${match.awayScore}` }}
            </text>
            <view class="flex items-center">
              <image
                src="https://sacdn.850g.com/football/static/icons/match/document.svg"
                class="w-30rpx h-30rpx"
              />
              <text
                class="flex-1 ml-20rpx p-20rpx bg-#F4F8FA text-black text-opacity-90 text-28rpx leading-40rpx"
              >
                {{ data }}
              </text>
            </view>
          </template>
          <!-- 比赛结束 -->
          <template v-else-if="type === LIVE_INFO_TYPE.END_OF_MATCH">
            <view class="flex items-center">
              <image
                src="https://sacdn.850g.com/football/static/icons/match/whistle.svg"
                class="w-30rpx h-30rpx"
              />
              <text
                class="flex-1 ml-20rpx p-20rpx bg-#F4F8FA text-black text-opacity-90 text-28rpx leading-40rpx"
              >
                {{ data }}
              </text>
            </view>
          </template>
          <template v-else>
            <view class="flex items-center">
              <image :src="getMatchIcon(type)" class="w-30rpx h-30rpx" />
              <text class="flex-1 mx-20rpx text-28rpx text-black text-opacity-90 leading-40rpx">
                {{ data }}
              </text>
              <image :src="match.homeTeamLogo" class="w-40rpx h-40rpx" v-if="position === 1" />
              <image :src="match.awayTeamLogo" class="w-40rpx h-40rpx" v-else-if="position === 2" />
            </view>
          </template>
        </template>
      </view>
    </view>
  </view>
  <view v-else>
    <view class="flex flex-col items-center pt-100rpx">
      <image
        src="https://sacdn.850g.com/football/static/icons/no-match.svg"
        mode="scaleToFill"
        class="w-180rpx h-180rpx"
      />
      <text class="text-28rpx leading-40rpx text-black text-opacity-50">暂无赛况信息</text>
    </view>
  </view>
</template>
<script setup lang="ts">
import { isEmpty } from 'lodash-es'
import { IMatchItem, ILiveRecord } from '@/api/match'
import matchProgress from './components/matchProgress.vue'
import { LIVE_INFO_TYPE } from '@/utils/enum'
import { MATCH_LIVE_ICONS } from '@/utils/constant'

const props = defineProps<{ match: IMatchItem }>()
const liveInfo = ref<ILiveRecord[]>()

const showScoreOnly = ref(false)

const getMatchIcon = computed(() => {
  return (t: LIVE_INFO_TYPE) => {
    const icon = MATCH_LIVE_ICONS.find(({ id }) => id === t)
    return icon ? icon.url : 'https://sacdn.850g.com/football/static/icons/match/document.svg'
  }
})

const filterLiveInfo = computed(() =>
  showScoreOnly.value
    ? liveInfo.value.filter(({ type }) => type === LIVE_INFO_TYPE.GOAL)
    : liveInfo.value,
)

const unwatch = watch(
  () => props.match,
  (v) => {
    if (v && v.liveInfo) {
      const i: ILiveRecord[] = JSON.parse(v.liveInfo)
      liveInfo.value = i.reverse()
    }
  },
)

onUnmounted(unwatch)
</script>
