<route lang="json5">
{
  style: {
    navigationBarTitleText: '选择服务号',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view>
    <view class="account-item" v-for="account in accountList" :key="account.id">
      <view class="text-center">{{ account.name }}</view>
      <view class="text-center">
        <view class="color-[rgb(255,0,0)]" v-if="account.id === currentAccountId">使用中</view>
        <wd-button v-else @click="switchAccount(account.id)">切换</wd-button>
      </view>
    </view>
  </view>
</template>
<script lang="ts" setup>
import { getUserPushWx, switchWx } from '@/api/mpAccount'
import { getUserInfo } from '@/service/userService'
import { useUserStore } from '@/store/user'

const accountList = ref([])

const userStore = useUserStore()

const currentAccountId = computed(() => userStore.userInfo.captivePushAccount)

const switchAccount = async (accountId: number) => {
  const req = {
    newWxId: accountId,
    oldWxId: currentAccountId.value,
  }

  await switchWx(req)

  // 切换成功后，更新用户信息

  await freshUserInfo()

  uni.showToast({
    title: '切换成功',
    icon: 'success',
    duration: 2000,
  })
}

const freshUserInfo = async () => {
  const userInfo = await getUserInfo()
  userStore.setUserInfo(userInfo)
}

onMounted(async () => {
  const data = await getUserPushWx()
  accountList.value = data
})
</script>
<style lang="scss" scoped>
.account-item {
  display: grid;
  justify-content: center;
  align-items: center;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  height: 100rpx;
  border-bottom: 1px solid #eee;
}
</style>
