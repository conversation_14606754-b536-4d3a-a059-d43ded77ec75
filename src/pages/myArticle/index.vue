<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的方案',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <view class="py-2 flex justify-between items-center">
      <wd-calendar
        custom-class="flex-1"
        use-default-slot
        v-model="curDate"
        @confirm="changeDate"
        placeholder="请选择日期"
      >
        <wd-input
          v-model="params.date"
          prefix-icon="calendar"
          suffix-icon="fill-arrow-down"
          custom-class="px-2 py-2 rounded-[20rpx]"
          readonly
          no-border
          placeholder="请选择日期"
        />
      </wd-calendar>
      <wd-button @click.stop="goToRank" size="small">销量排名</wd-button>
    </view>
    <view class="flex justify-between">
      <view class="left flex-1 p-1">
        <wd-select-picker
          custom-class="rounded-[20rpx]"
          v-model="params.decision"
          placeholder="请选择判定状态"
          :columns="decisionColumns"
          @confirm="changeDate"
          type="radio"
          clearable
        ></wd-select-picker>
      </view>
      <view class="right flex-1 p-1">
        <wd-select-picker
          custom-class="rounded-[20rpx]"
          v-model="params.win"
          :columns="winColumns"
          placeholder="请选择红黑状态"
          @confirm="changeDate"
          type="radio"
          :disabled="params.decision === 0"
          clearable
        ></wd-select-picker>
      </view>
    </view>
    <view class="jump">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view class="jp-item" v-for="(item, index) in dataList" :key="index" v-else>
        <view class="px-2" @click="gotoDetail(item)">
          <view class="flex justify-between">
            <view class="left flex w-[500rpx]">
              <view class="title overflow-ellipsis ellipsis whitespace-nowrap">
                {{ item.title }}
              </view>
              <view class="flex items-center" :class="winClass(item.win)" v-if="item.win !== 0">
                {{ winLabel(item) }}
              </view>
            </view>
            <view class="right w-[160rpx]">
              <text class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</text>
            </view>
          </view>
          <view class="mt-2 flex justify-between">
            <view class="left">
              <text class="time">浏览量：{{ item.pvNum }}</text>
              <text class="time ml-[15rpx]">收益：{{ item.authorDivide }}</text>
            </view>
            <view class="right text-right flex-1 flex justify-end">
              <view
                :class="item.top == 1 ? 'bt-top' : 'bt-top-cannel'"
                @click.stop="changeTop(item)"
              >
                {{ item.top == 1 ? '取消置顶' : '置顶' }}
              </view>
              <view
                :class="item.status === 0 ? 'bt-up' : 'bt-down'"
                :disabled="item.status === 2"
                @click.stop="changeStatus(item)"
              >
                {{ getStatusButtonName(item.status) }}
              </view>
              <view
                class="bt-red-black"
                type="primary"
                plain
                @click.stop="showWinSet(item.id)"
                custom-class="btTag"
                v-if="item.win === 0 || item.buyNum === 0"
              >
                设置红黑
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <wd-loadmore
      custom-class="loadmore"
      :state="loadmoreState"
      @reload="loadmore"
      v-if="dataList.length > params.pageSzie && dataList.length < total"
    />
  </view>
  <wd-message-box selector="comfirm-box-slot" />
  <wd-message-box selector="win-box-slot">
    <wd-form ref="winFormRef" :model="winFrom">
      <view class="mt-2"><text>请根据赛事真实结果和预测内容设置</text></view>
      <wd-radio-group
        v-model="winFrom.win"
        shape="dot"
        checked-color="rgb(209, 48, 46)"
        prop="win"
        class="mt-2"
        :rules="[{ required: true, message: '请选择红黑', trigger: 'blur' }]"
      >
        <wd-radio :value="1">红</wd-radio>
        <wd-radio :value="2">黑</wd-radio>
        <wd-radio :value="3">走水</wd-radio>
        <wd-radio :value="4">2中1</wd-radio>
        <wd-radio :value="5">3中2</wd-radio>
        <wd-radio :value="6">4中3</wd-radio>
        <wd-radio :value="7">被绝杀</wd-radio>
      </wd-radio-group>
      <view class="mt-2">
        <wd-input
          v-model="winFrom.conclusion"
          :disabled="!winFrom.win"
          prop="conclusion"
          placeholder="请输入结语(选填)"
          custom-class="message-box-input"
        />
      </view>
    </wd-form>
  </wd-message-box>
  <back />
</template>
<script lang="ts" setup>
import { getMyArticlyList, updateWin } from '@/service/userService'
import { useMessage } from 'wot-design-uni'
import back from '@/components/back/index.vue'
import { changeTopStatus, changeArticleStatus } from '@/api/article'
const message = useMessage()
const winMessage = useMessage('win-box-slot')
import dayjs from 'dayjs'

const comfirmMessage = useMessage('comfirm-box-slot')
const dataList = ref([])
const curDate = ref(null)
const loadmoreState = ref('finished')
const total = ref(0)
const params = reactive({
  pageNo: 1,
  pageSzie: 10,
  date: '',
  decision: null,
  win: null,
  type: 2,
})

const decisionColumns = [
  {
    label: '已判定',
    value: 1,
  },
  {
    label: '未判定',
    value: 0,
  },
]

const winColumns = [
  {
    label: '红',
    value: 1,
  },
  {
    label: '黑',
    value: 2,
  },
  {
    label: '走水',
    value: 3,
  },
  {
    label: '2中1',
    value: 4,
  },
  {
    label: '3中2',
    value: 5,
  },
  {
    label: '4中3',
    value: 6,
  },
  {
    label: '被绝杀',
    value: 7,
  },
]
const getWinColumnsLabel = (value) => {
  return winColumns.find((item) => item.value === value).label
}

const winLabel = computed(() => {
  return (item) => {
    switch (item.win) {
      case 1:
      case 4:
      case 5:
      case 6:
        return '红'
      case 2:
      case 7:
        return '黑'
      case 3:
        return '走'
      default:
        return item.winName
    }
  }
})

const winClass = computed(() => {
  return (win: number) => {
    switch (win) {
      case 1:
      case 4:
      case 5:
      case 6:
        return 'win_red'
      case 2:
      case 7:
        return 'win_black'
      case 3:
        return 'win_blue'
      default:
        return 'bt-down'
    }
  }
})
const winFormRef = ref()
const winFrom = ref({
  id: null,
  win: null,
  conclusion: null,
})
const showWinSet = (id) => {
  winFrom.value.win = null
  winFrom.value.id = id
  winFrom.value.conclusion = null
  winMessage
    .confirm({
      title: '设置红黑',
      beforeConfirm: ({ resolve }) => {
        if (!winFrom.value.win) {
          uni.showToast({
            title: '请选择赛事结果',
            icon: 'none',
          })
          resolve(false)
        } else {
          message
            .confirm('是否将此方案设置为【' + getWinColumnsLabel(winFrom.value.win) + '】？')
            .then(async () => {
              updateWin(winFrom.value).then(() => {
                winMessage.close()
                changeDate()
              })
            })
        }
      },
    })
    .then(() => {})
    .catch((error) => {
      console.log(error)
    })
}
const getStatusName = (status: number) => {
  switch (status) {
    case 0:
      return '在售中'
    case 1:
      return '已下架'
    case 2:
      return '已封禁'
    default:
      return ''
  }
}

const getStatusButtonName = (status: number) => {
  switch (status) {
    case 0:
      return '上架'
    case 1:
      return '下架'
    case 2:
      return '已封禁'
    default:
      return ''
  }
}

const changeTop = async (item) => {
  if (item.status !== 2) {
    const data = {
      id: item.id,
      topStatus: item.top === 1 ? 0 : 1,
    }

    const res = await changeTopStatus(data)

    uni.showToast({
      title: data.topStatus === 1 ? '置顶成功' : '取消置顶成功',
      icon: 'none',
    })

    item.top = data.topStatus
  } else {
    uni.showToast({
      title: '该方案已封禁,无法进行置顶操作',
      icon: 'none',
    })
  }
}

const changeStatus = (item) => {
  if (item.status !== 2) {
    const data = {
      id: item.id,
      status: item.status === 0 ? 1 : 0,
    }

    const confirm = async () => {
      const res = await changeArticleStatus(data)
      uni.showToast({
        title: data.status == 1 ? '上架成功' : '下架成功',
        icon: 'none',
      })
      item.status = data.status
    }

    if (data.status === 1) {
      confirm()
    } else {
      comfirmMessage
        .confirm({
          title: '下架',
          msg: '确认要下架该方案吗？',
        })
        .then(() => {
          confirm()
        })
        .catch(() => {
          console.log('取消')
        })
    }
  } else {
    uni.showToast({
      title: '该方案已封禁,无法进行上下架操作',
      icon: 'none',
    })
  }
}

const goToRank = () => {
  uni.navigateTo({
    url: `/pages/rank/saleRank/index`,
  })
}
const gotoDetail = (item) => {
  if (item.status !== 2) {
    // uni.navigateTo({
    //   url: `/pages/detail/index?id=${item.id}`,
    // })
    uni.navigateTo({
      url: `/pages/article/setting/index?id=${item.id}`,
    })
  } else {
    uni.showToast({
      title: '该方案已封禁',
      icon: 'none',
    })
  }
}

const changeDate = () => {
  params.pageNo = 1
  dataList.value = []
  if (params.decision === 0) {
    params.win = null
  }
  getData()
}

const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  params.pageNo = 1
  getData()
})

const getData = async () => {
  if (curDate.value) {
    params.date = dayjs(curDate.value).format('YYYY-MM-DD')
  }
  const data = await getMyArticlyList(params)
  total.value = data.total
  loadmoreState.value = 'finished'
  if (isRefresh.value) {
    dataList.value = data.list
    // 提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  } else {
    if (Array.isArray(data.list)) {
      dataList.value = [...dataList.value, ...data.list]
    }
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

const loadmore = () => {
  setTimeout(() => {
    params.pageNo = params.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}

const init = ref(false)

onShow(() => {
  if (init.value) {
    changeDate()
  }
})

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})
onMounted(() => {
  getData()
  init.value = true
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-calendar .wd-icon-arrow-right) {
  display: none !important;
}

.jump {
  min-height: 80vh;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    padding: 10rpx 0rpx;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid rgb(236, 234, 234);
    border-radius: 10rpx;

    .win_red {
      padding: 2rpx 10rpx;
      margin-left: 20rpx;
      font-family: 'PingFang SC';
      font-size: 26rpx;
      color: #fff;
      text-align: center;
      background: #d1302e;
      border-radius: 8rpx;
    }

    .win_black {
      padding: 2rpx 10rpx;
      margin-left: 20rpx;
      font-family: 'PingFang SC';
      font-size: 26rpx;
      color: #fff;
      text-align: center;
      background: rgba(0, 0, 0, 0.9);
      border-radius: 8rpx;
    }

    .win_blue {
      padding: 2rpx 10rpx;
      margin-left: 20rpx;
      font-family: 'PingFang SC';
      font-size: 26rpx;
      color: #fff;
      text-align: center;
      background: #70b603;
      border-radius: 8rpx;
    }

    .win_now {
      padding: 2rpx 10rpx;
      margin-left: 30rpx;
      font-family: 'PingFang SC';
      font-size: 20rpx;
      color: #70b603;
      text-align: center;
      background: rgba(112, 182, 3, 0.05);
      border: 1px solid rgba(112, 182, 3, 0.4);
      border-radius: 8px;
    }

    .title {
      font-size: 30rpx;
      font-weight: 400;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.9);
    }

    .time {
      font-size: 26rpx;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.5);
    }
  }
}

.bt-red-black {
  display: flex;
  width: 120rpx;
  height: 40rpx;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  background: #d1302e;
  border: 1rpx solid #d1302e;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  line-height: 20rpx;
  margin-left: 10rpx;
}

.bt-top {
  display: flex;
  width: 120rpx;
  height: 40rpx;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  border: 1rpx solid #d1302e;
  color: #d1302e;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  line-height: 20rpx;
  /* 76.923% */
}

.bt-top-cannel {
  display: flex;
  width: 80rpx;
  height: 40rpx;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 12rpx;
  border: 1rpx solid #d1302e;
  color: #d1302e;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  line-height: 20rpx;
}

.bt-up {
  display: flex;
  width: 80rpx;
  height: 40rpx;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  background: #70b603;
  border: 1rpx solid #70b603;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  line-height: 20rpx;
  margin-left: 10rpx;
}

.bt-down {
  display: flex;
  width: 80rpx;
  height: 40rpx;
  justify-content: center;
  align-items: center;
  border-radius: 12rpx;
  background: #ed8702;
  border: 1rpx solid #ed8702;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  line-height: 20rpx;
  margin-left: 10rpx;
}

:deep(.wd-message-box__content) {
  max-height: 1000rpx !important;
}
</style>
