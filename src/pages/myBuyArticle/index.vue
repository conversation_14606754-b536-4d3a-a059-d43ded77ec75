<route lang="json5">
{
  style: {
    navigationBarTitleText: '已买方案',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <view class="flex justify-between my-2 bg-white rounded-[20rpx]">
      <view class="flex items-center left flex-1 p-1">
        <wd-calendar use-default-slot v-model="curDate" @confirm="changeData" placeholder="请选择日期">
          <wd-input v-model="params.date" prefix-icon="calendar" suffix-icon="fill-arrow-down" class="px-2 py-2"
            readonly no-border placeholder="请选择日期" />
        </wd-calendar>
      </view>
      <view class="right flex-1 p-1">
        <wd-select-picker v-model="params.authorId" placeholder="请选择作者" title="请选择作者" filterable
          filter-placeholder="可输入作者名称进行搜索" :columns="authorColumns" @confirm="changeData" type="radio" clearable
          required custom-style="padding-right: 0" />
      </view>
    </view>
    <view class="jump">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view class="jp-item" v-for="(item, index) in dataList" :key="index" @click="gotoDetail(item.articleId)" v-else>
        <view class="px-2">
          <view>
            <text class="title">{{ item.articleTitle }}</text>
            <text :class="winClass(item.win)" v-if="item.win !== 0">
              {{ winLabel(item.win) }}
            </text>
          </view>
          <view class="mt-2">
            <wd-row>
              <img class="avatar" :src="item.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg'" />
              <text class="time">{{ item.authorName }}</text>
              <text class="amount">￥{{ item.articlePrice }}</text>
              <text class="time">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</text>
              <text class="refund" v-if="item.isRefund === 1">已退款</text>
              <text class="privilege" v-if="item.payType === PAY_TYPE.PRIVILEGE_NUMBER">包次</text>
              <text class="privilege" v-if="item.payType === PAY_TYPE.PRIVILEGE_TIME">包时</text>
              <text class="privilege" v-if="item.payType === PAY_TYPE.PRIVILEGE_REPAIR">补单</text>
            </wd-row>
          </view>
          <view class="mt-2" v-if="item.conclusion">
            <text class="time">备注：{{ item.conclusion }}</text>
          </view>
        </view>
      </view>
    </view>
    <wd-loadmore custom-class="loadmore" :state="loadmoreState" @reload="loadmore"
      v-if="dataList.length > params.pageSzie && dataList.length < total" />
  </view>
  <back />
</template>
<script lang="ts" setup>
import { getMyBuyArticlyList, getMyBuyArticlyAuthorList } from '@/service/userService'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'
import { PAY_TYPE } from '@/utils/enum'

const dataList = ref([])
const curDate = ref(null)
const loadmoreState = ref('finished')
const total = ref(0)
const params = reactive({
  pageNo: 1,
  pageSzie: 10,
  date: '',
  authorId: '',
})

const authorColumns = ref([])

const changeData = () => {
  params.pageNo = 1
  dataList.value = []
  getData()
}

const winLabel = computed(() => {
  return (win: number) => {
    switch (win) {
      case 1:
      case 4:
      case 5:
      case 6:
        return '红'
      case 2:
      case 7:
        return '黑'
      case 3:
        return '走'
      default:
        return '进行中'
    }
  }
})

const winClass = computed(() => {
  return (win: number) => {
    switch (win) {
      case 1:
      case 4:
      case 5:
      case 6:
        return 'win_red'
      case 2:
      case 7:
        return 'win_black'
      case 3:
        return 'win_blue'
      default:
        return 'win_now'
    }
  }
})
const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  params.pageNo = 1
  getData()
})

const getAuthorList = async () => {
  const data = await getMyBuyArticlyAuthorList()
  authorColumns.value = data.map((item: any) => {
    return {
      label: item.authorName,
      value: item.authorId,
    }
  })
}
const getData = async () => {
  if (curDate.value) {
    params.date = dayjs(curDate.value).format('YYYY-MM-DD')
  }
  const data = await getMyBuyArticlyList(params)
  total.value = data.total
  loadmoreState.value = 'finished'
  if (isRefresh.value) {
    dataList.value = data.list
    // 提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  } else {
    if (Array.isArray(data.list)) {
      dataList.value = [...dataList.value, ...data.list]
    }
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}
const gotoDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/detail/index?id=${id}`,
  })
}
const loadmore = () => {
  setTimeout(() => {
    params.pageNo = params.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})
onMounted(() => {
  getAuthorList()
  getData()
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-calendar .wd-icon-arrow-right) {
  display: none !important;
}

.win_red {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #d1302e;
  border-radius: 8rpx;
}

.win_black {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8rpx;
}

.win_blue {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #fff;
  text-align: center;
  background: #70b603;
  border-radius: 8rpx;
}

.win_now {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 26rpx;
  color: #70b603;
  text-align: center;
  background: rgba(112, 182, 3, 0.05);
  border: 1px solid rgba(112, 182, 3, 0.4);
  border-radius: 8px;
}

.refund {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  color: #fff;
  text-align: center;
  background: #ed8702;
  border: 1rpx solid #ed8702;
  border-radius: 8px;
}

.privilege {
  padding: 2rpx 10rpx;
  margin-left: 30rpx;
  font-family: 'PingFang SC';
  font-size: 24rpx;
  color: #fff;
  text-align: center;
  background: #70b603;
  border: 1rpx solid #70b603;
  border-radius: 8px;
}

.jump {
  min-height: 80vh;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 5rpx;
    margin-bottom: 20rpx;
    border-bottom: 1rpx solid rgb(236, 234, 234);
    border-radius: 10rpx;

    .title {
      font-size: 32rpx;
      font-weight: 400;
      line-height: 50rpx;
    }

    .status-gray {
      padding: 3rpx 10rpx;
      margin-left: 30rpx;
      font-size: 22rpx;
      color: #999;
      border: 1rpx solid #999;
    }

    .avatar {
      width: 40rpx;
      height: 40rpx;
      vertical-align: middle;
      border-radius: 50%;
    }

    .time {
      margin-left: 20rpx;
      font-size: 26rpx;
      line-height: 50rpx;
      color: rgba(0, 0, 0, 0.5);
    }

    .amount {
      margin-left: 20rpx;
      font-size: 26rpx;
      line-height: 50rpx;
      color: #d1302e;
    }
  }
}
</style>
