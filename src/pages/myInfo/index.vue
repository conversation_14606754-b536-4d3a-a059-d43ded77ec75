<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '个人中心',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view style="box-sizing: border-box" class="pb-[60rpx]">
    <view class="flex justify-between pb-[30rpx]">
      <view class="flex justify-center items-center" style="font-size: 35rpx; font-weight: bold">
        <image
          class="avatar"
          :src="userInfo.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg'"
          @click="updateAvatarMessageBox"
        />
        <view v-if="isLogined">
          <wd-row>
            <text class="nick mr-[10rpx]" @click="upateuUserNickName">{{ userInfo.nickname }}</text>
            <wd-icon
              name="edit"
              size="22px"
              @click="upateuUserNickName"
              v-if="userInfo.isAuthor"
            ></wd-icon>
            <!-- <text class="mscore" v-if="userInfo.isAuthor" @click="showMyScore">战绩</text> -->
            <text class="mscore" v-if="userInfo.isAuthor" @click="gotoZhanji">战绩</text>
          </wd-row>
          <wd-row>
            <text v-if="isLogined" class="uid">ID:{{ userInfo.id }}</text>
          </wd-row>
        </view>
        <text @click="goLogin" v-else>立即登录</text>
      </view>
      <view class="flex justify-center items-center mb-[5rpx]" v-if="isLogined">
        <view class="mb-[5rpx] mr-[30rpx]" @click="goAttention" v-if="!userInfo.isAuthor">
          <text class="font-bold mr-[5rpx]">{{ userStore.userInfo.attentionNum || 0 }}</text>
          <text class="titl">关注</text>
        </view>
        <image
          src="https://sacdn.850g.com/football/static/qhzh.svg"
          class="exchange"
          v-if="isRootAccount && userInfo.isAuthor && userInfo.author === 1"
          @click="exchangeAccount"
        ></image>
        <image
          src="https://sacdn.850g.com/football/static/qhjs.svg"
          class="publish"
          v-if="userInfo.author === 1"
          @click="changeRole"
        ></image>
      </view>
    </view>
    <view class="other-container" v-if="userInfo.isAuthor">
      <wd-row>
        <wd-col :span="8">
          <view class="text-center" @click="goMyArticle">
            {{ userStore.userInfo.articleNum || 0 }}
            <text class="titl">方案</text>
          </view>
        </wd-col>
        <wd-col :span="8">
          <view class="text-center" @click="goAttention">
            {{ userStore.userInfo.attentionNum || 0 }}
            <text class="titl">关注</text>
          </view>
        </wd-col>
        <wd-col :span="8">
          <view class="text-center" @click="goFans">
            {{ userStore.userInfo.fans || 0 }}
            <text class="titl">粉丝</text>
          </view>
        </wd-col>
      </wd-row>
    </view>
    <view class="action-container">
      <view class="packet" v-if="userInfo.isAuthor">
        <!--        <view class="gold" v-if="!userInfo.isAuthor">-->
        <!--          <image style="width: 40rpx; height: 40rpx" src="https://sacdn.850g.com/football/static/gold.svg" />-->
        <!--          <text class="num" @click="goGoldDetail">{{ userStore.userInfo.gold || 0 }}</text>-->
        <!--          <text class="ac-titl" @click="goGoldDetail">鱼币</text>-->
        <!--          <view class="recharge-button" @click="goRecharge">充值</view>-->
        <!--        </view>-->
        <view class="balance" @click="goBalanceDetail" v-if="userInfo.isAuthor">
          <image
            style="width: 40rpx; height: 40rpx"
            src="https://sacdn.850g.com/football/static/balance.svg"
          />
          <text class="num">{{ userStore.userInfo.balance || 0 }}</text>
          <text class="ac-titl">余额</text>
        </view>
        <view class="withdraw" v-if="userInfo.isAuthor" @click="goToWithdraw">提现</view>
        <image class="bg" src="https://sacdn.850g.com/football/static/info_bg.png" />
      </view>
      <view class="action">
        <view
          :class="userInfo?.author == 1 ? 'item' : 'item-a'"
          v-for="(item, index) in actionOptions"
          :key="index"
          @click="item.func()"
          :style="{ display: item.state ? 'flex' : 'none' }"
        >
          <image :src="item.icon" style="width: 50rpx; height: 50rpx; margin: 20rpx" />
          <text>{{ item.name }}</text>
        </view>
      </view>
    </view>
    <view class="jump">
      <template v-for="(item, index) in jumpOptions" :key="index">
        <!-- <view class="jp-item" v-for="(item, index) in jumpOptions" :key="index" @click="item.func()" -->
        <view class="jp-item flex" v-if="item.state" @click="item.func()">
          <template v-if="item.customized === 'pushNumber' && userInfo.isAuthor">
            <view class="flex flex-col w-full">
              <view class="flex justify-between w-full">
                <view class="title">{{ item.name }}</view>
                <wd-switch
                  v-model="isAttention"
                  :active-value="0"
                  :inactive-value="1"
                  @change="handlePushSwitch"
                />
              </view>
              <template v-if="!isAttention">
                <view class="flex justify-end mt-60rpx">
                  <wd-radio-group
                    inline
                    v-model="consume"
                    shape="dot"
                    @change="handleConsumeChange"
                  >
                    <wd-radio :value="0">全部</wd-radio>
                    <wd-radio :value="1">未消费</wd-radio>
                    <wd-radio :value="2">已消费</wd-radio>
                  </wd-radio-group>
                </view>
              </template>
            </view>
          </template>
          <template v-else-if="item.publicUser">
            <view class="flex flex-col w-full">
              <view class="flex justify-between w-full">
                <view class="title">{{ item.name }}</view>
                <wd-switch
                  v-model="publicUser"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handlePushSwitchPublicUser"
                />
              </view>
            </view>
          </template>
          <template v-else-if="item.accomplishment">
            <view class="flex flex-col w-full">
              <view class="flex justify-between w-full">
                <view class="title">{{ item.name }}</view>
                <wd-switch
                  v-model="accomplishment"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handlePushSwitchAccomplishment"
                />
              </view>
            </view>
          </template>
          <template v-else>
            <view class="title">{{ item.name }}</view>
            <view class="move">
              <wd-icon name="arrow-right" size="22px" color="#999" v-if="!item.othrer"></wd-icon>
              <view v-else v-html="item.othrer"></view>
            </view>
          </template>
        </view>
      </template>

      <view class="h-120rpx"></view>
    </view>
    <wd-popup v-model="showAuthorTips" closable>
      <view class="author-tips-container">
        <image
          style="width: 400rpx; height: 400rpx"
          src="https://sacdn.850g.com/football/static/author_tips.png"
        />
        <text class="tip">申请开通作者权限后才发布方案</text>
        <button class="button" @click="authorAudit">马上申请</button>
      </view>
    </wd-popup>
    <wd-message-box selector="nick-box-slot"></wd-message-box>
    <wd-message-box selector="avatar-box-slot">
      <view class="flex justify-center pt-[30rpx]">
        <wd-upload
          ref="uploadRef"
          :disabled="userInfo?.author !== 1"
          :header="uploadHearder"
          :limit="1"
          image-mode="aspectFill"
          @change="changeFileList"
          :file-list="imgUrl"
          :action="action"
          @success="successUpload"
        ></wd-upload>
      </view>
    </wd-message-box>
  </view>
  <wd-popup
    v-model="exchangeAccountVisible"
    custom-style="box-sizing: border-box;height: 1000rpx;margin-bottom: 60px;"
    position="bottom"
    lock-scroll
    :safe-area-inset-bottom="true"
  >
    <view class="h-100% flex flex-col">
      <view class="title h-100rpx flex justify-between items-center p-x-30rpx">
        <view class="left flex-1">
          <wd-input
            v-model="filterAccountName"
            class="flex-1 m-r-[10rpx]"
            prefix-icon="search"
            custom-style="box-sizing: border-box;pading-right:20rpx;"
          />
        </view>
        <view class="right flex items-center" v-if="!userInfo.parentId">
          <wd-button size="small" @click="openChildAccountModal">新增账号</wd-button>
        </view>
      </view>
      <scroll-view style="flex: 1">
        <view class="account-list">
          <view
            class="account-list-item"
            v-for="(item, index) in selectAccountList"
            :key="index"
            @click="selectAccount(item)"
          >
            <image
              class="avatar"
              :src="item.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg'"
            />
            <text class="font-size-25rpx font-bold">{{ item.nickname }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </wd-popup>
  <wd-message-box selector="wd-message-add-child">
    <wd-form ref="childAccountFormRef" :model="childAccountFormData">
      <wd-input
        label="账号"
        label-width="100rpx"
        prop="username"
        clearable
        :maxlength="12"
        minlength="6"
        custom-style="border-bottom:1px solid #eee;"
        v-model="childAccountFormData.username"
        placeholder="请输入账号"
        :rules="[
          { required: true, message: '请输入账号', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9]{6,12}$/,
            message: '只能包含字母和数字，长度为6-12位',
            trigger: 'blur',
          },
        ]"
      />
      <wd-input
        label="密码"
        label-width="100rpx"
        prop="password"
        clearable
        show-password
        :maxlength="15"
        minlength="6"
        custom-style="border-bottom:1px solid #eee;"
        v-model="childAccountFormData.password"
        placeholder="请输入密码"
        :rules="[
          { required: true, message: '请输入密码', trigger: 'blur' },
          {
            pattern: /^[a-zA-Z0-9!@#$%^&*()_+~`|}{[\]:;?><,./-=]{6,15}$/,
            message: '只能包含字母以及数字特殊符号，长度为6-15位',
            trigger: 'blur',
          },
        ]"
      />
      <wd-input
        label="昵称"
        label-width="100rpx"
        prop="nickname"
        clearable
        :maxlength="14"
        custom-style="border-bottom:1px solid #eee;"
        v-model="childAccountFormData.nickname"
        placeholder="请输入昵称"
      />
    </wd-form>
  </wd-message-box>
  <authorScore ref="authorScoreRef" />
</template>
<script lang="ts" setup>
import { wxLogin } from '@/service/login'
import {
  getUserInfo,
  updateNickName,
  updateAvatar,
  updateAttention,
  updateAccomplishment,
} from '@/service/userService'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { useMessage } from 'wot-design-uni'
import { getChildAccountList, addChildAccount, changeToChildAccount } from '@/api/user'
import { canWithdraw } from '@/api/withdraw'
import { checkPartnerStatus } from '@/api/partner'
import authorScore from '@/components/authorScore/index.vue'
import { isNil } from 'lodash-es'
import { getLastPage } from '@/utils'
import { updatePublicUser } from '@/api/partner'

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const code = ref(null)
const showAuthorTips = ref(false)
const authorScoreRef = ref()
const message = useMessage()
const nickMessageBox = useMessage('nick-box-slot')
const avatarMessageBox = useMessage('avatar-box-slot')
const exchangeAccountVisible = ref(false)
const childAccountList = ref([])
const filterAccountName = ref('')
const childMessageBox = useMessage('wd-message-add-child')
const childAccountFormData = ref({
  username: '',
  password: '',
  nickname: '',
})
const isRootAccount = computed(() => {
  return (!userInfo.value?.parentId || localStorage.getItem('isRootAccount') !== 'null')
})

const isAttention = computed(() => userInfo.value.isAttention)
const consume = computed(() => userInfo.value.consume)
const publicUser = computed(() => userInfo.value.publicUser)
const accomplishment = computed(() => userInfo.value.accomplishment)

const selectAccountList = computed(() => {
  if (!filterAccountName.value) {
    return childAccountList.value
  } else {
    filterAccountName.value = filterAccountName.value.replaceAll(' ', '')
    const filterList = childAccountList.value.filter((item) => {
      return item.nickname.includes(filterAccountName.value)
    })
    return filterList
  }
})

const childAccountFormRef = ref()

async function changePushStatus(attention: number, con: number) {
  try {
    uni.showLoading()
    await updateAttention(attention, con)
    uni.showToast({ title: '修改成功', icon: 'none' })
    const userInfoResult = await getUserInfo()
    userStore.setUserInfo(userInfoResult)
  } finally {
    uni.hideLoading()
  }
}

function handlePushSwitch({ value }: { value: number }) {
  if (value !== userInfo.value.isAttention) {
    changePushStatus(value, consume.value)
  }
}

const handlePushSwitchPublicUser = async ({ value }) => {
  console.log('value', value)
  await updatePublicUser(value)
  uni.showToast({ title: '修改成功', icon: 'success' })
  const userInfoResult = await getUserInfo()
  userStore.setUserInfo(userInfoResult)
}

const handlePushSwitchAccomplishment = async ({ value }) => {
  console.log('value', value)
  await updateAccomplishment(value)
  uni.showToast({ title: '修改成功', icon: 'success' })
  const userInfoResult = await getUserInfo()
  userStore.setUserInfo(userInfoResult)
}

function handleConsumeChange({ value }) {
  changePushStatus(isAttention.value, value)
}

// 打开新增子账号弹窗
const openChildAccountModal = () => {
  // 重置表单
  childAccountFormData.value = {
    username: '',
    password: '',
    nickname: '',
  }
  exchangeAccountVisible.value = false
  childMessageBox
    .confirm({
      title: '新增子账号',
      beforeConfirm: ({ resolve }) => {
        childAccountFormRef.value.validate().then(async ({ valid }) => {
          if (valid) {
            try {
              await addChildAccount(childAccountFormData.value)
              uni.showToast({ title: '新增子账号成功' })
            } catch (e) {
              resolve(false)
              return
            }

            resolve(true)
          } else {
            resolve(false)
          }
        })
      },
    })
    .then(async () => {})
}

const selectAccount = (item) => {
  uni.showModal({
    title: '切换账号',
    content: `是否切换到账号：${item.nickname}`,
    success: async (res) => {
      if (res.confirm) {
        const res = await changeToChildAccount({ userId: item.id })
        uni.showToast({ title: '切换账号成功' })
        exchangeAccountVisible.value = false
        localStorage.setItem('isRootAccount', res.parentAccessToken)
        userStore.setToken(
          res.accessToken.accessToken,
          res.accessToken.refreshToken,
          res.parentAccessToken,
        )
        login()
      }
    },
  })
}

const imgUrl = ref([])
const uploadRef = ref()
const uploadHearder = computed(() => {
  return {
    Authorization: `Bearer ${userInfo.value.token}`,
  }
})

const action = computed(() => {
  return import.meta.env.VITE_UPLOAD_BASEURL
})
const changeFileList = ({ fileList: files }) => {
  imgUrl.value = files
}

const successUpload = ({ file }) => {
  const response = file.response
  const result = JSON.parse(response)
  imgUrl.value = [{ url: result.data }]
}
const updateAvatarMessageBox = () => {
  if (!userInfo.value.isAuthor) {
    return
  }
  imgUrl.value = [
    { url: userInfo.value.avatarUrl || 'https://sacdn.850g.com/football/static/avatar.svg' },
  ]
  avatarMessageBox
    .confirm({
      title: '更改头像',
    })
    .then(() => {
      updateAvatar({ avatar: imgUrl.value[0].url }).then(() => {
        uni.showToast({ title: '修改成功' })
        avatarMessageBox.close()
        login()
      })
    })
    .catch(() => {
      console.log('点击了取消按钮')
    })
}

const goToWithdraw = async () => {
  const isCan = await canWithdraw()
  if (!isCan) {
    uni.showModal({
      title: '提现失败',
      content: '您还没有填写提现信息，请先填写提现信息',
      showCancel: false,
      confirmText: '确定',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/settlementinfo/index',
          })
        }
      },
    })
  } else {
    uni.navigateTo({
      url: '/pages/withdraw/index',
    })
  }
}

const upateuUserNickName = () => {
  if (userInfo.value.isAuthor) {
    nickMessageBox
      .prompt({
        title: '修改昵称',
        inputValue: userInfo.value?.nickname,
        inputPlaceholder: '请输入昵称',
        inputPattern: /\S.+/i,
        inputError: '昵称不能为空且不能超过15个字符',
        inputValidate: (value) => {
          if (String(value).length > 15) {
            return false
          } else {
            return true
          }
        },
      })
      .then((e) => {
        updateNickName({ nickname: e.value }).then(() => {
          uni.showToast({ title: '修改成功' })
          nickMessageBox.close()
          login()
        })
      })
      .catch((error) => {
        console.log(error)
      })
  }
}
const isLogined = computed(() => {
  return userStore.isLogined
})

// const showMyScore = () => {
//   authorScoreRef.value.showDialog()
// }

function gotoZhanji() {
  uni.navigateTo({ url: '/pages/zhanji/generate' })
}

const actionOptions = reactive([
  {
    name: '我的足迹',
    state: computed(() => {
      return !userInfo?.value.isAuthor
    }),
    icon: 'https://sacdn.850g.com/football/static/track.svg',
    func: () => {
      if (isLogined.value) {
        uni.navigateTo({
          url: '/pages/footprint/index',
        })
      } else {
        goLogin()
      }
    },
  },
  {
    name: '充值订单',
    state: computed(() => {
      return !userInfo?.value.isAuthor
    }),
    icon: 'https://sacdn.850g.com/football/static/order.svg',
    func: () => {
      if (isLogined.value) {
        uni.navigateTo({
          url: '/pages/order/index',
        })
      } else {
        goLogin()
      }
    },
  },
  {
    name: '鱼币明细',
    state: computed(() => {
      return !userInfo?.value.isAuthor
    }),
    icon: 'https://sacdn.850g.com/football/static/gold_info.svg',
    func: () => {
      if (isLogined.value) {
        uni.navigateTo({
          url: '/pages/goldlogs/index',
        })
      } else {
        goLogin()
      }
    },
  },
  {
    name: '余额明细',
    state: computed(() => {
      return (
        userInfo?.value.isAuthor &&
        !(userInfo?.value.payAccountId && userInfo?.value.payAccountId !== 0)
      )
    }),
    icon: 'https://sacdn.850g.com/football/static/income.svg',
    func: () => {
      uni.navigateTo({
        url: '/pages/authordivide/index',
      })
    },
  },
  {
    name: '收入明细',
    state: computed(() => {
      return (
        userInfo?.value.isAuthor &&
        userInfo?.value.payAccountId &&
        userInfo?.value.payAccountId !== 0
      )
    }),
    icon: 'https://sacdn.850g.com/football/static/income.svg',
    func: () => {
      uni.navigateTo({
        url: '/pages/authordivide/income/index',
      })
    },
  },
  {
    name: '套餐用户',
    state: computed(() => {
      return userInfo?.value.isAuthor
      // return false
    }),
    icon: 'https://sacdn.850g.com/football/static/tx_info.svg',
    func: () => {
      uni.navigateTo({
        url: '/pages/privilegeset/other/index',
      })
    },
  },
  {
    name: '套餐明细',
    state: computed(() => {
      return !userInfo?.value.isAuthor
    }),
    icon: 'https://sacdn.850g.com/football/static/order.svg',
    func: () => {
      uni.navigateTo({
        url: '/pages/privilegeset/user/index',
      })
    },
  },
  {
    name: '销量排名',
    state: computed(() => {
      return userInfo?.value.isAuthor
    }),
    icon: 'https://sacdn.850g.com/football/static/salerank.svg',
    func: () => {
      uni.navigateTo({
        url: '/pages/rank/saleRank/index',
      })
    },
  },
  {
    name: '数据报表',
    state: computed(() => {
      return userInfo?.value.isAuthor
    }),
    icon: 'https://sacdn.850g.com/football/static/salerank.svg',
    func: () => {
      uni.navigateTo({
        url: '/pages/report/index',
      })
    },
  },
  // {
  //   name: '提现记录',
  //   state: computed(() => {
  //     return userInfo?.value.isAuthor
  //   }),
  //   icon: '/static/images/tx_info.png',
  //   func: () => {
  //     uni.navigateTo({
  //       url: '/pages/withdraw/log/index',
  //     })
  //   },
  // },
  // {
  //   name: '提现信息',
  //   state: computed(() => {
  //     return userInfo?.value.isAuthor
  //   }),
  //   icon: 'https://sacdn.850g.com/football/static/tx_info.svg',
  //   func: () => {
  //     uni.navigateTo({
  //       url: '/pages/settlementinfo/index',
  //     })
  //   },
  // },
  {
    name: '在线客服',
    state: false,
    icon: 'https://sacdn.850g.com/football/static/kf.svg',
    func: () => {
      // location.href = 'https://work.weixin.qq.com/kfid/kfc2713cc44c19876b8'
      const url = 'http://work.weixin.qq.com/kfid/kfc2713cc44c19876b8'
      uni.navigateTo({
        url: `/pages/webview/index?target=${encodeURIComponent(url)}`,
      })
    },
  },
  {
    name: '补单记录',
    state: computed(() => {
      return userInfo?.value.isAuthor
    }),
    icon: 'https://sacdn.850g.com/football/static/tx_info.svg',
    func: () => {
      uni.navigateTo({
        url: '/pages/exOrder/record/index',
      })
    },
  },
  {
    name: '补单明细',
    state: computed(() => {
      return !userInfo?.value.isAuthor
    }),
    icon: 'https://sacdn.850g.com/football/static/tx_info.svg',
    func: () => {
      uni.navigateTo({
        url: '/pages/exOrder/detail/index',
      })
    },
  },
])

const withdraw = () => {
  console.log('提现')
  uni.navigateTo({
    url: '/pages/withdraw/index',
  })
}

const authorAudit = () => {
  showAuthorTips.value = false
  goRouter('/pages/article/apply/index')
}
const changeRole = () => {
  message
    .confirm(userInfo.value.isAuthor ? '确定切换为【用户角色】？' : '确定切换为【作者角色】？')
    .then(async () => {
      userStore.changeRole(!userStore.userInfo.isAuthor)
    })
}
const releaseArticle = () => {
  if (!userInfo.value.author) {
    // 提示要申请作者权限
    showAuthorTips.value = true
  } else {
    // 跳转到发布方案页面
    goRouter('/pages/article/relaese/index')
  }
}

const authorStatus = computed(() => {
  if (!isLogined.value) {
    return null
  }

  if (userInfo.value.authorAuditStatus === 1) {
    return '<text style="color: rgb(107, 173, 5)" >已认证</text>'
  } else if (userInfo.value.authorAuditStatus === 0) {
    return '<text style="color: rgb(241, 137, 2)">审核中</text>'
  } else if (userInfo.value.authorAuditStatus === -1) {
    return '<text style="color: rgb(211, 61, 59)" >已拒绝</text>'
  } else {
    return '<text style="color:#999999" >未申请</text>'
  }
})

const privilegeSetStatus = computed(() => {
  return userInfo?.value.privilegeSetStatus === 1
    ? '<text style="color: rgb(107, 173, 5)" >已设置</text>'
    : '<text style="color: rgb(241, 137, 2)" >未设置</text>'
})

const jumpOptions = reactive([
  {
    name: '已买方案',
    state: computed(() => {
      return !userInfo?.value.isAuthor
    }),
    func: () => {
      if (isLogined.value) {
        uni.navigateTo({
          url: '/pages/myBuyArticle/index',
        })
      } else {
        goLogin()
      }
    },
  },
  // {
  //   name: '我的方案',
  //   state: computed(() => {
  //     return userInfo?.value.isAuthor
  //   }),
  //   func: () => {
  //     uni.navigateTo({
  //       url: '/pages/myArticle/index',
  //     })
  //   },
  // },
  {
    name: '特权设置',
    state: computed(() => {
      // return userInfo?.value.isAuthor
      return false
    }),
    func: () => {
      if (isLogined.value) {
        uni.navigateTo({
          url: '/pages/privilegeset/index',
        })
      } else {
        goLogin()
      }
    },
    othrer: computed(() => {
      return privilegeSetStatus.value
    }),
  },
  // {
  //   name: '提现说明',
  //   state: computed(() => {
  //     return userInfo?.value.isAuthor
  //   }),
  //   func: () => {
  //     uni.navigateTo({
  //       url: '/pages/settlerule/index',
  //     })
  //   },
  // },
  {
    name: '申请成为作者',
    state: computed(() => {
      // return userInfo?.value.author !== 1
      return true
    }),
    func: () => {
      if (isLogined.value) {
        // if (userInfo.value.authorAuditStatus !== 1) {
        // goRouter('/pages/article/apply/index')
        // }
        ![0, 1].includes(userInfo.value.authorAuditStatus) && goRouter('/pages/author/apply/poster')
      } else {
        goLogin()
      }
    },
    othrer: computed(() => {
      return authorStatus.value
    }),
  },
  {
    // name: !userInfo?.value.partner ? '申请成为合伙人' : '我是合伙人',
    name: computed(() => (!userInfo?.value.partner ? '申请成为合伙人' : '我是合伙人')),
    // state: computed(() => !userInfo?.value.partner),
    state: true,
    func: async () => {
      // if (!isLogined.value) {
      //   goLogin()
      // } else if (!userInfo?.value.partner) {
      //   goRouter('/pages/partner/index')
      // }
      if (!isLogined.value) {
        goLogin()
      } else {
        await checkPartnerStatus()
        // goRouter('/pages/partner/index')
        const url = userInfo?.value.partner ? '/pages/partner/index' : '/pages/partner/poster'
        goRouter(url)
      }
    },
  },
  {
    // name: !userInfo?.value.partner ? '申请成为合伙人' : '我是合伙人',
    name: '投诉建议',
    // state: computed(() => !userInfo?.value.partner),
    state: true,
    func: async () => {
      uni.navigateTo({
        url: '/pages/complaints/index',
      })
    },
  },
  {
    name: '服务协议',
    state: true,
    func: () => {
      uni.navigateTo({
        url: '/pages/agreement/index',
      })
    },
  },
  {
    name: '隐私政策',
    state: true,
    func: () => {
      uni.navigateTo({
        url: '/pages/privacy/index',
      })
    },
  },
  // {
  //   name: '个人信息',
  //   state: true,
  //   func: () => {
  //     uni.navigateTo({
  //       url: '/pages/partner/info'
  //     })
  //   }
  // },
  {
    name: '是否强制关注服务号',
    state: computed(() => {
      return userInfo.value.isAuthor
    }),
    func: () => {},
    customized: 'pushNumber',
    // othrer: computed(() => {
    //   return 'aaaaa'
    // })
  },
  {
    name: '是否开放公域',
    state: computed(() => {
      return userInfo.value.isAuthor
    }),
    func: () => {},
    publicUser: true,
    // othrer: computed(() => {
    //   return 'aaaaa'
    // })
  },
  {
    name: '是否展示战绩',
    state: computed(() => {
      return userInfo.value.isAuthor
    }),
    func: () => {},
    accomplishment: true,
    // othrer: computed(() => {
    //   return 'aaaaa'
    // })
  },
  {
    name: '切换服务号',
    state: computed(() => {
      return userInfo.value.isAuthor && userInfo.value.multipleWx
    }),
    func: () => {
      uni.navigateTo({
        url: '/pages/mpAccount/changeAccount',
      })
    },
  },
  {
    name: '退出登录',
    state: computed(() => {
      return isLogined.value
    }),
    func: () => {
      uni.showModal({
        title: '退出登录',
        content: '确定退出登录吗？',
        success: (res) => {
          if (res.confirm) {
            userStore.reset()
          }
        },
      })
    },
  },
])

const exchangeAccount = async () => {
  const res = await getChildAccountList()
  childAccountList.value = res
  exchangeAccountVisible.value = true
}

const goRouter = (url: string) => {
  uni.navigateTo({
    url,
  })
}

const goLogin = () => {
  // const localUrl = window.location.origin + window.location.pathname
  const localUrl = getLastPage() as string
  console.log(localUrl)
  let route = localUrl.route
  if (!route.startsWith('/')) {
    route = '/' + route
  }
  uni.navigateTo({
    url: `/pages/login/index?redirect=${route}`,
  })
  // getWXH5LoginCode(localUrl)
}

const goMyArticle = () => {
  uni.navigateTo({
    url: '/pages/myArticle/index',
  })
}

const goRecharge = () => {
  uni.navigateTo({
    url: '/pages/recharge/index',
  })
}

const goBalanceDetail = () => {
  uni.navigateTo({
    url: '/pages/authordivide/index',
  })
}

const goGoldDetail = () => {
  uni.navigateTo({
    url: '/pages/goldlogs/index',
  })
}

const goAttention = () => {
  uni.navigateTo({
    url: '/pages/attention/index',
  })
}

const goFans = () => {
  uni.navigateTo({
    url: '/pages/fans/index',
  })
}

const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  login()
})

const login = async () => {
  if (code.value && !isLogined.value) {
    const loginResult = await wxLogin({ code: code.value })
    // console.log('登录结果', loginService.value.accessToken)
    userStore.setToken(
      loginResult.accessToken.accessToken,
      loginResult.accessToken.refreshToken,
      null,
    )
  }
  if (isLogined.value) {
    const userInfoResult = await getUserInfo()
    userStore.setUserInfo(userInfoResult)
    if (isNil(userStore.userInfo.isAuthor)) {
      userStore.changeRole(!!userInfoResult.author)
    }
  }
  if (isRefresh.value) {
    //提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

onShow(() => {
  login()
})

onLoad((e) => {
  code.value = e.code
  // isAttention.value = userInfo.value.isAttention
  // consume.value = userInfo.value.consume
})
</script>

<style lang="scss" scoped>
:deep(.wd-radio.is-dot.is-checked .wd-radio__shape) {
  background: #d1302e !important;
  border-color: #d1302e !important;
}

.cmbutton {
  background: #d1302e !important;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.mscore {
  padding: 5rpx 10rpx;
  margin-left: 20rpx;
  font-size: 26rpx;
  font-weight: 400;
  color: white;
  background-color: #d1302e;
  border-radius: 12rpx;
}

.nick {
  font-size: 30rpx;
}

.uid {
  font-size: 26rpx;
  color: rgba(0, 0, 0, 0.5);
}

.exchange {
  width: 60rpx;
  height: 60rpx;
  padding: 10rpx;
  margin-right: 10rpx;
}

.publish {
  width: 60rpx;
  height: 60rpx;
  padding: 10rpx;
  margin-right: 20rpx;
}

.other-container {
  padding: 0 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);

  .titl {
    font-size: 30rpx;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.5);
  }
}

.action-container {
  margin: 20rpx;
  overflow: hidden;
  font-size: 28rpx;
  background-color: white;
  border-radius: 20rpx;

  .packet {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 40rpx;
    font-size: 32rpx;

    .withdraw {
      padding: 2rpx 15rpx;
      font-size: 26rpx;
      color: white;
      border: 1px solid white;
      border-radius: 10rpx;
    }

    .bg {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
    }

    .balance {
      display: flex;
      align-items: center;
      justify-content: space-around;
      color: white;

      .num {
        margin-left: 10rpx;
        font-weight: 600;
      }

      .ac-titl {
        margin: 0 10rpx;
        font-size: 26rpx;
      }
    }

    .gold {
      display: flex;
      align-items: center;
      justify-content: space-around;
      color: white;

      .num {
        margin-left: 10rpx;
        font-weight: 600;
      }

      .ac-titl {
        margin: 0 30rpx 0 10rpx;
        font-size: 26rpx;
      }

      .recharge-button {
        padding: 2rpx 15rpx;
        font-size: 26rpx;
        border: 1px solid white;
        border-radius: 10rpx;
      }
    }
  }

  .action {
    display: flex;
    flex-wrap: wrap;
    justify-content: start;
    padding: 30rpx 0;

    .item {
      box-sizing: border-box;
      display: flex;
      flex: 0 0 calc(33.33%);
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: rgb(149, 149, 149);
    }

    .item-a {
      box-sizing: border-box;
      display: flex;
      flex: 0 0 calc(25%);
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: rgb(149, 149, 149);
    }
  }
}

.jump {
  margin: 20rpx;
  background-color: white;
  border-radius: 20rpx;

  .jp-item {
    display: flex;
    justify-content: space-between;
    padding: 26rpx 40rpx;
    border-bottom: 1px solid rgb(246, 246, 246);

    &:active {
      background-color: rgb(246, 246, 246);
    }

    .title {
      display: flex;
      align-items: center;
      font-size: 35rpx;

      &::before {
        display: inline-block;
        width: 8rpx;
        height: 40rpx;
        margin-right: 20rpx;
        content: '';
        background-color: rgb(209, 48, 46);
      }
    }
  }
}

.author-tips-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 600rpx;
  // height: 600rpx;
  border-radius: 40rpx;

  .tip {
    z-index: 1;
    margin-top: -40rpx;
    font-size: 28rpx;
    color: rgb(174, 174, 174);
  }

  .button {
    box-sizing: border-box;
    width: 500rpx;
    margin: 100rpx 0 50rpx 0;
    color: white;
    background-color: rgb(209, 48, 46);

    &:active {
      background-color: rgb(253, 124, 122);
    }
  }
}

#article-qrcode {
  position: relative;
  width: 690rpx;
  height: 920rpx;

  .top {
    position: relative;
    height: 500rpx;

    .logo {
      position: absolute;
      top: 20rpx;
      left: 20rpx;
      width: 190rpx;
      height: 54rpx;
    }

    .tip {
      position: absolute;
      top: 90rpx;
      left: 94rpx;
      width: 502rpx;
      height: 157rpx;
    }

    .title {
      position: absolute;
      top: 450rpx;
      left: 50%;
      font-size: 30rpx;
      color: white;
      transform: translateX(-50%);
    }
  }

  .bottom {
    .qrcode {
      position: absolute;
      top: 530rpx;
      left: 460rpx;
      width: 200rpx;
      height: 200rpx;
    }

    .avatar {
      position: absolute;
      top: 530rpx;
      left: 30rpx;
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
    }

    .username {
      position: absolute;
      top: 580rpx;
      left: 160rpx;
      font-size: 30rpx;
      font-weight: 600;
      line-height: 42rpx;
    }

    .button {
      position: absolute;
      top: 780rpx;
      left: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 600rpx;
      height: 100rpx;
      color: white;
      background-color: rgb(209, 48, 46);
      border-radius: 20rpx;
      transform: translateX(-50%);
    }
  }
}

.account-list {
  display: flex;
  flex-wrap: wrap;

  .account-list-item {
    box-sizing: border-box;
    display: flex;
    flex: 0 0 30%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 20rpx 10rpx;
    border-radius: 20rpx;

    &:active {
      box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
    }
  }
}

:deep(.wd-popup) {
  border-radius: 10rpx;
}
</style>
