<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    enablePullDownRefresh: false,
  },
}
</route>

<template>
  <view
    class="h-full pb-120px bg-[url(https://sacdn.850g.com/football/static/official_bg.png)] bg-cover bg-center"
  >
    <wd-navbar left-arrow title="关注公众号" @click-left="handleClickLeft"></wd-navbar>
    <view class="flex flex-col items-center text-white">
      <text class="mt-16rpx mb-40rpx text-26rpx">关注“神鱼赛事”公众号，全球赛事不迷路</text>
      <text class="mt-180rpx mb-10rpx text-90rpx leading-126rpx tracking-[-7.2rpx] font-bold">
        神鱼体育
      </text>
      <text class="text-80rpx leading-112rpx font-bold">专业赛事分析</text>
      <text class="mt-18rpx text-28rpx leading-42rpx">扫一扫关注“神鱼赛事”公众号</text>
      <text class="mb-18rpx text-28rpx leading-42rpx">或点击复制公众号到微信进行搜索</text>
      <image
        src="https://sacdn.850g.com/football/static/official-qrcode.jpg"
        mode="scaleToFill"
        class="w-350rpx h-350rpx rounded-12rpx"
      />
      <text
        @click="copy"
        class="center w-350rpx h-88rpx mt-40rpx text-30rpx leading-42rpx bg-[url(https://sacdn.850g.com/football/static/official-btn.png)] bg-cover [text-shadow:_0_4px_4px_rgba(0,0,0,0.2)]"
      >
        <!-- <a href="weixin://profile/gh_c37bc18372b3">点击复制公众号</a> -->
        点击复制公众号
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { officialLog } from '@/api/partner'
function handleClickLeft() {
  uni.navigateTo({ url: '/pages/third/competition' })
}

function copy() {
  uni.setClipboardData({
    data: '神鱼体育',
    success() {
      window.open('weixin://')
      officialLog(1)
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    },
  })
}
</script>
