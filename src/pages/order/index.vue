<route lang="json5">
{
  style: {
    navigationBarTitleText: '充值订单',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2">
    <view class="py-2">
      <wd-calendar
        use-default-slot
        v-model="curDate"
        @confirm="changeDate"
        placeholder="请选择日期"
      >
        <wd-input
          v-model="params.createTime"
          suffix-icon="fill-arrow-down"
          class="px-2 py-2 rounded-[20rpx]"
          readonly
          no-border
          placeholder="请选择日期"
        />
      </wd-calendar>
    </view>
    <wd-table :data="dataList" :stripe="false">
      <wd-table-col prop="createTime" label="充值时间" align="center" width="40%" />
      <wd-table-col prop="goldNum" label="获得鱼币" align="center" width="30%">
        <template #value="{ row }">
          {{ (Number(row.goldNum) || 0) + (Number(row.presentGold) || 0) }}
        </template>
      </wd-table-col>
      <wd-table-col prop="payAmount" label="支付金额" align="center" width="30%" />
    </wd-table>
    <!-- <wd-pagination v-model="params.pageNo" :total="total" @change="handlePageChange" /> -->
  </view>
  <back />
</template>
<script lang="ts" setup>
import { getMyOrderList } from '@/service/userService'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'

const dataList = ref([])
const curDate = ref(null)
const loadmoreState = ref('finished')
const total = ref(0)
const params = reactive({
  pageNo: 1,
  pageSzie: 100,
  createTime: '',
})

const changeDate = () => {
  params.pageNo = 1
  dataList.value = []
  getData()
}

const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  params.pageNo = 1
  getData()
})

const getData = async () => {
  if (curDate.value) {
    params.createTime = dayjs(curDate.value).format('YYYY-MM-DD')
  }
  const data = await getMyOrderList(params)
  loadmoreState.value = 'finished'
  if (isRefresh.value) {
    dataList.value = data
    // 提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  } else {
    if (Array.isArray(data)) {
      dataList.value = [...dataList.value, ...data]
    }
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

const handlePageChange = ({ value }) => {
  params.pageNo = value
  getData()
}

const loadmore = () => {
  setTimeout(() => {
    params.pageNo = params.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}

// onReachBottom(() => {
//   if (dataList.value.length < total.value) {
//     loadmore()
//   } else if (dataList.value.length >= total.value) {
//     uni.showToast({
//       icon: 'none',
//       title: '没有更多数据了',
//     })
//   }
// })
onMounted(() => {
  getData()
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-icon-arrow-right) {
  display: none !important;
}
</style>
