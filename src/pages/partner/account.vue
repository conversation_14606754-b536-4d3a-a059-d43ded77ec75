<route lang="json5">
{
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="h-full pt-20rpx bg-#F4F8FA">
    <view class="flex flex-col mb-40rpx bg-white">
      <wd-form :model="formData" :rules="rules" ref="formRef">
        <view
          class="relative flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-10 text-30rpx"
        >
          <text
            class="relative flex items-center w-200rpx text-black text-opacity-90 before:content-['*'] before:text-#fa4350 before:text-34rpx"
          >
            手续费
          </text>
          <view class="flex-1 flex justify-between items-center">
            <text class="text-black text-opacity-30">
              {{ formData ? formData.commissionRate : 0 }}
            </text>
            <text class="text-black text-opacity-90">%</text>
          </view>
        </view>
        <!-- 提现账号 -->
        <view
          class="relative flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-10 text-30rpx"
        >
          <text
            class="relative flex items-center w-200rpx text-black text-opacity-90 before:content-['*'] before:text-#fa4350 before:text-34rpx"
          >
            提现账号
          </text>
          <wd-input
            prop="account"
            custom-class="account-input"
            placeholder="请输入提现账号"
            v-model="formData.account"
          />
        </view>
        <!-- 姓名 -->
        <view
          class="relative flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-10 text-30rpx"
        >
          <text
            class="relative flex items-center w-200rpx text-black text-opacity-90 before:content-['*'] before:text-#fa4350 before:text-34rpx"
          >
            姓名
          </text>
          <wd-input
            prop="name"
            custom-class="account-input"
            placeholder="请输入姓名"
            v-model="formData.name"
          />
        </view>
        <!-- 身份证号码 -->
        <view
          class="relative flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-10 text-30rpx"
        >
          <text
            class="relative flex items-center w-200rpx text-black text-opacity-90 before:content-['*'] before:text-#fa4350 before:text-34rpx"
          >
            身份证号码
          </text>
          <wd-input
            prop="idNo"
            custom-class="account-input"
            placeholder="请输入身份证号码"
            v-model="formData.idNo"
          />
        </view>
        <!-- 开户银行 -->
        <view
          v-if="formData.settleType === 2"
          class="relative flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-10 text-30rpx"
        >
          <text
            class="relative flex items-center w-200rpx text-black text-opacity-90 before:content-['*'] before:text-#fa4350 before:text-34rpx"
          >
            开户银行
          </text>
          <wd-picker
            prop="bankId"
            :columns="bankList"
            custom-class="bank-picker"
            v-model="formData.bankId"
            :rules="[{ required: true, message: '请输入银行卡账号' }]"
          />
        </view>
      </wd-form>
    </view>
    <view class="text-center mx-30rpx">
      <wd-button block custom-class="submit" @click="submit">保存设置</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  IPartnerSettlementParams,
  getPartnerSettlement,
  updatePartnerSettlementInfo,
} from '@/api/partner'
import { getBankListData } from '@/service/userService'
import { ID_CARD_REG } from '@/utils/regex'
import { omit } from 'lodash-es'

const formData = ref<IPartnerSettlementParams & { commissionRate: number }>({
  settleType: 1,
  account: '',
  name: '',
  idNo: '',
  bankId: null,
  commissionRate: 0,
})

const bankList = ref([])

const formRef = ref()

const rules = reactive({
  account: [{ required: true, message: '请输入提现账号' }],
  name: [{ required: true, message: '请输入姓名' }],
  idNo: [{ pattern: ID_CARD_REG, message: '请输入正确的身份证号码' }],
})

/* 保存账户信息 */
async function submit() {
  const { valid } = await formRef.value.validate()
  if (!valid) return

  const { bankId, commissionRate, ...rest } = formData.value
  const d = formData.value.settleType === 2 ? { ...rest, bankId } : rest

  await updatePartnerSettlementInfo(d)

  uni.showToast({ title: '提交成功', icon: 'none', duration: 60000 })

  setTimeout(() => {
    uni.hideToast()
    uni.navigateTo({ url: '/pages/partner/info' })
  }, 2000)
}

onLoad(async ({ type }: { type: string }) => {
  uni.showLoading()

  const [a, b] = await Promise.all([getPartnerSettlement(type), getBankListData()])
  if (Array.isArray(b)) bankList.value = b.map((item) => ({ label: item.bankName, value: item.id }))
  const accountTypeStr = type === '1' ? '支付宝账号' : '银行卡'
  const optStr = a.id ? '更换' : '添加'
  const { account, name, idNo, commissionRate, bankId } = a
  formData.value = { settleType: +type, account, name, idNo, bankId, commissionRate }

  uni.setNavigationBarTitle({ title: `${optStr}${accountTypeStr}` })
  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
.account-input {
  flex: 1;

  &::after {
    display: none;
  }
}

.bank-picker {
  flex: 1;

  :deep(.wd-picker__cell) {
    padding: 0;
  }
}

.submit {
  height: 100rpx !important;
  border-radius: 12rpx !important;
}
</style>
