<route lang="json5">
{
  style: {
    navigationBarTitleText: '佣金明细',
    enablePullDownRefresh: false,
  },
}
</route>

<template>
  <view class="pt-10rpx px-30rpx">
    <view
      class="flex items-center h-64rpx px-16rpx border-1rpx border-solid border-#797979 border-opacity-20 rounded-12rpx text-28rpx"
    >
      <wd-datetime-picker
        placeholder="请选择日期"
        prefix-icon="search"
        v-model="date"
        @confirm="handleDateConfirm"
        type="date"
        use-default-slot
        custom-class="search"
      >
        <view>
          <wd-icon name="search1" color="rgba(0,0,0,0.5)" />
          <template v-if="date">
            <text class="ml-10rpx mr-auto text-black text-opacity-90">{{ dateStr }}</text>
          </template>
          <template v-else>
            <text class="ml-10rpx text-black text-opacity-50">请选择日期</text>
          </template>
        </view>
      </wd-datetime-picker>
      <wd-icon name="clear" size="30rpx" @click="clearDate" />
    </view>
    <template v-if="isEmpty(data)">
      <view>
        <view class="flex flex-col items-center pt-110rpx pb-64rpx">
          <image :src="emptyImg" class="w-488rpx h-266rpx" />
          <text class="mt-30rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view
        :key="id"
        v-for="{ id, title, userName, rewardAmount, createTime } in data"
        class="flex justify-between items-center py-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
      >
        <view class="flex flex-col gap-y-10rpx">
          <text>{{ title }}</text>
          <view class="flex gap-x-40rpx text-26rpx text-black text-opacity-30">
            <text>购买人:{{ userName }}</text>
            <text>{{ dateTime(createTime) }}</text>
          </view>
        </view>
        <text class="text-30rpx text-#D1302E">{{ rewardAmount }}佣金</text>
      </view>
    </template>
  </view>
</template>

<script lang="ts" setup>
import { IPartnerCommissionItem, getPromotionRewardPage } from '@/api/partner'
import { isEmpty } from 'lodash-es'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
import { formatDataTime, formatDate } from '@/utils/format'
import emptyImg from '@/static/images/empty.png'

const data = ref<IPartnerCommissionItem[]>([])
const pageNo = ref(0)
const total = ref(0)
const totalPage = computed(() => Math.ceil(total.value / DEFAULT_PAGE_SIZE))

const date = ref<number>()

const dateStr = computed(() => (date.value ? formatDate(date.value) : ''))

const dateTime = computed(() => {
  return (timestamp: number) => (timestamp ? formatDataTime(timestamp) : '')
})

function clearDate() {
  if (data.value) {
    date.value = undefined
    getData()
  }
}

async function getData(loadMore = false, date?: string) {
  uni.showLoading()
  if (loadMore) {
    const { list, total: t } = await getPromotionRewardPage(
      pageNo.value + 1,
      DEFAULT_PAGE_SIZE,
      date,
    )
    data.value = [...data.value, ...list]
    total.value = t
  } else {
    const { list, total: t } = await getPromotionRewardPage(1, DEFAULT_PAGE_SIZE, date)
    data.value = list
    total.value = t
    if (t) pageNo.value = pageNo.value + 1
  }

  // data.value = Array.from({ length: 10 }, (_, id) => ({
  //   id,
  //   title: '这里显示用户昵称',
  //   userName: 'Fuck you',
  //   rewardAmount: 98,
  //   createTime: Date.now()
  // }))

  uni.hideLoading()
}

onReachBottom(() => {
  if (pageNo.value >= totalPage.value) {
    uni.showToast({ title: '没有更多数据', icon: 'none' })
    return
  }
  getData(true, dateStr.value)
})

function handleDateConfirm() {
  getData(false, dateStr.value)
}

onReachBottom(() => {
  if (pageNo.value >= totalPage.value) {
    uni.showToast({ title: '没有更多数据', icon: 'none' })
    return
  }
  getData(true, dateStr.value)
})

onLoad(async () => {
  getData()
})
</script>

<style lang="scss" scoped>
.search {
  flex: 1;
}
</style>
