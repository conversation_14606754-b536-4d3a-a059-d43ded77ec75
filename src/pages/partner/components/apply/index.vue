<template>
  <view class="min-h-full bg-#F4F8FA">
    <wd-gap bg-color="bg-#F4F8FA" height="20rpx" />
    <wd-form ref="formRef" :rules="rules" :model="formData">
      <wd-input
        label="姓名"
        label-width="150rpx"
        prop="name"
        clearable
        placeholder="请输入姓名"
        v-model="formData.name"
      />
      <wd-input
        label="身份证"
        label-width="150rpx"
        prop="idCard"
        clearable
        placeholder="请输入身份证号码"
        v-model="formData.idCard"
      />
      <wd-input
        label="联系电话"
        required
        label-width="150rpx"
        prop="phone"
        clearable
        placeholder="请输入联系电话"
        v-model="formData.phone"
      />
      <!-- 身份证正反面 -->
      <view class="flex flex-col pt-30rpx pl-30rpx">
        <text
          class="mb-20rpx before:content-['*'] before:text-#fa4350 before:text-34rpx text-26rpx"
        >
          上传身份证
        </text>
        <view class="flex gap-x-20rpx">
          <wd-upload
            ref="uploadRef"
            :file-list="formData.imgUrlFront"
            :header="uploadHearder"
            :limit="1"
            image-mode="aspectFill"
            :action="action"
            @success="successUploadFontImg"
          >
            <template #default>
              <view
                class="flex flex-col justify-center items-center gap-y-5rpx w-190rpx h-154rpx bg-[rgba(0,0,0,0.04)] rounded-4rpx"
              >
                <image
                  src="https://sacdn.850g.com/football/static/icons/camera.svg"
                  class="w-68rpx h-68rpx"
                />
                <text class="text-26rpx text-black text-opacity-30">身份证正面</text>
              </view>
            </template>
          </wd-upload>
          <wd-upload
            ref="uploadRef"
            :file-list="formData.imgUrlBack"
            :header="uploadHearder"
            :limit="1"
            image-mode="aspectFill"
            :action="action"
            @success="successUploadBackImg"
          >
            <template #default>
              <view
                class="flex flex-col justify-center items-center gap-y-5rpx w-190rpx h-154rpx bg-[rgba(0,0,0,0.04)] rounded-4rpx"
              >
                <image
                  src="https://sacdn.850g.com/football/static/icons/camera.svg"
                  class="w-68rpx h-68rpx"
                />
                <text class="text-26rpx text-black text-opacity-30">身份证反面</text>
              </view>
            </template>
          </wd-upload>
        </view>
      </view>
      <!-- 形象照 -->
      <view class="flex flex-col pt-30rpx pl-30rpx">
        <view class="flex items-center text-26rpx mb-20rpx">
          <text class="before:content-['*'] before:text-#fa4350 before:text-34rpx">
            上传本人形象照
          </text>
          <text class="text-black text-opacity-30">（本人形象照用于头像）</text>
        </view>
        <wd-upload
          ref="uploadRef"
          :file-list="formData.headshot"
          :header="uploadHearder"
          :limit="1"
          image-mode="aspectFill"
          :action="action"
          @success="successUploadAvatarImg"
        >
          <template #default>
            <view
              class="flex flex-col justify-center items-center gap-y-5rpx w-190rpx h-154rpx bg-[rgba(0,0,0,0.04)] rounded-4rpx"
            >
              <image
                src="https://sacdn.850g.com/football/static/icons/camera.svg"
                class="w-68rpx h-68rpx"
              />
              <text class="text-26rpx text-black text-opacity-30">本人照片</text>
            </view>
          </template>
        </wd-upload>
      </view>
      <view class="mt-40rpx text-center">
        <wd-button @click="submit">提交申请</wd-button>
      </view>
    </wd-form>
  </view>
</template>

<script setup lang="ts">
import { submitPartnerAudit } from '@/api/partner'
import { useUserStore } from '@/store'
import { ID_CARD_REG, PHONE_REG } from '@/utils/regex'
import { isEmpty } from 'lodash-es'
import { storeToRefs } from 'pinia'

const emit = defineEmits<{ (e: 'success'): void }>()

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const formRef = ref()

const formData = ref({
  name: '',
  idCard: '',
  phone: '',
  imgUrlFront: [] as { url: string }[], // 身份证正面
  imgUrlBack: [] as { url: string }[], // 身份证反面
  headshot: [] as { url: string }[], // 形象照
})

const rules = reactive({
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  idCard: [{ pattern: ID_CARD_REG, message: '请输入正确的身份证号码', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: PHONE_REG, message: '请输入正确的电话号码' },
  ],
})

const uploadHearder = computed(() => {
  return {
    Authorization: `Bearer ${userInfo.value.token}`,
  }
})

const action = computed(() => {
  return import.meta.env.VITE_UPLOAD_BASEURL
})

function successUploadFontImg({ file }) {
  const response = file.response
  const result = JSON.parse(response)
  formData.value.imgUrlFront = [{ url: result.data }]
}

function successUploadBackImg({ file }) {
  const response = file.response
  const result = JSON.parse(response)
  formData.value.imgUrlBack = [{ url: result.data }]
}

function successUploadAvatarImg({ file }) {
  const response = file.response
  const result = JSON.parse(response)
  formData.value.headshot = [{ url: result.data }]
}

async function submit() {
  const { valid } = await formRef.value.validate()
  if (!valid) return

  let errorMsg = ''
  const { imgUrlFront, imgUrlBack, headshot } = formData.value

  console.log(imgUrlFront, imgUrlBack, headshot)
  if (isEmpty(imgUrlFront)) errorMsg = '请上传身份证正面照'
  else if (isEmpty(imgUrlBack)) errorMsg = '请上传身份证背面照片'
  else if (isEmpty(headshot)) errorMsg = '请上传本人形象照'

  if (errorMsg) {
    uni.showToast({ icon: 'error', title: errorMsg })
    return
  }

  await submitPartnerAudit({
    ...formData.value,
    imgUrlFront: imgUrlFront[0].url,
    imgUrlBack: imgUrlBack[0].url,
    headshot: headshot[0].url,
  })

  emit('success')
}
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  border-bottom: 1rpx solid rgba(121, 121, 121, 0.2);
}
</style>
