<template>
  <view class="flex flex-col items-center pt-30rpx">
    <!-- <view class="pt-30rpx text-center"> -->
    <image
      src="https://sacdn.850g.com/football/static/partner/partner.png"
      class="w-250rpx h-250rpx"
    />
    <text class="my-10rpx text-30rpx text-black">合伙人申请已提交</text>
    <text class="text-26rpx text-black text-opacity-50">请耐心等待，可随时关注申请进度</text>
    <view
      class="flex justify-between items-center w-600rpx h-54rpx mt-30rpx px-20rpx rounded-12rpx bg-#FFF5E0 text-24rpx text-#623F08"
    >
      <wd-icon name="check-circle-filled" size="30rpx" color="#02BB6F" />
      <text class="ml-7rpx mr-auto">已提交</text>
      <text>{{ applyDateTime(createTime) }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { formatDateTime } from '@/utils/format'

defineProps<{ createTime: number }>()

const applyDateTime = computed(() => {
  return (timestamp: number) => formatDateTime(timestamp)
})
</script>
