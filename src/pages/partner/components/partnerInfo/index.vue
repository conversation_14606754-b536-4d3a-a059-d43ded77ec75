<template>
  <view class="flex flex-col h-full bg-white bg-opacity-0">
    <view class="header">
      <view class="flex justify-between items-center">
        <image :src="partner.avatarUrl || 'https://sacdn.850g.com/football/static/icons/default-avatar.svg'
          " class="w-80rpx h-80rpx rounded-full" />
        <text class="ml-20rpx mr-auto text-30rpx text-white">{{ partner.nickname }}</text>
        <view class="flex items-center gap-x-10rpx">
          <text class="text-26rpx text-white">UID:{{ partner.exCode }}</text>
          <wd-icon name="file-copy" size="24rpx" color="white" @click="copy" />
        </view>
      </view>
      <!-- 统计信息 -->
      <view class="flex-1 flex mt-20rpx px-30rpx pt-20rpx bg-#FFE3C0 rounded-t-12rpx">
        <view class="flex-1 flex flex-col items-center gap-y-10rpx" @click="gotoCommission">
          <text class="text-26rpx text-black text-opacity-50">累计佣金(元) ></text>
          <text class="text-40rpx text-black text-opacity-90">{{ partner.totalAmount || 0 }}</text>
        </view>
        <view class="flex-1 flex flex-col items-center gap-y-10rpx" @click="gotoPromotion">
          <text class="text-26rpx text-black text-opacity-50">推广总数 ></text>
          <text class="text-40rpx text-black text-opacity-90">{{ partner.totalCount || 0 }}</text>
        </view>
      </view>
    </view>
    <!-- content -->
    <view class="flex-1 bg-#F4F8FA rounded-t-12rpx mt-[-20rpx] p-30rpx">
      <!-- 账户信息 -->
      <view class="mb-20rpx rounded-12rpx bg-white rounded-12rpx">
        <view class="flex justify-between p-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20">
          <view class="flex flex-col gap-y-10rpx">
            <text class="text-26rpx text-black text-opacity-50 underline underline-offset-2">
              可提现(元)
            </text>
            <text class="text-40rpx text-black text-opacity-90">
              {{ partner.canWithdrawAmount || 0 }}
            </text>
          </view>
          <text class="text-26rpx text-black text-opacity-50 underline underline-offset-2" @click="gotWithdrawRecords">
            提现记录
          </text>
        </view>

        <view class="flex flex-col gap-y-30rpx p-30rpx">
          <view class="flex justify-between">
            <view class="flex flex-col">
              <text class="text-26rpx text-black text-opacity-50">待入账(元)</text>
              <text class="text-40rpx text-black text-opacity-90">{{ partner.earnings || 0 }}</text>
            </view>
            <view class="flex flex-col">
              <text class="text-26rpx text-black text-opacity-50">提现中(元)</text>
              <text class="text-40rpx text-black text-opacity-90">
                {{ partner.withdrawingAmount || 0 }}
              </text>
            </view>
            <view class="flex flex-col">
              <text class="text-26rpx text-black text-opacity-50">已提现(元)</text>
              <text class="text-40rpx text-black text-opacity-90">
                {{ partner.withdrawedAmount || 0 }}
              </text>
            </view>
          </view>
          <wd-button block custom-class="withdrew" @click="gotoWithdraw">立即提现</wd-button>
        </view>
      </view>
      <!-- 操作 -->
      <view class="p-30rpx rounded-12rpx bg-white">
        <!-- 是否公开用户 -->
        <view
          class="flex justify-between items-center pb-26rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20">
          <text
            class="flex items-center gap-x-20rpx text-28rpx text-#333 before:content-[''] before:w-6rpx before:h-30rpx before:bg-#D1302E">
            是否公开用户
          </text>
          <wd-switch v-model="partner.publicUser" :active-value="1" :inactive-value="0" size="48rpx"
            @change="changeUserPublicStatus" />
        </view>
        <!-- 声请成为作者 -->
        <view v-if="userInfo?.author !== 1"
          class="flex justify-between items-center py-26rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20">
          <text
            class="flex items-center gap-x-20rpx text-28rpx text-#333 before:content-[''] before:w-6rpx before:h-30rpx before:bg-#D1302E">
            申请成为作者
          </text>
          <wd-icon name="arrow-right" size="40rpx" color="#999" />
        </view>
        <!-- 个人信息 -->
        <view @click="gotoInfo"
          class="flex justify-between items-center py-26rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20">
          <text
            class="flex items-center gap-x-20rpx text-28rpx text-#333 before:content-[''] before:w-6rpx before:h-30rpx before:bg-#D1302E">
            个人信息
          </text>
          <wd-icon name="arrow-right" size="40rpx" color="#999" />
        </view>
        <!-- 我的UID -->
        <view @click="gotoUID"
          class="flex justify-between items-center py-26rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20">
          <text
            class="flex items-center gap-x-20rpx text-28rpx text-#333 before:content-[''] before:w-6rpx before:h-30rpx before:bg-#D1302E">
            我的UID
          </text>
          <wd-icon name="arrow-right" size="40rpx" color="#999" />
        </view>
        <!-- 联系客服 -->
        <view class="flex justify-between items-center py-26rpx">
          <text
            class="flex items-center gap-x-20rpx text-28rpx text-#333 before:content-[''] before:w-6rpx before:h-30rpx before:bg-#D1302E">
            联系客服
          </text>
          <wd-icon name="arrow-right" size="40rpx" color="#999" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { IPartnerInfo, getPartnerInfo, updatePublicUser } from '@/api/partner'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'

const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const partner = ref<IPartnerInfo>({ publicUser: 0 } as IPartnerInfo)

function copy() {
  const data = partner.value.exCode || ''
  uni.setClipboardData({
    data, // e是你要保存的内容
    success: function () {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    },
  })
}

async function getPartnerData() {
  const info = await getPartnerInfo()
  const { publicUser } = info
  partner.value = { ...info, publicUser: publicUser ? 1 : 0 }
}

/* 更新用户是否公开信息状态 */
async function changeUserPublicStatus({ value }: { value: 0 | 1 }) {
  uni.showLoading()
  await updatePublicUser(value)
  await getPartnerData()
  uni.hideLoading()
}

/* 跳转UID */
function gotoUID() {
  uni.navigateTo({ url: '/pages/partner/uid' })
}

/* 跳转个人信息页面 */
function gotoInfo() {
  uni.navigateTo({ url: '/pages/partner/info' })
}

/* 跳转到推广页面 */
function gotoPromotion() {
  uni.navigateTo({ url: '/pages/partner/promotion' })
}

function gotoCommission() {
  uni.navigateTo({ url: '/pages/partner/commission' })
}

/* 跳转提现记录页面 */
function gotWithdrawRecords() {
  uni.navigateTo({ url: '/pages/withdraw/log/index?role=partner' })
}

/* 跳转到提现页面 */
function gotoWithdraw() {
  uni.navigateTo({ url: '/pages/withdraw/partner' })
}

onLoad(async () => {
  uni.showLoading()
  await getPartnerData()
  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  flex-direction: column;
  height: 240rpx;
  padding: 30rpx 30rpx 20rpx;
  overflow: hidden;
  background-image: url('https://sacdn.850g.com/football/static/partner/top_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.withdrew {
  width: 100%;
  height: 100rpx !important;
  border-radius: 12rpx !important;
}
</style>
