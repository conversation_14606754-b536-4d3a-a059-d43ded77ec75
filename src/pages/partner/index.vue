<route lang="json5" type="partner">
{
  style: {
    navigationBarTitleText: '我是合伙人',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <template v-if="needApplyPartner">
    <apply @success="getPartnerInfo" />
  </template>
  <template v-else-if="auditInfo.status === 0">
    <!-- 审核中 -->
    <applying :create-time="auditInfo ? auditInfo.createTime : 0" />
  </template>
  <template v-else>
    <partner-info />
  </template>
</template>

<script setup lang="ts">
import { IPartnerAuditInfo, getPartnerAuditInfo } from '@/api/partner'
import apply from './components/apply/index.vue'
import applying from './components/applying/index.vue'
import partnerInfo from './components/partnerInfo/index.vue'

const auditInfo = ref<null | IPartnerAuditInfo>(null)

/* 是否需要申请合伙人 */
const needApplyPartner = computed(() => !auditInfo.value || auditInfo.value.status === -1)

async function getPartnerInfo() {
  uni.showLoading()
  const a = await getPartnerAuditInfo()
  auditInfo.value = a
  uni.hideLoading()
}

onLoad(getPartnerInfo)
</script>
