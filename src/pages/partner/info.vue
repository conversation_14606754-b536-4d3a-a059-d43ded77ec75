<route lang="json5">
{
  style: {
    navigationBarTitleText: '个人信息',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="text-30rpx text-black text-opacity-90">
    <wd-gap bg-color="#F4F8FA" height="20rpx" />
    <view
      class="flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
    >
      <text class="w-175rpx">真实姓名</text>
      <text>{{ detail.name }}</text>
    </view>
    <!-- 联系电话 -->
    <view
      @click="gotoPhoneModify"
      class="flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
    >
      <text class="w-175rpx">联系电话</text>
      <text class="mr-auto">{{ detail.phone }}</text>
      <wd-icon name="arrow-right" size="40rpx" color="#999" />
    </view>
    <!-- 身份证号 -->
    <view
      class="flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
    >
      <text class="w-175rpx">身份证号</text>
      <text class="mr-auto">{{ detail.idCard }}</text>
      <wd-icon name="arrow-right" size="40rpx" color="#999" />
    </view>
    <wd-gap bg-color="#F4F8FA" height="20rpx" />
    <!-- 支付宝账号 -->
    <view
      class="flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
      @click="gotoAccount(1)"
    >
      <text class="w-175rpx">支付宝账号</text>
      <text v-if="detail.aliAccount" class="mr-auto">{{ detail.aliAccount }}</text>
      <text v-else class="mr-auto text-30rpx text-black text-opacity-50">请绑定支付宝账号</text>
      <wd-icon name="arrow-right" size="40rpx" color="#999" />
    </view>
    <!-- 银行卡账号 -->
    <view
      class="flex items-center h-100rpx px-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
      @click="gotoAccount(2)"
    >
      <text class="w-175rpx">银行卡账号</text>
      <text v-if="detail.bankAccount" class="mr-auto">{{ detail.bankAccount }}</text>
      <text v-else class="mr-auto text-30rpx text-black text-opacity-50">请绑定银行账号</text>
      <wd-icon name="arrow-right" size="40rpx" color="#999" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { IPartnerDetailInfo, getPartnerDetail } from '@/api/partner'

const detail = ref<IPartnerDetailInfo>({} as IPartnerDetailInfo)

function gotoPhoneModify() {
  uni.navigateTo({ url: '/pages/partner/phone' })
}

function gotoAccount(type: 1 | 2) {
  uni.navigateTo({ url: `/pages/partner/account?type=${type}` })
}

onLoad(async () => {
  const partnerDetail = await getPartnerDetail()
  detail.value = partnerDetail
})
</script>
