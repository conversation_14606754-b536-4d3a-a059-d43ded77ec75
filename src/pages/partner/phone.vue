<route lang="json5">
{
  style: {
    navigationBarTitleText: '更换手机号',
    enablePullDownRefresh: false,
  },
}
</route>

<template>
  <view class="flex flex-col items-center px-60rpx">
    <image
      src="https://sacdn.850g.com/football/static/icons/phone.svg"
      class="w-100rp h-100rpx mt-30rpx"
    />
    <text class="mt-20rpx mb-30rpx text-36rpx text-black text-opacity-90">修改手机号码</text>
    <text class="text-30rpx text-black text-opacity-50">{{ `当前绑定的手机号码为${phone}` }}</text>
    <wd-input placeholder="输入新的手机号码" custom-class="new-phone" v-model="formData.mobile" />
    <wd-input placeholder="输入验证码" custom-class="verify-code" v-model="formData.code">
      <template #suffix>
        <text v-if="cutdown" class="mr-30rpx text-26rpx text-#D1302E">{{ cutdown }}</text>
        <text
          v-else
          class="mr-30rpx text-26rpx text-#D1302E underline underline-offset-2"
          @click="getVerifyCode"
        >
          获取验证码
        </text>
      </template>
    </wd-input>
    <wd-button block class="submit-btn" @click="submit">确定更换</wd-button>
  </view>
</template>

<script setup lang="ts">
import { getPartnerDetail, getPartnerPhoneVerifyCode, updatePartnerPhone } from '@/api/partner'
import { VERIFY_CODE_CUTDOWN } from '@/utils/constant'
import { PHONE_REG } from '@/utils/regex'
import { useIntervalFn } from '@vueuse/core'

const phone = ref('')

const formData = ref({ mobile: '', code: '' })

const cutdown = ref(0)

const { resume, pause } = useIntervalFn(cutdownFn, 1000)

function cutdownFn() {
  if (cutdown.value <= 0) {
    pause()
  } else {
    cutdown.value = cutdown.value - 1
  }
}

// 获取验证码
async function getVerifyCode() {
  const mobile = formData.value.mobile.trim()
  if (!mobile) {
    uni.showToast({ title: '请输入手机号码' })
    return
  }

  if (!PHONE_REG.test(formData.value.mobile)) {
    console.log(formData.value.mobile)
    uni.showToast({ title: '请输入正确的手机号码' })
    return
  }

  uni.showLoading()
  await getPartnerPhoneVerifyCode(formData.value.mobile)
  uni.hideLoading()
  cutdown.value = VERIFY_CODE_CUTDOWN
  resume()
}

/* 更换手机号码 */
async function submit() {
  const mobile = formData.value.mobile.trim()
  const code = formData.value.code.trim()

  if (!mobile) {
    uni.showToast({ title: '请输入手机号码' })
    return
  }

  if (!PHONE_REG.test(formData.value.mobile)) {
    console.log(formData.value.mobile)
    uni.showToast({ title: '请输入正确的手机号码' })
    return
  }

  if (!code) {
    uni.showToast({ title: '请输入验证码' })
    return
  }

  uni.showLoading()
  await updatePartnerPhone({ mobile, code })
  uni.hideLoading()
  uni.navigateBack()
}

onLoad(async () => {
  uni.showLoading()
  const detail = await getPartnerDetail()
  phone.value = detail.phone
  uni.hideLoading()
})

onUnload(pause)
</script>

<style lang="scss" scoped>
.new-phone {
  width: 100%;
  padding-left: 30rpx;
  margin: 30rpx 0 20rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;

  &::after {
    display: none;
  }
}

.verify-code {
  width: 100%;
  padding-left: 30rpx;
  margin-bottom: 40rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;

  &::after {
    display: none;
  }
}

.submit-btn {
  width: 100%;
  border-radius: 12rpx !important;
}
</style>
