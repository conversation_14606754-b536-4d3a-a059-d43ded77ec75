<route lang="json5">
{
  style: {
    navigationBarTitleText: '申请成为合伙人',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view
    class="flex justify-center items-end w-750rpx h-2492rpx pb-226rpx bg-[url(https://sacdn.850g.com/football/static/partner/partner-poster_bg.png)] bg-cover bg-center"
  >
    <text
      @click="gotoApply"
      class="center w-660rpx h-122rpx bg-[url(https://sacdn.850g.com/football/static/poster-btn.png)] bg-cover text-36rpx text-white"
    >
      申请成为合伙人
    </text>
  </view>
</template>

<script setup lang="ts">
function gotoApply() {
  uni.navigateTo({ url: '/pages/partner/index' })
}
</script>
