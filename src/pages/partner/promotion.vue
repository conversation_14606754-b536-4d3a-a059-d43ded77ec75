<route lang="json5" type="promotion">
{
  style: {
    navigationBarTitleText: '推广明细',
    enablePullDownRefresh: false,
  },
}
</route>

<template>
  <view class="pt-10rpx px-30rpx">
    <view
      class="flex items-center h-64rpx px-16rpx border-1rpx border-solid border-#797979 border-opacity-20 rounded-12rpx text-28rpx"
    >
      <wd-datetime-picker
        placeholder="请选择日期"
        prefix-icon="search"
        v-model="date"
        @confirm="handleDateConfirm"
        type="date"
        use-default-slot
        custom-class="search"
      >
        <view>
          <wd-icon name="search1" color="rgba(0,0,0,0.5)" />
          <template v-if="date">
            <text class="ml-10rpx mr-auto text-black text-opacity-90">{{ dateStr }}</text>
          </template>
          <template v-else>
            <text class="ml-10rpx text-black text-opacity-50">请选择日期</text>
          </template>
        </view>
      </wd-datetime-picker>
      <wd-icon name="clear" size="30rpx" @click="clearDate" />
    </view>
    <template v-if="isEmpty(data)">
      <view>
        <view class="flex flex-col items-center pt-110rpx pb-64rpx">
          <image :src="emptyImg" class="w-488rpx h-266rpx" />
          <text class="mt-30rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
        </view>
      </view>
    </template>
    <template v-else>
      <view
        :key="id"
        v-for="{ id, avatarUrl, nickname, createTime } in data"
        class="flex items-center gap-x-20rpx py-30rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20"
      >
        <image :src="avatarUrl" class="w-100rpx h-100rpx" v-if="avatarUrl" />
        <image
          v-else
          src="https://sacdn.850g.com/football/static/icons/default-avatar2.svg"
          class="w-100rpx"
        />
        <view class="flex-1 flex flex-col justify-between h-100rpx py-6rpx">
          <text class="text-30rpx text-black text-opacity-90">{{ nickname }}</text>
          <text class="text-26rpx text-black text-opacity-30">{{ dateTime(createTime) }}</text>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup lang="ts">
import { IPartnerPromotionItem, getPromotionPage } from '@/api/partner'
import { DEFAULT_PAGE_SIZE } from '@/utils/constant'
import emptyImg from '@/static/images/empty.png'
import { isEmpty } from 'lodash-es'
import { formatDataTime, formatDate } from '@/utils/format'

const data = ref<IPartnerPromotionItem[]>([])
const date = ref<number>()
const pageNo = ref(0)
const total = ref(0)

const dateStr = computed(() => (date.value ? formatDate(date.value) : ''))

const totalPage = computed(() => Math.ceil(total.value / DEFAULT_PAGE_SIZE))

const dateTime = computed(() => {
  return (timestamp: number) => (timestamp ? formatDataTime(timestamp) : '')
})

async function getData(loadMore = false, date?: string) {
  uni.showLoading()
  if (loadMore) {
    // 触底加载更多
    const { list, total: t } = await getPromotionPage(pageNo.value + 1, DEFAULT_PAGE_SIZE, date)
    data.value = [...data.value, ...list]
    total.value = t
  } else {
    const { list, total: t } = await getPromotionPage(1, DEFAULT_PAGE_SIZE, date)
    data.value = list
    total.value = t
    if (t) pageNo.value = pageNo.value + 1
  }
  // const { list, total: t } = d
  // data.value = list
  // total.value = t
  // if (t) pageNo.value = pageNo.value + 1

  // data.value = Array.from({ length: 10 }, (_, id) => ({
  //   id,
  //   nickname: '罗全',
  //   avatarUrl: 'https://sacdn.850g.com/football/static/icons/default-avatar2.svg',
  //   createTime: Date.now()
  // }))

  uni.hideLoading()
}

function clearDate() {
  if (data.value) {
    date.value = undefined
    getData()
  }
}

function handleDateConfirm() {
  getData(false, dateStr.value)
}

onReachBottom(() => {
  if (pageNo.value >= totalPage.value) {
    uni.showToast({ title: '没有更多数据', icon: 'none' })
    return
  }
  getData(true, dateStr.value)
})

onLoad(async () => {
  getData()
})
</script>

<style lang="scss" scoped>
.search {
  flex: 1;
}
</style>
