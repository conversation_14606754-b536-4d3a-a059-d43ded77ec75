<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的UID',
    enablePullDownRefresh: false,
  },
}
</route>

<template>
  <view class="container">
    <view
      class="relative flex flex-col items-center w-600rpx h-800rpx mx-auto pt-110rpx rounded-24rpx overflow-hidden bg-white content"
    >
      <text class="mb-50rpx text-30rpx text-black text-opacity-90">保存二维码，分享给我的好友</text>
      <template v-if="loading">
        <wd-loading size="300rpx" color="#D1302E" />
      </template>
      <template v-else>
        <image
          id="share-image"
          :src="shareInfo ? shareInfo.qrcode : ''"
          class="w-300rpx h-300rpx"
        />
      </template>
      <!-- 我的UID -->
      <text class="my-40rpx text-26rpx text-black text-opacity-50">
        我的UID:{{ shareInfo ? shareInfo.uid : '' }}
      </text>
      <!-- <view class="flex justify-center items-center gap-x-50rpx overflow-hidden text-26rpx text-#D1302E"> -->
      <view class="flex justify-center items-center overflow-hidden text-26rpx text-#D1302E">
        <view class="flex items-center gap-x-10rpx" @click="saveQRCode">
          <!-- <wd-icon name="download1" size="30rpx" />
          <text>保存二维码</text> -->
          <text>长按二维码进行保存</text>
        </view>
        <!-- <view class="flex items-center gap-x-10rpx">
          <image src="https://sacdn.850g.com/football/static/icons/share.svg" class="w-30rpx h-30rpx" />
          <text>分享二维码</text>
        </view> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getPartnerShareInfo } from '@/api/partner'
// import html2canvas from 'html2canvas'

const loading = ref(false)
const shareInfo = ref<{ id: number; uid: string; qrcode: string }>()

// const contentClazz = computed(() => {
//   return "before:content-[''] before:absolute before:left-[-50rpx] before:top-200rpx before:w-100rpx before:h-100rpx before:opacity-0 before:rounded-full after:content-[''] after:absolute after:right-[-50rpx] after:top-200rpx after:w-100rpx after:h-100rpx after:bg-white after:bg-opacity-0 after:rounded-full"
// })

async function saveQRCode() {
  if (!shareInfo.value || !shareInfo.value.qrcode) return
  // const scoreimg = document.getElementById('share-image')
  // 使用 html2canvas 将 DOM 元素转换为图片
  // const canvas = await html2canvas(scoreimg, {
  //   scale: window.devicePixelRatio || 2,
  //   useCORS: true, // 处理跨域问题
  //   allowTaint: true,
  //   logging: false,
  //   backgroundColor: null,
  // })

  // const base64 = canvas.toDataURL('image/svg')
  // console.log('base64',base64)
  // uni.downloadFile({ url: base64 })
  // #ifdef APP-PLUS
  // console.log('h5')
  // #endif
  // const a = document.createElement('a')
  // a.download = 'qrcode.png'
  // a.href = shareInfo.value.qrcode
  // a.target = '_blank'
  // document.body.appendChild(a)
  // a.click()
  // a.remove()

  // uni.downloadFile({
  //   url: shareInfo.value.qrcode,
  //   success: (success) => {
  //     console.log(success)
  //   },
  //   fail: (fail) => {
  //     console.log(fail)
  //   },
  // })
}

onLoad(async () => {
  loading.value = true
  const d = await getPartnerShareInfo()
  shareInfo.value = d
  loading.value = false
})
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  padding-top: 230rpx;
  background-image: url('https://sacdn.850g.com/football/static/partner/uid_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.content {
  -webkit-mask: radial-gradient(circle at 0 270rpx, #0000 20px, red 0),
    radial-gradient(circle at right 270rpx, #0000 20px, red 0);
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: 0, 100%;
  -webkit-mask-size: 51%;
}
</style>
