<route lang="json5">
{
  style: {
    navigationBarTitleText: '特权设置',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="container">
    <!-- 顶部切换栏 -->
    <!-- <view class="switch-tabs">
      <view class="tab" :class="{ active: activeTab === 'time' }" @click="activeTab = 'time'">
        包时
      </view>
      <view class="tab" :class="{ active: activeTab === 'count' }" @click="activeTab = 'count'">
        包次
      </view>
    </view> -->

    <!-- 套餐列表 -->
    <!-- <view class="package-list">
      <view class="package-item" v-for="item in packages" :key="item.id">
        <view class="package-info">
          <text class="package-title">{{ item.title }}</text>
          <text class="package-price">{{ item.price }}</text>
        </view>
        <view class="package-actions">
          <text class="action modify" @click="modifyPackage(item)">修改</text>
          <text class="action delete" @click="deletePackage(item)">删除</text>
        </view>
      </view>
    </view> -->
    <wd-tabs v-model="type" @change="tabChange" class="package-list">
      <block v-for="tab in tabs" :key="tab.type">
        <wd-tab :title="tab.name">
          <view
            class="package-item"
            v-for="(item, index) in itemList"
            :key="index"
            style="border-bottom: 1px solid rgba(121, 121, 121, 0.2)"
          >
            <view class="package-info flex justify-between align-center">
              <view class="package-title" v-if="item.id">
                {{ item.days }}{{ type === 0 ? '天' : '次'
                }}{{ item.matchType !== 0 ? getMatchName(item.matchType) : '' }}套餐
              </view>

              <text class="package-price">
                <!-- <img
                  src="https://sacdn.850g.com/football/static/gold.svg"
                  style="width: 50rpx; height: 35rpx; vertical-align: middle"
                /> -->
                <image class="w-[40rpx] h-[40rpx] ml-auto" src="/static/images/gold.svg" />
                {{ item.price }}
              </text>
            </view>
            <view class="package-actions">
              <text class="action modify" @click="modifyPackage(item)">修改</text>
              <text class="action delete" @click="deletePackage(item, index)">删除</text>
              <text class="action generate" @click="generateQrcode(item.id)">生成二维码</text>
            </view>
          </view>
        </wd-tab>
      </block>
    </wd-tabs>
    <!-- 添加按钮 -->
    <view class="add-button">
      <text @click="addPackage">添加套餐</text>
    </view>
  </view>

  <!-- 底部弹窗 -->
  <wd-popup v-model="showEditDialog" position="bottom" round>
    <view class="edit-dialog">
      <view class="dialog-header">
        {{ dialogTitle }}
        <wd-icon name="close" @click="showEditDialog = false"></wd-icon>
      </view>

      <view class="form-item with-border">
        <text class="label" v-if="type === 0">天数</text>
        <text class="label" v-if="type === 1">次数</text>
        <wd-input
          v-model="currentItem.days"
          placeholder="填写"
          type="number"
          class="input-field"
        ></wd-input>
      </view>

      <view class="form-item with-border">
        <text class="label">价格</text>
        <wd-input
          v-model="currentItem.price"
          placeholder="填写"
          type="digit"
          class="input-field"
        ></wd-input>
      </view>

      <view class="form-item" v-if="currentItem.id === null">
        <text class="label">类型</text>
        <!-- 类型选择器 -->
        <wd-picker
          class="type-select"
          v-model="currentItem.matchType"
          :columns="[matchTypes]"
          label-key="name"
          value-key="id"
        ></wd-picker>
      </view>

      <view class="dialog-footer">
        <wd-button type="primary" block @click="confirmEdit" class="confirm-btn">确定</wd-button>
      </view>
    </view>
  </wd-popup>
  <wd-popup v-model="showQrcode" custom-style="border-radius:32rpx;">
    <view class="w-[600rpx] h-[800rpx] flex justify-center items-center flex-col">
      <image style="width: 400rpx; height: 400rpx" :src="qrcode" />
      <wd-button size="large" custom-style="margin-top: 50rpx">长按保存二维码</wd-button>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import {
  savePrivilegeSet,
  delPrivilegeSet,
  getPrivilegeSet,
  getUserInfo,
  getMatchTypes,
} from '@/service/userService'
import { getPrivilegeQrCode } from '@/api/author'
import { useUserStore } from '@/store'
import back from '@/components/back/index.vue'
import { useMessage } from 'wot-design-uni'
const message = useMessage()

const userStore = useUserStore()
const itemList = ref([])
const qrcode = ref('')
const showQrcode = ref(false)
const tabs = ref([
  {
    type: 0,
    name: '包时特权',
  },
  {
    type: 1,
    name: '包次特权',
  },
])

const matchTypes = ref([])

const getMatchName = (type) => {
  const match = matchTypes.value.find((item) => item.id === type)
  return match ? match.name : ''
}

// 当前选中的类型名称
const getSelectedTypeName = () => {
  if (currentItem.value.matchType === 0) return '全部'
  const selected = matchTypes.value.find((item) => item.id === currentItem.value.matchType)
  return selected?.name || '请选择'
}

const generateQrcode = async (id) => {
  if (id) {
    const result = await getPrivilegeQrCode(id)
    qrcode.value = result
    showQrcode.value = true
  } else {
    uni.showToast({
      title: '请先保存套餐再生成二维码',
      icon: 'none',
    })
  }
}

// // 处理类型选择确认
// const handleTypeConfirm = ({ value }: { value: MatchType[] }) => {
//   if (value && value.length > 0) {
//     currentItem.value.matchType = value[0].type
//   } else {
//     currentItem.value.matchType = 0
//   }
// }

const type = ref(0)
const add = () => {
  itemList.value.push({
    id: null,
    days: 0,
    price: null,
    type: type.value,
  })
}
const tabChange = (val) => {
  console.log('切换', val)
  type.value = val.index
  getData()
}
const getData = async () => {
  const data = await getPrivilegeSet(type.value)
  itemList.value = data
}
const modifyPackage = async (item) => {
  dialogTitle.value = '修改包时套餐'
  if (type.value === 1) {
    dialogTitle.value = '修改包次套餐'
  }
  currentItem.value = item
  showEditDialog.value = true
}
const deletePackage = async (id, index) => {
  if (id) {
    message.confirm('确定删除？').then(async () => {
      await delPrivilegeSet(id)
      getData()
    })
  } else {
    itemList.value.splice(index, 1)
  }
}

const dialogTitle = ref()

const currentItem = ref({
  id: null,
  days: '',
  price: '',
  type: 0,
  matchType: 0,
})
const showEditDialog = ref(false)
const addPackage = () => {
  dialogTitle.value = '添加包时套餐'
  if (type.value === 1) {
    dialogTitle.value = '添加包次套餐'
  }
  currentItem.value = {
    id: null,
    days: '',
    price: '',
    type: type.value,
    matchType: 0,
  }
  showEditDialog.value = true
}

const confirmEdit = async () => {
  const data = await savePrivilegeSet(currentItem.value)
  if (data) {
    uni.showToast({ title: '保存成功', icon: 'none' })
    getData()
    const userInfoResult = await getUserInfo()
    userStore.setUserInfo(userInfoResult)
    showEditDialog.value = false
  }
}

const getMatchType = async () => {
  const data = await getMatchTypes()
  matchTypes.value = data
}

onPullDownRefresh(() => {
  console.log('下拉刷新')
  uni.showToast({ title: '刷新成功', icon: 'none' })
  uni.stopPullDownRefresh()
})

onMounted(async () => {
  getMatchType()
  getData()
})
</script>

<style>
/* 全局样式 */
page {
  font-size: 28rpx;
  background-color: #f5f5f5;
}

.container {
  padding: 20rpx;
}
/* 切换标签样式 */
.switch-tabs {
  display: flex;
  margin-bottom: 20rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 10rpx;
}

.tab {
  flex: 1;
  padding: 20rpx;
  color: #e6dfdf;
  text-align: center;
}

.tab.active {
  position: relative;
  font-weight: bold;
  color: #c10707;
}

.tab.active::after {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 80rpx;
  height: 4rpx;
  content: '';
  background-color: #c10707;
  transform: translateX(-50%);
}
/* 套餐列表样式 */
.package-list {
  padding: 20rpx 0;
  margin-bottom: 100rpx;
}

.package-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}

.package-info {
  display: flex;
  flex-direction: column;
}

.package-title {
  margin-bottom: 10rpx;
  color: #333;
}

.package-price {
  display: flex;
  font-weight: bold;
  color: #f70707;
}

.package-actions {
  display: flex;
  margin-right: 10rpx;
}

.action {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #fa9819;
  border: 3rpx solid #fa9819;
  border-radius: 10rpx;
}

.action-btn.modify {
  width: 140rpx;
  height: 40rpx;
  color: #fa9819;
  text-align: center;
}

.action-btn.delete {
  width: 140rpx;
  height: 40rpx;
  color: #ff4444;
  text-align: center;
}

.action-btn.generate {
  width: 180rpx;
  height: 40rpx;
  color: #fa9819;
  text-align: center;
}

.action.delete {
  color: #ff4d4f;
  border: 3rpx solid #ff4444;
}
/* 添加按钮样式 */
.add-button {
  position: fixed;
  right: 20rpx;
  bottom: 40rpx;
  left: 20rpx;
  padding: 20rpx;
  font-size: 32rpx;
  color: #fff;
  text-align: center;
  background-color: #c12307;
  border-radius: 50rpx;
}
/* 弹窗样式 */
.edit-dialog {
  padding: 40rpx;
}

.dialog-header {
  position: relative;
  margin-bottom: 40rpx;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}

.dialog-header .wd-icon {
  position: absolute;
  top: 0;
  right: 0;
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.label {
  width: 120rpx;
  font-size: 30rpx;
  color: #333;
}

.input-field {
  flex: 1;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
}

.type-select {
  flex: 1;
  background: #f5f5f5;
  border-radius: 8rpx;
}

.dialog-footer {
  margin-top: 60rpx;
}

.confirm-btn {
  height: 90rpx;
  font-size: 32rpx;
  border-radius: 45rpx;
}
.form-item.with-border::after {
  position: absolute;
  right: 30rpx;
  bottom: 0;
  left: 30rpx;
  height: 1px;
  content: '';
  background-color: #eee;
  transform: scaleY(0.5);
}
</style>
