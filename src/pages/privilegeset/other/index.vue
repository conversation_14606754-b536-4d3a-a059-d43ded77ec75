<route lang="json5">
{
  style: {
    navigationBarTitleText: '套餐用户',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view>
    <view class="p-20rpx">
      <wd-input
        custom-class="search-input"
        v-model="params.userName"
        placeholder="请输入用户名"
        prefix-icon="search"
        @input="changeSearch"
      />
    </view>
    <view class="filter-buttons">
      <wd-select-picker
        v-model="params.matchType"
        placeholder="选择方案类型"
        title="选择方案类型"
        :columns="matchTypes"
        type="radio"
        label-key="name"
        value-key="id"
        style="width: 320rpx; margin-left: 40rpx"
        @confirm="changeData"
        @clear="changeData"
        clearable
        required
        custom-style="padding-right: 40rpx"
      />

      <wd-select-picker
        v-model="params.status"
        placeholder="选择状态"
        title="选择状态"
        :columns="statusOptions"
        type="radio"
        label-key="name"
        value-key="id"
        style="width: 320rpx"
        @confirm="changeData"
        @clear="changeData"
        clearable
        required
        custom-style="padding-right: 0"
      />
    </view>
    <view class="radio-group-container">
      <view class="radio-group">
        <view
          class="radio"
          :class="{ active: params.type === item.value }"
          v-for="(item, index) in radioOptions"
          :key="index"
          @click="changeType(item.value)"
        >
          {{ item.label }}
        </view>
      </view>
    </view>
    <view class="user-list">
      <view v-if="!dataList.length">
        <wd-status-tip image="search" tip="当前搜索无结果" />
      </view>
      <view v-else class="item" v-for="(item, index) in dataList" :key="index">
        <view class="top">
          <view class="left">
            <view class="avatar">
              <image
                :src="item.avatarUrl"
                style="width: 100rpx; height: 100rpx; border-radius: 50%"
              />
              <span class="tips1" v-if="item.type === 0 && item.activate === 0">待使用</span>
              <span class="tips2" v-if="item.activate === 1">使用中</span>
              <span class="tips3" v-if="item.pstatus === 1">已过期</span>
              <!-- <image
                class="tips"

                src="/static/images/dsy.png"
              />
              <image class="tips" v-if="item.activate === 1" src="/static/images/syz.png" />
              <image class="tips" v-if="item.pstatus === 1" src="/static/images/ygq.png" /> -->
            </view>
            <view class="other">
              <view class="name">{{ item.userName }}</view>
              <view class="time">
                {{ item.num }}{{ item.type === 0 ? '天' : '次'
                }}{{ item.matchType !== 0 ? getMatchName(item.matchType) : '套餐' }}卡
              </view>
            </view>
          </view>
          <view class="right">
            <view class="btn" @click="goToDetail(item)">查看明细</view>
            <view v-if="item.type == 0 && item.activate === 1">
              到期时间:
              <text>{{ item.endDate }}</text>
            </view>
            <view v-if="item.type == 1">
              剩余次数:
              <text class="color-[rgb(209,48,46)]">{{ item.num }}次</text>
            </view>
          </view>
        </view>
        <!-- <view class="bottom">
          <view>套餐类型：{{ item.type === 0 ? '包时' : '次数' }}</view>
          <view>剩余次数：{{ item.lastNum }}</view>
          <view>查看次数：{{ item.articleNum }}</view>
        </view> -->
      </view>
    </view>
    <wd-loadmore
      custom-class="loadmore"
      :state="loadmoreState"
      @reload="loadmore"
      v-if="dataList.length < total"
    />
  </view>
  <Back />
</template>
<script lang="ts" setup>
import { getAuthorPrivilegeUser } from '@/api/author'
import Back from '@/components/back/index.vue'
import { getMatchTypes } from '@/service/userService'
const loadmoreState = ref('finished')
const radioOptions = ref([
  {
    label: '全部',
    value: null,
  },
  {
    label: '包时',
    value: 0,
  },
  {
    label: '次数',
    value: 1,
  },
])

const goToDetail = (item) => {
  console.log(item)
  uni.navigateTo({
    url: `/pages/privilegeset/other/detail/index?privilegeId=${item.id}&userId=${item.userId}`,
  })
}

const statusOptions = ref([
  { name: '全部状态', id: 3 },
  { name: '使用中', id: 1 },
  { name: '待使用', id: 0 },
  { name: '已过期', id: 2 },
])

const params = ref({
  userName: null,
  type: null,
  matchType: null,
  status: null,
  pageNo: 1,
  pageSize: 10,
})

const dataList = ref([])
const total = ref(0)

const changeType = (type) => {
  params.value.type = type
  startSearch()
}

const changeSearch = ({ value }) => {
  console.log(value)
  params.value.userName = value
  startSearch()
}

const changeData = () => {
  params.value.pageNo = 1
  dataList.value = []
  console.log('aaa')
  getList()
}

const matchTypes = ref([])
const getMatchName = (type) => {
  const match = matchTypes.value.find((item) => item.id === type)
  return match ? match.name : ''
}

const getMatchType = async () => {
  const data = await getMatchTypes()
  matchTypes.value = data
}

const startSearch = () => {
  params.value.pageNo = 1
  dataList.value = []
  console.log('aaa')
  getList()
}

const loadmore = () => {
  setTimeout(() => {
    params.value.pageNo = params.value.pageNo + 1
    loadmoreState.value = 'loading'
    console.log('bbb')
    getList()
  }, 200)
}

const getList = async () => {
  const res = await getAuthorPrivilegeUser(params.value)
  dataList.value = dataList.value.concat(res.list)
  total.value = res.total
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onMounted(() => {
  getMatchType()
  getList()
})
</script>
<style lang="scss" scoped>
:deep() {
  .search-input {
    background-color: rgb(250, 250, 250);
    border: 1px solid #ccc;
    border-radius: 10rpx;
    .wd-input__prefix {
      margin-left: 10rpx;
    }
  }
}

:deep(.is-not-empty) {
  &::after {
    background-color: transparent !important;
  }
}

.radio-group-container {
  display: flex;
  justify-content: center;
  padding-top: 20rpx;
  box-shadow: 0px 3px 4px 0px rgb(233, 233, 233);
  .radio-group {
    display: flex;
    justify-content: space-around;
    width: 70%;

    .radio {
      color: rgb(175, 175, 175);
    }

    .active {
      color: rgb(212, 67, 65);

      &::after {
        display: inline-block;
        width: 100%;
        height: 2px;
        content: '';
        background-color: rgb(212, 67, 65);
      }
    }
  }
}

.item {
  padding: 20rpx 20rpx 20rpx 20rpx;
  border-bottom: 1px solid #ccc;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .avatar {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 20rpx;
        .tips1 {
          position: absolute;
          bottom: 0;
          width: 90rpx;
          height: 35rpx;
          font-size: 25rpx;
          color: #fff;
          text-align: center;
          background-color: red;
          border-radius: 20rpx;
        }
        .tips2 {
          position: absolute;
          bottom: 0;
          width: 90rpx;
          height: 35rpx;
          font-size: 25rpx;
          color: #fff;
          text-align: center;
          background-color: #debb69;
          border-radius: 20rpx;
        }
        .tips3 {
          position: absolute;
          bottom: 0;
          width: 90rpx;
          height: 35rpx;
          font-size: 25rpx;
          color: #fff;
          text-align: center;
          background-color: #000000;
          border-radius: 20rpx;
        }
      }
      .other {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        margin-left: 20rpx;
        .name {
          margin-bottom: 10rpx;
          font-weight: bold;
        }
        .time {
          width: fit-content;
          padding: 5rpx 10rpx;
          margin-top: 10rpx;
          font-size: 26rpx;
          color: rgb(220, 95, 93);
          background-color: rgb(251, 235, 235);
          border-radius: 5rpx;
        }
      }
    }
    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      margin-left: 20rpx;
      font-size: 25rpx;
      color: rgb(184, 184, 184);
      .btn {
        margin-bottom: 10rpx;
        margin-left: auto; /* 将按钮推到最右侧 */
        text-decoration: underline; /* 增加下划线 */
      }
      .info {
        display: flex;
        flex-direction: column; /* 信息部分垂直排列 */
        margin-right: 20rpx; /* 为信息部分添加一些右边距 */
      }
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20rpx;
    color: rgb(184, 184, 184);
  }
}

.filter-buttons {
  display: flex;
  gap: 20rpx;

  .filter-btn {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    padding: 16rpx;
    font-size: 28rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;

    text {
      margin-right: 8rpx;
    }
  }
}

.user-list {
  .user-card {
    padding: 24rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    border-bottom: 1px solid #ccc;
    border-radius: 12rpx;
    .user-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .username {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        margin-left: 20rpx;
      }

      .package-type {
        width: fit-content;
        padding: 5rpx 10rpx;
        margin-top: 10rpx;
        font-size: 26rpx;
        color: rgb(220, 95, 93);
        background-color: rgb(251, 235, 235);
        border-radius: 5rpx;
      }
    }
    .left {
      display: flex;
      align-items: center;
      .avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 20rpx;
      }
      .other {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        margin-left: 20rpx;
        .name {
          margin-bottom: 10rpx;
          font-weight: bold;
        }
        .time {
          width: fit-content;
          padding: 5rpx 10rpx;
          margin-top: 10rpx;
          font-size: 26rpx;
          color: rgb(220, 95, 93);
          background-color: rgb(251, 235, 235);
          border-radius: 5rpx;
        }
      }
    }

    .package-info {
      .expire-time {
        margin-bottom: 16rpx;
        font-size: 26rpx;
        color: #999;
      }

      .action-container {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .remaining {
          font-size: 28rpx;
          color: #666;

          .remaining-num {
            color: #d1302e;
          }
        }

        .view-detail {
          font-size: 26rpx;
          color: #1989fa;
        }
      }
    }
  }
}
</style>
