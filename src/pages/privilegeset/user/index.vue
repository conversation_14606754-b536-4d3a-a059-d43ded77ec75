<route lang="json5">
{
  style: {
    navigationBarTitleText: '套餐明细',
    enablePullDownRefresh: false,
  },
}
</route>

<template>
  <view class="container">
    <!-- 顶部筛选栏 -->
    <view class="filter-container">
      <view class="tabs">
        <view
          v-for="(item, index) in tabs"
          :key="index"
          :class="{ active: activeTab === index }"
          @click="changeTab(index)"
        >
          {{ item }}
        </view>
      </view>

      <view class="filter-options">
        <wd-select-picker
          v-model="params.authorId"
          placeholder="选择作者"
          title="选择作者"
          filterable
          filter-placeholder="可输入作者名称进行搜索"
          :columns="authorColumns"
          @confirm="changeData"
          type="radio"
          clearable
          required
          custom-style="padding-right: 0"
        />
        <wd-select-picker
          v-model="params.matchType"
          placeholder="选择方案类型"
          title="选择方案类型"
          :columns="matchTypes"
          @confirm="changeData"
          type="radio"
          label-key="name"
          value-key="id"
          clearable
          required
          custom-style="padding-right: 0"
        />
        <wd-select-picker
          v-model="params.status"
          placeholder="选择状态"
          title="选择状态"
          :columns="statusOptions"
          @confirm="changeData"
          type="radio"
          label-key="name"
          value-key="id"
          clearable
          required
          custom-style="padding-right: 0"
        />
      </view>
    </view>

    <!-- 套餐列表 -->
    <view class="card-list">
      <view v-if="!dataList.length" class="empty-tip">
        <wd-status-tip image="search" tip="暂无套餐数据" />
      </view>

      <view
        v-for="(item, index) in dataList"
        :key="index"
        class="card"
        :class="getStatusClass(item.activate)"
      >
        <view class="card-header">
          <text class="card-title">
            {{ getPrivilegeName(item) }}
          </text>
          <view class="expired-badge">
            <text class="expired-text">{{ getStatusName(item.activate) }}</text>
            <image
              v-if="item.activate === 2"
              class="expired-icon"
              src="/static/images/expired.png"
              mode="aspectFit"
            />
          </view>
        </view>

        <view class="card-divider"></view>

        <view class="card-content">
          <view class="info-item">
            <text class="info-author">所属作者：</text>
            <text class="info-name">{{ item.username }}</text>
          </view>

          <view class="info-item" v-if="item.activate !== 0 && item.type === 0">
            <text class="info-time">到期时间：</text>
            <text class="info-time">{{ item.endDate }}</text>
          </view>

          <view class="info-item" v-else>
            <text class="info-time">购买时间：</text>
            <text class="info-time">{{ format(item.createTime, 'YYYY-MM-DD HH:mm:ss') }}</text>
          </view>
        </view>
      </view>
    </view>

    <wd-loadmore :state="loadmoreState" @reload="loadmore" v-if="dataList.length < total" />
  </view>

  <back />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { getUserPrivilegeList } from '@/api/author'
import { format } from '@/utils/format'
import { getMatchTypes, getMyBuyPrivilegeAuthorList } from '@/service/userService'
import back from '@/components/back/index.vue'

const tabs = ref(['全部', '包时', '包次', '套餐包'])
const activeTab = ref(0)
const authorNames = ref([])

const statusOptions = ref([
  { name: '全部状态', id: 3 },
  { name: '使用中', id: 1 },
  { name: '待使用', id: 0 },
  { name: '已过期', id: 2 },
])

const params = ref({
  authorId: null,
  type: null,
  status: null,
  matchType: null,
  pageNo: 1,
  pageSize: 10,
})

const getPrivilegeName = (privilegeInfo) => {
  if (privilegeInfo.name) {
    return privilegeInfo.name
  } else {
    return (
      privilegeInfo.num +
      (privilegeInfo.type === 0 ? '天' : '次') +
      (privilegeInfo.matchType !== 0 ? getMatchName(privilegeInfo.matchType) : '') +
      '套餐'
    )
  }
}

// 监听 params 的变化
watch(
  params,
  (newParams) => {
    // 如果分页参数不是第一页，则重置分页
    if (newParams.pageNo !== 1) {
      newParams.pageNo = 1
    }

    // 清空数据列表
    dataList.value = []

    // 重新加载数据
    getList()
  },
  { deep: true },
)

const matchTypes = ref([])
const getMatchName = (type) => {
  const match = matchTypes.value.find((item) => item.id === type)
  return match ? match.name : ''
}

const getMatchType = async () => {
  const data = await getMatchTypes()
  matchTypes.value = data
}

const dataList = ref([])
const total = ref(0)
const loadmoreState = ref('finished')

const changeTab = (index) => {
  activeTab.value = index
  params.value.type = index === 1 ? 0 : index === 2 ? 1 : index === 3 ? 4 : null
  // startSearch()
}

const startSearch = () => {
  params.value.pageNo = 1
  dataList.value = []
  getList()
}

const loadmore = () => {
  params.value.pageNo += 1
  loadmoreState.value = 'loading'
  getList()
}
const changeData = () => {
  params.value.pageNo = 1
  dataList.value = []
  // getList()
}
const authorColumns = ref([])
const getAuthorList = async () => {
  const data = await getMyBuyPrivilegeAuthorList()
  authorColumns.value = data.map((item: any) => {
    return {
      label: item.authorName,
      value: item.authorId,
    }
  })
}

const getList = async () => {
  try {
    const res = await getUserPrivilegeList(params.value)
    dataList.value = dataList.value.concat(res.list)
    total.value = res.total
    loadmoreState.value = dataList.value.length < total.value ? 'loadmore' : 'finished'
  } catch (error) {
    console.error('获取套餐列表失败:', error)
    loadmoreState.value = 'error'
  }
}

const getStatusName = (status) => {
  const statusMap = {
    1: '使用中',
    0: '待使用',
    2: '已过期',
  }
  return statusMap[status] || '未知状态'
}

const getStatusClass = (status) => {
  return `status-${status}`
}

onMounted(() => {
  getMatchType()
  getAuthorList()
  getList()
})

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  }
})
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
}

.filter-container {
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;

  .tabs {
    display: flex;
    margin-bottom: 30rpx;

    view {
      flex: 1;
      padding: 20rpx 0;
      font-size: 32rpx;
      color: #666;
      text-align: center;

      &.active {
        position: relative;
        font-weight: bold;
        color: #d1302e;

        &::after {
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 80rpx;
          height: 6rpx;
          content: '';
          background-color: #d1302e;
          border-radius: 3rpx;
          transform: translateX(-50%);
        }
      }
    }
  }

  .filter-options {
    display: flex;
    justify-content: space-between;

    .author-input {
      flex: 1;
      width: 100rpx;
      padding: 16rpx 24rpx;
      margin-right: 20rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
    }

    .filter-item {
      display: flex;
      align-items: center;
      padding: 16rpx 24rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
      border-radius: 8rpx;

      text {
        margin-right: 10rpx;
      }
    }
  }
}

.card-list {
  .empty-tip {
    padding: 100rpx 0;
  }

  .card {
    position: relative;

    margin-bottom: 20rpx;
    overflow: hidden;
    background-color: #fff;
    border-radius: 16rpx;

    &.status-1 {
      background: linear-gradient(to right bottom, #fefcfb, #ffd7d0);

      .card-header {
        .card-status {
          color: #d1302e;
        }

        .card-title {
          color: #d1302e;
        }
      }

      .expired-text {
        position: relative;
        z-index: 2;
        font-size: 26rpx;
        font-weight: bold;
        color: #d1302e;
        transform: translateY(-150rpx);
      }

      .card-divider {
        background-color: linear-gradient(to right bottom, #fefcfb, #ffd7d0);
      }

      .card-content {
        padding: 24rpx 30rpx;

        .info-item {
          display: flex;
          margin-bottom: 20rpx;
          font-size: 28rpx;

          .info-author {
            color: #d1302e;
          }

          .info-name {
            color: #d1302e;
          }

          .info-time {
            color: #999;
          }

          &:last-child {
            margin-bottom: 0;
          }

          text:first-child {
            width: 160rpx;
          }
        }
      }
    }

    &.status-0 {
      background: linear-gradient(to right bottom, #fefcfb, #fff1cd);

      .card-header {
        .card-status {
          color: #b16912;
        }

        .card-title {
          color: #b16912;
        }
      }

      .expired-text {
        position: relative;
        z-index: 2;
        font-size: 26rpx;
        font-weight: bold;
        color: #b16912;
        transform: translateY(-150rpx);
      }

      .card-divider {
        background-color: rgba(25, 137, 250, 0.2);
      }

      .card-content {
        padding: 24rpx 30rpx;
        color: #333;

        .info-item {
          display: flex;
          margin-bottom: 20rpx;
          font-size: 28rpx;
          color: #666;

          .info-author {
            color: #b16912;
          }

          .info-name {
            color: #b16912;
          }

          .info-time {
            color: #999;
          }

          &:last-child {
            margin-bottom: 0;
          }

          text:first-child {
            width: 160rpx;
          }
        }
      }
    }

    .expired-badge {
      position: absolute;
      right: 20rpx;
      bottom: 20rpx;
      display: flex;
      justify-content: center;

      .expired-icon {
        position: absolute;
        z-index: 1;
        width: 120rpx;
        height: 120rpx;
        transform: translateY(-80rpx);
      }
    }
    /* 已过期卡片整体样式 */
    &.status-2 {
      position: relative;
      /* 为绝对定位的标签提供参照 */
      background-color: #f8f8f8;

      .card-header {
        .card-title {
          color: #999 !important;
        }

        .card-status {
          display: none;
          /* 隐藏原来的状态文字 */
        }
      }

      .expired-text {
        position: relative;
        z-index: 2;
        font-size: 26rpx;
        font-weight: bold;
        color: #999;
        transform: translateY(-150rpx);
      }

      .card-content {
        padding: 24rpx 30rpx;
        color: #333;

        .info-item {
          display: flex;
          margin-bottom: 20rpx;
          font-size: 28rpx;
          color: #666;

          .info-author {
            color: #999;
          }

          .info-name {
            color: #999;
          }

          .info-time {
            color: #999;
          }

          &:last-child {
            margin-bottom: 0;
          }

          text:first-child {
            width: 160rpx;
          }
        }
      }
    }

    // &.status-2 {
    //   background-color: rgba(153, 153, 153, 0.1);

    //   .card-header {
    //     .card-title, .card-status {
    //       color: #999;
    //     }
    //   }

    //   .card-divider {
    //     height: 1px;
    //     margin: 0 30rpx;
    //     background-color: rgba(153, 153, 153, 0.2);
    //     background-color: #eee;
    //   }

    //   .card-content {
    //     padding: 24rpx 30rpx;
    //     color: #999;
    //     text:first-child {
    //       color: #999;
    //     }

    //     .info-item {
    //       display: flex;
    //       margin-bottom: 20rpx;
    //       font-size: 28rpx;
    //       color: #666;

    //       &:last-child {
    //         margin-bottom: 0;
    //       }

    //       text:first-child {
    //         width: 160rpx;
    //         color: #999;
    //       }
    //     }
    //   }
    // }

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 30rpx;

      .card-title {
        font-size: 32rpx;
        font-weight: bold;
      }

      .card-status {
        font-size: 28rpx;
      }
    }
  }
}
</style>
