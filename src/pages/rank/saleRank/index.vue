<route lang="json5">
{
  style: {
    navigationBarTitleText: '销量排名',
  },
}
</route>
<template>
  <view
    class="rank-item"
    v-for="(item, index) in dataList"
    @click="goToDetail(item.articleId)"
    :key="index"
  >
    <view class="left">
      <view class="title w-[450rpx] overflow-ellipsis ellipsis whitespace-nowrap">
        {{ item.title }}
      </view>
      <view class="date">{{ item.createTime }}</view>
    </view>
    <view class="right">
      <view class="num">销量：{{ item.count || 0 }}</view>
      <view class="value">总额：{{ item.amount + '鱼币' }}</view>
    </view>
  </view>
  <wd-loadmore
    custom-class="loadmore"
    :state="loadmoreState"
    @reload="loadmore"
    v-if="dataList.length < total"
  />
  <back />
</template>
<script lang="ts" setup>
import { getArticleRankPage } from '@/api/article'
import back from '@/components/back/index.vue'

const dataList = ref([])
const total = ref(0)
const loadmoreState = ref('finished')

const goToDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/detail/index?id=${id}`,
  })
}

const loadmore = () => {
  setTimeout(() => {
    params.value.pageNo = params.value.pageNo + 1
    loadmoreState.value = 'loading'
    getData()
  }, 200)
}

const params = ref({
  pageNo: 1,
  pageSize: 10,
})

const isRefresh = ref(false)
const getData = async () => {
  const result = await getArticleRankPage(params.value)
  if (isRefresh.value) {
    dataList.value = result.list
    //提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  } else {
    dataList.value = dataList.value.concat(result.list)
  }
  total.value = result.total
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

onPullDownRefresh(() => {
  isRefresh.value = true
  params.value.pageNo = 1
  getData()
})

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onMounted(() => {
  getData()
})
</script>
<style lang="scss" scoped>
.rank-item {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin: 10rpx 0 10rpx;
  background-color: white;
  border: 1px solid #eee;
  border-radius: 20rpx;

  .left {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .title {
      margin-bottom: 10rpx;
    }

    .date {
      font-size: 26rpx;
      color: #999;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .num {
      margin-bottom: 10rpx;
      font-size: 28rpx;
      text-align: end;
    }

    .value {
      font-size: 26rpx;
      color: rgb(217, 0, 27);
      text-align: end;
    }
  }
}
</style>
