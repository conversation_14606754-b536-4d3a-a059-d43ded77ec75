<route lang="json5">
{
  style: {
    navigationBarTitleText: '充值',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen px-2 pb-[100rpx]">
    <view class="py-2">
      <view class="backgroudImage px-4">
        <view>
          <view class="coinStyle">当前鱼币</view>
          <view class="coinStyle1">{{ userStore.userInfo.gold || 0 }}</view>
        </view>
        <image src="https://sacdn.850g.com/football/static/pay_money.png" class="moneyImg" />
      </view>
    </view>
    <view class="inline-flex items-center mt-[20rpx]">
      <view class="recharge"></view>
      <view class="recharge-text">快捷充值</view>
      <view class="recharge-text1">(1元=1鱼币)</view>
    </view>
    <view class="recharge-content">
      <view
        class="recharge-view"
        v-for="item in mountList"
        :class="{ selected: amount === item }"
        @click="selectAmount(item)"
        :key="item"
      >
        <text class="one">{{ item }}鱼币</text>
        <text class="two" :value="item">￥{{ item }}</text>
      </view>
    </view>
    <view class="input-view">
      <wd-input
        type="number"
        v-model="amount"
        prefix-icon="Money Icon"
        placeholder="输入自定义金额"
        :noBorder="true"
        :maxlength="6"
        placeholderStyle="font-size: 36rpx;align-items: center;"
        style="
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          font-size: 50rpx;
          font-weight: 30rpx;
          background: rgba(0, 0, 0, 0.02);
          border: 1rpx solid rgba(0, 0, 0, 0.1);
          border-radius: 10rpx;
        "
      >
        <template #prefix>
          <image
            src="https://sacdn.850g.com/football/static/money_icon.png"
            alt="Money Icon"
            style="left: 20rpx; width: 20rpx; height: 28rpx; margin-right: 20rpx"
          />
        </template>
      </wd-input>
    </view>
    <view
      class="flex pl-[-30rpx] items-center justify-center"
      v-if="giftRatio > 0"
      style="margin-top: 10rpx"
    >
      <text class="font-size-[30rpx] mt-[10rpx] text-[#d1302e]">充值立即加赠{{ giftRatio }}%</text>
    </view>
    <view class="flex justify-center">
      <button class="custom-button" @click="handlePayment">
        <text style="width: 120rpx">支付({{ amount == '' ? 0 : amount }}元)</text>
      </button>
    </view>
    <view class="flex pl-[30rpx] items-center" style="margin-top: 20rpx">
      <wd-checkbox v-model="checkVal" checked-color="#d1302e"></wd-checkbox>
      <text @click="showAgreement" class="font-size-[26rpx] text-[#666]">
        我已经阅读并同意
        <text class="text-[#d1302e]">《神鱼体育购买协议》</text>
      </text>
    </view>
    <view class="flex justify-center" style="margin-top: 50rpx">
      <text class="font-size-[32rpx] text-black">充值说明</text>
    </view>
    <view
      class="flex justify-center w-[90%] mt-[30rpx]"
      style="border: solid 1rpx #ccc; border-radius: 10rpx; padding: 20rpx"
    >
      <text class="font-size-[26rpx] text-black line-height-[50rpx]">
        1. 神鱼体育
        <text class="text-[#d1302e]">非购彩平台</text>
        ，鱼币一经充值成功，只可用于消费(付费阅读)，
        <text class="text-[#d1302e]">不支持提现、退款、购彩等操作。</text>
        <br />
        2. 充值鱼币不可以用户任何形式的竞猜。
        <br />
        3. 使用充值服务前，需确认您已
        <text class="text-[#d1302e]">年满18岁</text>
        ，若您为未成年人，您使用充值服务的行为将被视为已获得监护人认可。
      </text>
    </view>
  </view>
  <back></back>
  <buyAgreement ref="buyAgreementRef" @pass="passAgreement" />
</template>
<script lang="ts" setup>
import { useUserStore } from '@/store'
import { requestPayment, requestGiftRetio } from '@/service/userService'
import { onBridgeReady } from '@/utils/wxPay'
import back from '@/components/back/index.vue'
import buyAgreement from '@/components/buyAgreement/index.vue'
import { cleanUrl } from '@/utils/sqbPay'
const userStore = useUserStore()
const amount = ref(1)
const buyAgreementRef = ref()
const checkVal = ref(false)
const mountList = [38, 98, 198, 498, 998, 1988]
const giftRatio = ref()

function selectAmount(value) {
  amount.value = value
  params.amount = value
}

const params = reactive({
  amount: 1,
  payType: 2,
  redirectUrl: '',
})

const showAgreement = () => {
  buyAgreementRef.value.showDialog()
}

const passAgreement = () => {
  checkVal.value = true
}

const getGiftRetio = async () => {
  const data = await requestGiftRetio()
  giftRatio.value = data
  console.log('giftRatio', giftRatio.value)
}

const handlePayment = async () => {
  if (!checkVal.value) {
    showAgreement()
    return
  }
  if (!amount.value) {
    uni.showToast({
      title: '请输入充值金额',
      icon: 'none',
    })
    return
  } else {
    params.amount = amount.value
  }

  params.redirectUrl = cleanUrl(window.location.href)

  const data = await requestPayment(params)

  // window.location.href = data.payUrl

  // 微信支付参数
  // const wxJsapiParams = data.wxJsapiParams

  // onBridgeReady(
  //   wxJsapiParams,
  //   () => {
  //     console.log('微信支付成功')
  //   },
  //   () => {
  //     console.log('微信支付失败')
  //   },
  // )

  const payUrl = data.payUrl

  // 跳转到支付页面
  window.location.href = payUrl
}

onMounted(() => {
  getGiftRetio()
  selectAmount(mountList[0])
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-icon-arrow-right) {
  display: none !important;
}

.backgroudImage {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 170rpx;
  background-image: url('https://sacdn.850g.com/football/static/pay_back.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  border-radius: 10rpx;
}

.coinStyle {
  font-size: 30rpx;
  color: #ffffff;
  letter-spacing: 1.5rpx;
}

.coinStyle1 {
  margin-top: 20rpx;
  font-size: 40rpx;
  color: #ffffff;
  letter-spacing: 1.5rpx;
}

.moneyImg {
  width: 120rpx;
  height: 120rpx;
}

.recharge-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  padding: 20rpx;
}

.recharge {
  /* Inside auto layout */

  left: 15rpx;
  flex: none;
  flex-grow: 0;
  order: 0;
  width: 6rpx;
  height: 50rpx;
  background: #d1302e;
}

.recharge-text {
  margin-left: 20rpx;
  font-family: 'PingFang SC';
  font-size: 40rpx;
}

.recharge-text1 {
  margin-left: 20rpx;
  font-family: 'PingFang SC';
  font-size: 36rpx;
  color: rgba(0, 0, 0, 0.5);
  text-align: center;
  mix-blend-mode: normal;
}

.recharge-view.selected {
  color: #d1302e;
  background: rgba(209, 48, 46, 0.1);
  border: 1px solid #d1302e;
}

.recharge-view {
  box-sizing: border-box;
  display: flex;
  flex: 0 0 calc(30%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  text-align: center;
  background: rgba(0, 0, 0, 0.02);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  border-radius: 12rpx;

  .one {
    font-family: 'PingFang SC';
    font-size: 30rpx;
    font-style: normal;
    text-align: center;
    letter-spacing: 2rpx;
  }

  .two {
    margin-top: 20rpx;
    font-family: 'PingFang SC';
    font-size: 30rpx;
    font-style: normal;
    text-align: center;
    letter-spacing: 2rpx;
  }
}

.input-view {
  width: 90%;
  height: 90rpx;
  margin: 0 auto;
}

.wd-input {
  box-sizing: border-box;
  outline: none;
}

.custom-button {
  bottom: 0;
  align-items: center;
  justify-content: center;
  width: 92%;
  padding: 20rpx;
  margin-top: 20rpx;
  font-family: 'PingFang SC';
  font-size: 36rpx;
  font-weight: 400;
  line-height: 55rpx;
  color: #ffffff;
  letter-spacing: 2rpx;
  background: #d1302e;
  border: 1rpx solid #ccc;
  border-radius: 12rpx;
}
</style>
