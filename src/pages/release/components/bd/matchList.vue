<template>
  <view
    v-for="match in matchList"
    :key="match.matchId"
    class="pb-35rpx border-b-2rpx border-b-solid border-b-#F3F3F3"
  >
    <view class="flex flex-col mt-25rpx mb-40rpx text-26rpx">
      <text
        class="center w-fit mb-8rpx px-8rpx h-37rpx mr-18rpx text-#D1302E bg-#FFECEE rounded-4rpx"
      >
        {{ match.shortComp }}
      </text>
      <view class="flex justify-between items-center text-26rpx">
        <text class="text-black">{{ dayjs(match.matchTime * 1000).format('MM-DD HH:mm') }}</text>
        <text class="text-black text-26rpx font-medium">
          {{ `${match.shortHomeName} VS ${match.shortAwayName}` }}
        </text>
      </view>
    </view>
    <!-- 玩法(让步) -->
    <view class="grid grid-cols-3 gap-x-20rpx h-70rpx">
      <view class="center bg-#FEF2E7 rounded-4rpx text-24rpx text-#E8AF74">让球</view>
      <view
        class="play-item"
        @click="handleMainCheck(match, PLAY_TYPE.RQ, '3')"
        :class="isMainActive(match.matchId, PLAY_TYPE.RQ, '3') ? 'isActive' : ''"
      >
        <view class="text-24rpx">
          <text>主胜</text>
          <text v-if="match.matchPlayOdds.rq" class="rq">
            {{ `(${match.matchPlayOdds.rq.split(',')[0]})` }}
          </text>
        </view>
        <text v-if="match.matchPlayOdds.rq" class="odd">
          {{ `(${match.matchPlayOdds.rq.split(',')[1]})` }}
        </text>
      </view>
      <view
        class="play-item"
        @click="handleMainCheck(match, PLAY_TYPE.RQ, '0')"
        :class="isMainActive(match.matchId, PLAY_TYPE.RQ, '0') ? 'isActive' : ''"
      >
        <text class="text-24rpx">客胜</text>
        <text v-if="match.matchPlayOdds.rq" class="odd">
          {{ `(${match.matchPlayOdds.rq.split(',')[3]})` }}
        </text>
      </view>
    </view>
    <!-- 玩法(大小球) -->
    <view class="grid grid-cols-3 gap-x-20rpx h-70rpx my-20rpx">
      <view class="center bg-#EDF9FF rounded-4rpx text-24rpx text-#4188DF">
        <text v-if="match.matchPlayOdds.dxq">
          {{ `大小球(${match.matchPlayOdds.dxq.split(',')[1]})` }}
        </text>
        <text v-else>大小球</text>
      </view>
      <view
        class="play-item"
        @click="handleMainCheck(match, PLAY_TYPE.DXQ, '3')"
        :class="isMainActive(match.matchId, PLAY_TYPE.DXQ, '3') ? 'isActive' : ''"
      >
        <text class="text-24rpx">大</text>
        <text v-if="match.matchPlayOdds.dxq" class="odd">
          {{ `(${match.matchPlayOdds.dxq.split(',')[0]})` }}
        </text>
      </view>
      <view
        class="play-item"
        @click="handleMainCheck(match, PLAY_TYPE.DXQ, '0')"
        :class="isMainActive(match.matchId, PLAY_TYPE.DXQ, '0') ? 'isActive' : ''"
      >
        <text class="text-24rpx">小</text>
        <text v-if="match.matchPlayOdds.dxq" class="odd">
          {{ `(${match.matchPlayOdds.dxq.split(',')[2]})` }}
        </text>
      </view>
    </view>
    <!-- 玩法(胜平负) -->
    <view class="grid grid-cols-3 gap-x-20rpx h-70rpx my-20rpx">
      <view class="center bg-#E6F5DD rounded-4rpx text-24rpx text-#60B53F">胜平负</view>
      <view class="col-span-2 flex gap-x-15rpx">
        <view
          class="play-item"
          @click="handleMainCheck(match, PLAY_TYPE.WIN_LOSE_DRAW, '3')"
          :class="isMainActive(match.matchId, PLAY_TYPE.WIN_LOSE_DRAW, '3') ? 'isActive' : ''"
        >
          <text class="text-24rpx">主胜</text>
          <text v-if="match.matchPlayOdds.spf" class="odd">
            {{ match.matchPlayOdds.spf.split(',')[0] }}
          </text>
        </view>
        <view
          class="play-item"
          @click="handleMainCheck(match, PLAY_TYPE.WIN_LOSE_DRAW, '1')"
          :class="isMainActive(match.matchId, PLAY_TYPE.WIN_LOSE_DRAW, '1') ? 'isActive' : ''"
        >
          <text class="text-24rpx">平</text>
          <text v-if="match.matchPlayOdds.spf" class="odd">
            {{ match.matchPlayOdds.spf.split(',')[1] }}
          </text>
        </view>
        <view
          class="play-item"
          @click="handleMainCheck(match, PLAY_TYPE.WIN_LOSE_DRAW, '0')"
          :class="isMainActive(match.matchId, PLAY_TYPE.WIN_LOSE_DRAW, '0') ? 'isActive' : ''"
        >
          <text class="text-24rpx">客胜</text>
          <text v-if="match.matchPlayOdds.spf" class="odd">
            {{ match.matchPlayOdds.spf.split(',')[2] }}
          </text>
        </view>
      </view>
    </view>
    <!-- 附赠玩法 -->
    <view class="flex items-center mt-33rpx mb-20rpx">
      <text class="text-#333 text-26rpx">附赠玩法</text>
      <text class="text-#999 text-24rpx">（结果不计入作者战绩）</text>
    </view>
    <view class="grid grid-cols-4 gap-x-14rpx h-70rpx text-#333 text-24rpx">
      <wd-badge :is-dot="isSelected(match.matchId, PLAY_TYPE.SCORE)">
        <view
          class="center h-70rpx bg-#F3F4F5 rounded-4rpx"
          @click="openModal(PLAY_TYPE.SCORE, match)"
        >
          比分
        </view>
      </wd-badge>
      <wd-badge :is-dot="isSelected(match.matchId, PLAY_TYPE.JQ)">
        <view
          class="center h-70rpx bg-#F3F4F5 rounded-4rpx"
          @click="openModal(PLAY_TYPE.JQ, match)"
        >
          进球数
        </view>
      </wd-badge>
    </view>
  </view>
  <template v-if="releaseModalType === PLAY_TYPE.SCORE">
    <score-modal
      :match="currentMatch"
      :limit="getLimit(PLAY_TYPE.SCORE)"
      :result-type="getResultType(PLAY_TYPE.SCORE)"
      ref="scoreRef"
      :category="SCHEME_CATEGORY.BD"
    />
  </template>
  <template v-else-if="releaseModalType === PLAY_TYPE.JQ">
    <jq-modal
      ref="jqRef"
      :match="currentMatch"
      :limit="getLimit(PLAY_TYPE.JQ)"
      :result-type="getResultType(PLAY_TYPE.JQ)"
      :category="SCHEME_CATEGORY.JZ"
    />
  </template>
</template>

<script setup lang="ts">
import { IGamePlayRecord } from '@/api/author'
import { IMatchInfoData } from '@/api/match'
import { useMatchSchemeStore } from '@/store/matchScheme'
import { PLAY_TYPE, SCHEME_CATEGORY } from '@/utils/enum'
import dayjs from 'dayjs'
import scoreModal from '../../modal/score.vue'
import jqModal from '../../modal/jq.vue'

const props = defineProps<{ matchList: IMatchInfoData[]; playMethod: IGamePlayRecord[] }>()
const { scheme } = useMatchSchemeStore()
const scoreRef = ref()
const jqRef = ref()

const emit = defineEmits<{
  (e: 'mainPlayCheck', m: IMatchInfoData, p: PLAY_TYPE, v: string): void
}>()

const releaseModalType = ref<PLAY_TYPE>(PLAY_TYPE.SCORE)
const currentMatch = ref<IMatchInfoData>({} as IMatchInfoData)

function getLimit(t: PLAY_TYPE) {
  const pm = props.playMethod
  if (!pm) return 0
  const r = pm.find((e) => e.id === t)
  return r ? r.resultNum : 0
}

function getResultType(t: PLAY_TYPE) {
  const rt = props.playMethod.find((p) => p.id === t)
  return rt ? rt.resultType : 0
}

function openModal(r: PLAY_TYPE, m: IMatchInfoData) {
  if (r !== releaseModalType.value) {
    releaseModalType.value = r
  }

  currentMatch.value = m
  nextTick(() => {
    if (r === PLAY_TYPE.SCORE) {
      scoreRef.value.open()
    } else if (r === PLAY_TYPE.JQ) {
      jqRef.value.open()
    }
  })
}

function isMainActive(matchId: number, p: PLAY_TYPE, v: string) {
  const m = scheme.main.find((m) => m.matchId === matchId)
  if (!m) return false
  const ret = m.matchPlays.find((mp) => mp.playId === p)
  if (!ret) return false
  const result = [PLAY_TYPE.RQ, PLAY_TYPE.DXQ].includes(p)
    ? ret.result.split(',').slice(1)
    : ret.result.split(',')
  return result.includes(v)
}

function isSelected(id: number, t: PLAY_TYPE) {
  const m = scheme.bonus.find((b) => b.matchId === id)
  if (!m) return false
  return !!m.matchPlays.find((mp) => mp.playId === t)
}

function handleMainCheck(m: IMatchInfoData, playId: PLAY_TYPE, v: string) {
  emit('mainPlayCheck', m, playId, v)
}
</script>

<style lang="scss" scoped>
.play-item {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #333;
  background-color: #f3f4f5;
  border-radius: 4rpx;

  .rq {
    color: #d1302e;
  }

  .odd {
    font-size: 22rpx;
    color: #999;
  }

  &.isActive {
    color: white;
    background-color: #d1302e;

    .rq {
      color: white !important;
    }

    .odd {
      color: white;
    }
  }
}
</style>
