<template>
  <view class="bg-white shadow rounded-12rpx px-20rpx py-25rpx mx-20rpx h-full px-20rpx box-border">
<!--    <view class="text-26rpx h-40rpx">-->
<!--      复制链接,到电脑发布-->
<!--      <wd-icon class="ml-10rpx" name="file-copy" size="30rpx"></wd-icon>-->
<!--    </view>-->
    <view
      class="bg-white mt-20rpx border border-solid border-[#e5e4e4] box-border rounded-12rpx overflow-hidden"
      style="height: calc(100% - 60rpx)"
    >
      <!-- 顶部标签切换 -->
      <view class="flex h-85rpx">
        <view
          :class="
            activeTab === 'fourteen'
              ? 'text-#D1302E '
              : 'rounded-br-[12rpx] border-r-solid border-b-solid border-#e5e4e4 border-[1rpx] text-#333 bg-#F6F7FB'
          "
          class="flex-1 text-center py-20rpx text-32rpx font-bold"
          @click="switchTab('fourteen')"
        >
          14场
        </view>
        <view
          :class="
            activeTab === 'nine'
              ? 'text-#D1302E border-b-2rpx border-#D1302E'
              : 'rounded-bl-[12rpx] border-l-solid border-b-solid border-#e5e4e4 border-[1rpx] text-#333 bg-#F6F7FB'
          "
          class="flex-1 text-center py-20rpx text-32rpx font-bold"
          @click="switchTab('nine')"
        >
          任9
        </view>
      </view>

      <!-- 选择比赛标题和期号 -->
      <view class="flex justify-between items-center px-30rpx h-80rpx">
        <text class="text-32rpx font-bold">
          {{ activeTab === "fourteen" ? "选择14场比赛" : "选择任9比赛" }}
        </text>
        <view class="flex items-center bg-#D1302E text-white px-20rpx py-5rpx rounded-8rpx">
          <text class="text-28rpx mr-10rpx">{{ currentIssue }}</text>
          <wd-icon color="white" name="arrow-down" size="24rpx" @click="showIssuePicker" />
        </view>
      </view>

      <!-- 比赛列表 -->
      <scroll-view
        :show-scrollbar="false"
        class="p-30rpx box-border"
        scroll-y
        style="height: calc(100% - 165rpx)"
      >
        <view v-for="(match, index) in matchList" :key="match.matchId" class="mb-40rpx">
          <!-- 比赛编号和联赛 -->
          <view class="flex items-center justify-between mb-20rpx">
            <text class="text-26rpx text-#999 mr-20rpx">
              {{ formatMatchNumber(index + 1, match) }}
            </text>
            <view v-if="getSelectedOptionsCount(match.matchId) > 0" class="flex items-center">
              <text class="text-24rpx text-#D1302E mr-10rpx">
                已选{{ getSelectedOptionsCount(match.matchId) }}项
              </text>
              <view class="w-20rpx h-20rpx bg-#D1302E rounded-full center">
                <text class="text-12rpx text-white">
                  {{ getSelectedOptionsCount(match.matchId) }}
                </text>
              </view>
            </view>
          </view>

          <!-- 主队 vs 客队 -->
          <view class="flex justify-between items-center mb-20rpx">
            <text class="text-30rpx font-medium flex-1">{{ match.homeName }}</text>
            <text class="text-26rpx text-#999 mx-20rpx">
              {{ formatMatchTime(match.matchTime) }}
            </text>
            <text class="text-30rpx font-medium flex-1 text-right">{{ match.awayName }}</text>
          </view>

          <!-- 投注选项 -->
          <view class="flex gap-x-20rpx">
            <view
              :class="getOptionClass(match.matchId, 3)"
              class="flex-1 h-70rpx center border-2rpx rounded-8rpx relative"
              @click="selectOption(match, 3)"
            >
              <text class="text-32rpx font-bold">3</text>
              <view
                v-if="isOptionSelected(match.matchId, 3)"
                class="absolute top-4rpx right-4rpx w-16rpx h-16rpx bg-white rounded-full center"
              >
                <text class="text-12rpx text-#D1302E">✓</text>
              </view>
            </view>
            <view
              :class="getOptionClass(match.matchId, 1)"
              class="flex-1 h-70rpx center border-2rpx rounded-8rpx relative"
              @click="selectOption(match, 1)"
            >
              <text class="text-32rpx font-bold">1</text>
              <view
                v-if="isOptionSelected(match.matchId, 1)"
                class="absolute top-4rpx right-4rpx w-16rpx h-16rpx bg-white rounded-full center"
              >
                <text class="text-12rpx text-#D1302E">✓</text>
              </view>
            </view>
            <view
              :class="getOptionClass(match.matchId, 0)"
              class="flex-1 h-70rpx center border-2rpx rounded-8rpx relative"
              @click="selectOption(match, 0)"
            >
              <text class="text-32rpx font-bold">0</text>
              <view
                v-if="isOptionSelected(match.matchId, 0)"
                class="absolute top-4rpx right-4rpx w-16rpx h-16rpx bg-white rounded-full center"
              >
                <text class="text-12rpx text-#D1302E">✓</text>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 期号选择器弹窗 -->
      <wd-popup
        v-model="showPicker"
        custom-style="border-radius: 24rpx 24rpx 0 0;"
        position="bottom"
      >
        <view class="issue-picker-popup">
          <!-- 弹窗头部 -->
          <view class="popup-header">
            <view class="header-title">选择期号</view>
            <wd-icon
              class="close-icon"
              color="#999"
              name="close"
              size="32rpx"
              @click="closePicker"
            />
          </view>

          <!-- 期号列表 -->
          <scroll-view :show-scrollbar="false" class="issue-list" scroll-y>
            <view
              v-for="issue in issueList"
              :key="issue"
              :class="getIssueItemClass(issue)"
              class="issue-item"
              @click="selectIssue(issue)"
            >
              <text class="issue-text">第{{ issue }}期</text>
              <wd-icon
                v-if="tempSelectedIssue === issue"
                color="#D1302E"
                name="check"
                size="32rpx"
              />
            </view>
          </scroll-view>

          <!-- 确认按钮 -->
          <view class="popup-footer">
            <wd-button
              block
              custom-style="background: #D1302E; border-color: #D1302E;"
              size="large"
              type="primary"
              @click="confirmIssueSelection"
            >
              确认选择
            </wd-button>
          </view>
        </view>
      </wd-popup>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import { getMatchListInfo, type IMatchInfoItem, type IMatchScheme } from "@/api/match";
import { DATA_TYPE } from "@/utils/enum";
import dayjs from "dayjs";
import { useMatchSchemeStore } from '@/store/matchScheme'
import { SCHEME_TYPE } from '@/utils/enum'
// 定义事件
const emit = defineEmits<{
  "update-count": [count: number]
}>();

const { setMainScheme, changeSchemePlay, scheme } = useMatchSchemeStore()

// 响应式数据
const activeTab = ref<"fourteen" | "nine">("fourteen");
const currentIssue = ref("");
const showPicker = ref(false);
const matchList = ref<IMatchInfoItem[]>([]);
const selectedOptions = ref<IMatchScheme[]>([]); // matchId -> options array [0,1,3]
const tempSelectedIssue = ref(""); // 临时选择的期号
const allMatchData = ref<IMatchInfoItem[]>([]); // 存储所有比赛数据

// 期号列表 - 从API数据中动态获取
const issueList = ref<string[]>([]);

// 计算属性
const selectedCount = computed(() => {
  return selectedOptions.value.length || 0;
});

// 监听选择变化并发出事件
watch(
  selectedCount,
  (newCount) => {
    emit("update-count", newCount);
  },
  { immediate: true }
);

// 方法
const switchTab = (tab: "fourteen" | "nine") => {
  if (activeTab.value === tab) return; // 如果当前已是该标签页，则不做任何操作
  // 切换标签时重置选择
  selectedOptions.value = [];
  setMainScheme([]);
  activeTab.value = tab;
  changeSchemePlay(tab === "fourteen" ? SCHEME_TYPE.MATCH_LOTTERY : SCHEME_TYPE.ANY_NINE);
  loadMatchData();
};

// 从API数据中提取期号列表
const extractIssueList = (data: IMatchInfoItem[]) => {
  const issues = data
    .map((item) => item.issue)
    .filter((issue) => issue !== null && issue !== undefined)
    .filter((issue, index, arr) => arr.indexOf(issue) === index) // 去重
    .sort((a, b) => a!.localeCompare(b!)); // 排序

  return issues as string[];
};

// 根据期号过滤比赛数据
const filterMatchesByIssue = (data: IMatchInfoItem[], issue: string) => {
  return data.filter((item) => item.issue === issue);
};

const showIssuePicker = () => {
  tempSelectedIssue.value = currentIssue.value; // 初始化临时选择
  showPicker.value = true;
};

const closePicker = () => {
  showPicker.value = false;
  tempSelectedIssue.value = currentIssue.value; // 重置临时选择
};

const selectIssue = (issue: string) => {
  tempSelectedIssue.value = issue;
};

const getIssueItemClass = (issue: string) => {
  return tempSelectedIssue.value === issue ? "issue-item-selected" : "issue-item-normal";
};

const confirmIssueSelection = () => {
  if (tempSelectedIssue.value !== currentIssue.value) {
    currentIssue.value = tempSelectedIssue.value;
    // 根据新期号过滤比赛数据
    const filteredData = filterMatchesByIssue(allMatchData.value, currentIssue.value);
    matchList.value = filteredData.slice(0, activeTab.value === "fourteen" ? 14 : 9);
    // 切换期号时重置选择
    selectedOptions.value = [];
    setMainScheme([])
    scheme.instalments = currentIssue.value; // 更新方案的期号
  }
  showPicker.value = false;
};

const formatMatchNumber = (index: number, data: IMatchInfoItem) => {
  return `${String(index).padStart(2, "0")}.${data.roundName}`;
};

const formatMatchTime = (timestamp: number) => {
  return dayjs(timestamp).format("MM/DD HH:mm");
};

const isOptionSelected = (matchId: number, option: number) => {
  const matchItem = selectedOptions.value.find(item => item.matchId === matchId);
  return !!matchItem && matchItem.matchPlays.some(play => play.result.includes(option.toString()));
};

const getSelectedOptionsCount = (matchId: number) => {
  const selectedOptionsForMatch = selectedOptions.value.find(item => item.matchId === matchId);
  return selectedOptionsForMatch?.matchPlays[0]?.result.split(',').length || 0;
};

const getOptionClass = (matchId: number, option: number) => {
  const isSelected = isOptionSelected(matchId, option);
  if (isSelected) {
    return "bg-#D1302E border-#D1302E text-white";
  } else {
    return "bg-#F5F5F5 border-#E0E0E0 text-#333";
  }
};

const selectOption = (match: IMatchInfoItem, option: number) => {
  let matchItem = selectedOptions.value.find(item => item.matchId === match.matchId);
  if (!matchItem) {
    matchItem = {
      matchId: match.matchId,
      homeName: match.homeName,
      awayName: match.awayName,
      roundName: match.roundName,
      matchTime: match.matchTime,
      opinion: 0,
      matchPlays: [{ type: 0, playId: 3, opinion: 0, resultType: 1, result: option.toString() }]
    };
    selectedOptions.value.push(matchItem);
  } else {
    const idx = matchItem.matchPlays.findIndex(play => play.result.includes(option.toString()));
    if (idx > -1) {
      matchItem.matchPlays[0].result = matchItem.matchPlays[0].result.split(",").filter(item => item !== option.toString()).join(","); // 移除选项
      // 如果没有选项了，移除该比赛
      if (matchItem.matchPlays[0].result === "") {
        let deleteIndex = selectedOptions.value.findIndex(item => item.matchId === match.matchId);
        if (deleteIndex !== -1) {
          selectedOptions.value.splice(deleteIndex, 1);
        }
      }
    } else {
      matchItem.matchPlays[0].result = matchItem.matchPlays[0].result + "," + option.toString();
    }
  }
  setMainScheme(selectedOptions.value);
  console.info("选中选项:", selectedOptions.value);
};

const resetSelection = () => {
  selectedOptions.value = [];
  uni.showToast({ title: "已重置", icon: "success" });
};

const loadMatchData = async () => {
  try {
    uni.showLoading({ title: "加载中..." });
    const dataType = activeTab.value === "fourteen" ? DATA_TYPE.FOURTY_NINE : DATA_TYPE.FOURTY_NINE;
    const data = await getMatchListInfo(dataType);

    // 存储所有数据
    allMatchData.value = data;

    // 提取期号列表
    const issues = extractIssueList(data);
    issueList.value = issues;

    // 如果当前期号为空或不在列表中，设置为第一个期号
    if (!currentIssue.value || !issues.includes(currentIssue.value)) {
      currentIssue.value = issues[0] || "";
      tempSelectedIssue.value = currentIssue.value;
    }

    // 根据当前期号过滤比赛数据
    if (currentIssue.value) {
      matchList.value = filterMatchesByIssue(data, currentIssue.value);
    } else {
      matchList.value = [];
    }
  } catch (error) {
    console.error("加载比赛数据失败:", error);
    uni.showToast({ title: "加载失败", icon: "error" });
  } finally {
    uni.hideLoading();
  }
};

// 生命周期
onMounted(() => {
  selectedOptions.value = scheme.main || [];
  console.log(`🚀 ~ onMounted ~ scheme.schemePlay:`, scheme.schemePlay);
  activeTab.value = scheme.schemePlay === SCHEME_TYPE.ANY_NINE ? "nine" : "fourteen";
  currentIssue.value = scheme.instalments || "";
  changeSchemePlay(activeTab.value === "fourteen" ? SCHEME_TYPE.MATCH_LOTTERY : SCHEME_TYPE.ANY_NINE);
  loadMatchData();
});

// 向父组件暴露选中的比赛数量和重置方法
defineExpose({
  selectedCount,
  resetSelection
});
</script>

<style lang="scss" scoped>
.issue-picker-popup {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-icon {
  padding: 10rpx;
}

.issue-list {
  flex: 1;
  max-height: 500rpx;
  padding: 20rpx 0;
}

.issue-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  margin: 0 20rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.issue-item-normal {
  background: #f8f9fa;
  border: 2rpx solid transparent;
}

.issue-item-normal:active {
  background: #e9ecef;
}

.issue-item-selected {
  background: #fff2f2;
  border: 2rpx solid #d1302e;
}

.issue-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.issue-item-selected .issue-text {
  color: #d1302e;
  font-weight: bold;
}

.popup-footer {
  padding: 30rpx;
  border-top: 2rpx solid #f5f5f5;
  background: white;
}

/* 确保弹窗内容不会被遮挡 */
.issue-picker-popup {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
