<template>
  <view class="flex px-20rpx">
    <view class="flex-1 pt-32rpx px-20rpx pb-50rpx rounded-16rpx bg-white shadow">
<!--      <view class="text-26rpx">-->
<!--        复制链接,到电脑发布-->
<!--        <wd-icon name="file-copy" size="30rpx" class="ml-10rpx" />-->
<!--      </view>-->
      <view class="flex items-center gap-x-30rpx pt-30rpx">
        <text class="text-black text-32rpx" v-if="matchGroup[today]">{{ todayTxt }}</text>
        <wd-input
          prefixIcon="search"
          placeholder="搜索比赛如球队名称"
          class="searchBar"
          @input="debounceSearch"
          @clear="clearSearch"
          clearable
        />
      </view>
      <uni-list :border="false">
        <template v-if="matchGroup[today]">
          <uni-list-item>
            <template v-slot:body>
              <view class="w-full">
                <match-list
                  :match-list="matchGroup[today]"
                  @main-play-check="handleMainPlayCheck"
                  :play-method="playMethod"
                />
              </view>
            </template>
          </uni-list-item>
        </template>
        <template v-if="isEmpty(Object.keys(matchGroup))">
          <wd-status-tip image="content" tip="暂无数据" />
        </template>
        <template v-else>
          <template v-for="date in Object.keys(matchGroup).filter((k) => k !== today)" :key="date">
            <uni-list-item>
              <template v-slot:body>
                <view class="w-full">
                  <view class="flex items-center gap-x-30rpx pt-30rpx">
                    <text class="text-black text-32rpx">{{ getDateInfo(date) }}</text>
                  </view>
                  <match-list
                    :match-list="matchGroup[date]"
                    @main-play-check="handleMainPlayCheck"
                    :play-method="playMethod"
                  />
                </view>
              </template>
            </uni-list-item>
          </template>
        </template>
      </uni-list>
    </view>
  </view>
</template>

<script setup lang="ts">
import { IMatchInfoData } from '@/api/match'
import { WEEK_CN } from '@/utils/constant'
import { DEFAULT_DATE_FORMATER } from '@/utils/format'
import dayjs, { ConfigType } from 'dayjs'
import { isEmpty, uniq } from 'lodash-es'
import matchList from './matchList.vue'
import { PLAY_TYPE } from '@/utils/enum'
import { IGamePlayRecord } from '@/api/author'
import { useMatchSchemeStore } from '@/store/matchScheme'
import { debounce } from 'lodash-es'

const props = defineProps<{
  matchGroup: { [key: string]: IMatchInfoData[] }
  playMethod: IGamePlayRecord[]
}>()

const emit = defineEmits<{
  (e: 'search', key: string): void
}>()

const today = ref('')
const todayTxt = ref('')

function getDateInfo(date: ConfigType) {
  const datetime = dayjs(date)
  return `${datetime.format(DEFAULT_DATE_FORMATER)} (${WEEK_CN[datetime.day()]})`
}

const { scheme, setMainScheme, changeSchemePlay } = useMatchSchemeStore()

function search({ value }: { value: string }) {
  emit('search', value)
}

function clearSearch() {
  emit('search', '')
}

const debounceSearch = debounce(search, 600)

/* 主玩法的选择 */
function handleMainPlayCheck(m: IMatchInfoData, playId: PLAY_TYPE, v: string) {
  const {
    matchId,
    homeName,
    awayName,
    roundName,
    matchTime,
    matchPlayOdds,
    shortHomeName,
    shortAwayName,
    comp,
    shortComp,
    issueNum,
    week,
  } = m
  const { main: mainScheme } = scheme
  const pms = props.playMethod
  const pm = pms.find((p) => p.id === playId)!
  const opinion = 0
  const s = mainScheme.find((e) => e.matchId === matchId)
  const type = 0

  if (playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW) {
    const [odd] = matchPlayOdds.rq ? matchPlayOdds.rq.split(',') : [0]
    // 让球胜平负
    if (!s) {
      setMainScheme([
        ...mainScheme,
        {
          matchId,
          homeName,
          awayName,
          roundName,
          shortHomeName,
          shortAwayName,
          comp,
          shortComp,
          issueNum,
          week,
          matchTime,
          opinion,
          matchPlays: [
            {
              type: 0,
              playId,
              opinion,
              resultType: pm.resultType,
              result: [odd, v].join(','),
            },
          ],
        },
      ])

      const count = uniq([
        ...scheme.main.map((s) => s.matchId),
        ...scheme.bonus.map((s) => s.matchId),
      ]).length

      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }

      return
    }

    const mp = s.matchPlays.find((m) => m.playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW)
    if (!mp) {
      // 选择了此赛事的其他玩法，但还未选择让球胜平负的玩法
      setMainScheme(
        mainScheme.map((m) => {
          if (m.matchId === matchId) {
            return {
              ...m,
              matchPlays: [
                ...m.matchPlays,
                {
                  type,
                  playId,
                  opinion,
                  resultType: pm.resultType,
                  result: [odd, v].join(','),
                },
              ],
            }
          }

          return m
        }),
      )

      const count = uniq([
        ...scheme.main.map((s) => s.matchId),
        ...scheme.bonus.map((s) => s.matchId),
      ]).length

      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }
      return
    }

    // 已选择让球胜平负玩法
    let result = mp.result.split(',').slice(1)
    result = result.includes(v) ? result.filter((r) => r !== v) : [...result, v]

    const limit = pms.find((p) => p.id === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW)!.resultNum
    let currentNum = result.length
    if (currentNum > limit) {
      uni.showToast({ title: '超出可选范围', icon: 'none' })
      return
    }

    if (isEmpty(result) && s.matchPlays.length === 1) {
      // 如果是取消选择，并且取消了所有玩法
      setMainScheme(mainScheme.filter((e) => e.matchId !== matchId))

      const count = uniq([
        ...scheme.main.map((s) => s.matchId),
        ...scheme.bonus.map((s) => s.matchId),
      ]).length

      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }

      return
    }

    isEmpty(result)
      ? setMainScheme(
          mainScheme.map((e) =>
            e.matchId === matchId
              ? {
                  ...e,
                  matchPlays: e.matchPlays.filter(
                    (m) => m.playId !== PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW,
                  ),
                }
              : e,
          ),
        )
      : setMainScheme(
          mainScheme.map((e) =>
            e.matchId === matchId
              ? {
                  ...e,
                  matchPlays: e.matchPlays.map((m) =>
                    m.playId === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW
                      ? {
                          ...m,
                          result: [odd, result].join(','),
                        }
                      : m,
                  ),
                }
              : e,
          ),
        )

    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length

    if (count === 1) {
      changeSchemePlay(4)
    } else if (count === 2) {
      changeSchemePlay(3)
    } else {
      changeSchemePlay(5)
    }
  } else {
    // 胜平负
    if (!s) {
      setMainScheme([
        ...mainScheme,
        {
          matchId,
          homeName,
          awayName,
          roundName,
          shortHomeName,
          shortAwayName,
          comp,
          shortComp,
          issueNum,
          week,
          matchTime,
          opinion,
          matchPlays: [
            {
              type,
              playId,
              opinion,
              resultType: pm.resultType,
              result: v,
            },
          ],
        },
      ])

      const count = uniq([
        ...scheme.main.map((s) => s.matchId),
        ...scheme.bonus.map((s) => s.matchId),
      ]).length

      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }

      return
    }

    const mp = s.matchPlays.find((m) => m.playId === PLAY_TYPE.WIN_LOSE_DRAW)
    if (!mp) {
      setMainScheme(
        mainScheme.map((m) => {
          if (m.matchId === matchId) {
            return {
              ...m,
              matchPlays: [
                ...m.matchPlays,
                {
                  type,
                  playId,
                  opinion,
                  resultType: pm.resultType,
                  result: v,
                },
              ],
            }
          }

          return m
        }),
      )

      const count = uniq([
        ...scheme.main.map((s) => s.matchId),
        ...scheme.bonus.map((s) => s.matchId),
      ]).length

      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }

      return
    }

    // 已选择胜平负玩法
    let result = mp.result.split(',')
    result = result.includes(v) ? result.filter((r) => r !== v) : [...result, v]

    const limit = pms.find((p) => p.id === PLAY_TYPE.WIN_LOSE_DRAW)!.resultNum
    let currentNum = result.length
    if (currentNum > limit) {
      uni.showToast({ title: '超出可选范围', icon: 'none' })
      return
    }

    if (isEmpty(result) && s.matchPlays.length === 1) {
      // 如果是取消选择，并且取消了所有玩法
      setMainScheme(mainScheme.filter((e) => e.matchId !== matchId))

      const count = uniq([
        ...scheme.main.map((s) => s.matchId),
        ...scheme.bonus.map((s) => s.matchId),
      ]).length

      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }

      return
    }

    isEmpty(result)
      ? setMainScheme(
          mainScheme.map((e) =>
            e.matchId === matchId
              ? {
                  ...e,
                  matchPlays: e.matchPlays.filter((m) => m.playId !== PLAY_TYPE.WIN_LOSE_DRAW),
                }
              : e,
          ),
        )
      : setMainScheme(
          mainScheme.map((e) =>
            e.matchId === matchId
              ? {
                  ...e,
                  matchPlays: e.matchPlays.map((m) =>
                    m.playId === PLAY_TYPE.WIN_LOSE_DRAW
                      ? {
                          ...m,
                          result: result.join(','),
                        }
                      : m,
                  ),
                }
              : e,
          ),
        )

    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length

    if (count === 1) {
      changeSchemePlay(4)
    } else if (count === 2) {
      changeSchemePlay(3)
    } else {
      changeSchemePlay(5)
    }
  }
}

onShow(() => {
  const now = dayjs()
  today.value = now.format(DEFAULT_DATE_FORMATER)
  todayTxt.value = `${today.value} (${WEEK_CN[now.day()]})`
})
</script>
<style>
:deep(.uni-list-item__container) {
  padding: 0 !important;
}

.searchBar {
  display: flex;
  flex: 1;
  align-items: center;
  padding-left: 10rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;

  &.no-match {
    margin-top: 28rpx;
  }

  &::after {
    display: none;
  }
}
</style>
