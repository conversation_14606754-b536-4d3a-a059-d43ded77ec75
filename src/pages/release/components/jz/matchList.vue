<template>
  <view
    v-for="match in matchList"
    :key="match.matchId"
    class="pb-35rpx border-b-2rpx border-b-solid border-b-#F3F3F3"
  >
    <view class="flex justify-between mt-25rpx mb-40rpx text-26rpx">
      <view class="flex">
        <text>{{ `${match.week}${match.issueNum}` }}</text>
        <text class="center px-8rpx h-37rpx mx-18rpx text-#D1302E bg-#FFECEE rounded-4rpx">
          {{ match.shortComp }}
        </text>
        <text>{{ dayjs(match.matchTime * 1000).format('HH:mm') }}</text>
      </view>
      <text class="font-medium">{{ `${match.shortHomeName} VS ${match.shortAwayName}` }}</text>
    </view>
    <!-- 玩法(胜平负) -->
    <view class="flex gap-x-14rpx h-70rpx mb-20rpx">
      <view class="flex-1 center bg-#E6F5DD rounded-4rpx text-24rpx text-#60B53F">胜平负</view>
      <view
        class="play-item"
        :class="isMainActive(match.matchId, PLAY_TYPE.WIN_LOSE_DRAW, '3') ? 'isActive' : ''"
        @click="handleMainCheck(match, PLAY_TYPE.WIN_LOSE_DRAW, '3')"
      >
        <text class="text-24rpx">主胜</text>
        <text v-if="match.matchPlayOdds.spf" class="odd">
          {{ match.matchPlayOdds.spf.split(',')[0] }}
        </text>
      </view>
      <view
        class="play-item"
        :class="isMainActive(match.matchId, PLAY_TYPE.WIN_LOSE_DRAW, '1') ? 'isActive' : ''"
        @click="handleMainCheck(match, PLAY_TYPE.WIN_LOSE_DRAW, '1')"
      >
        <text class="text-24rpx">平</text>
        <text v-if="match.matchPlayOdds.spf" class="odd">
          {{ match.matchPlayOdds.spf.split(',')[1] }}
        </text>
      </view>
      <!-- <view class="flex-1 flex flex-col items-center justify-center bg-#F3F4F5 rounded-4rpx" -->
      <view
        class="play-item"
        :class="isMainActive(match.matchId, PLAY_TYPE.WIN_LOSE_DRAW, '0') ? 'isActive' : ''"
        @click="handleMainCheck(match, PLAY_TYPE.WIN_LOSE_DRAW, '0')"
      >
        <text class="text-24rpx">客胜</text>
        <text v-if="match.matchPlayOdds.spf" class="odd">
          {{ match.matchPlayOdds.spf.split(',')[2] }}
        </text>
      </view>
    </view>
    <!-- 玩法(让球) -->
    <view class="flex gap-x-14rpx h-70rpx">
      <view class="flex-1 center bg-#FEF2E7 rounded-4rpx text-24rpx text-#E8AF74">让球</view>
      <view
        class="play-item"
        :class="
          isMainActive(match.matchId, PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, '3') ? 'isActive' : ''
        "
        @click="handleMainCheck(match, PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, '3')"
      >
        <view class="text-24rpx">
          <text>主胜</text>
          <text v-if="match.matchPlayOdds.rq" class="rq">
            {{ `(${match.matchPlayOdds.rq.split(',')[0]})` }}
          </text>
        </view>
        <text v-if="match.matchPlayOdds.rq" class="odd">
          {{ `(${match.matchPlayOdds.rq.split(',')[1]})` }}
        </text>
      </view>
      <view
        class="play-item"
        :class="
          isMainActive(match.matchId, PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, '1') ? 'isActive' : ''
        "
        @click="handleMainCheck(match, PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, '1')"
      >
        <text class="text-24rpx">平</text>
        <text v-if="match.matchPlayOdds.rq" class="odd">
          {{ `(${match.matchPlayOdds.rq.split(',')[2]})` }}
        </text>
      </view>
      <view
        class="play-item"
        :class="
          isMainActive(match.matchId, PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, '0') ? 'isActive' : ''
        "
        @click="handleMainCheck(match, PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW, '0')"
      >
        <text class="text-24rpx">客胜</text>
        <text v-if="match.matchPlayOdds.rq" class="odd">
          {{ `(${match.matchPlayOdds.rq.split(',')[3]})` }}
        </text>
      </view>
    </view>
    <!-- 附赠玩法 -->
    <view class="flex items-center mt-33rpx mb-20rpx">
      <text class="text-#333 text-26rpx">附赠玩法</text>
      <text class="text-#999 text-24rpx">（结果不计入作者战绩）</text>
    </view>
    <view class="grid grid-cols-4 gap-x-14rpx h-70rpx text-#333 text-24rpx">
      <wd-badge :is-dot="isSelected(match.matchId, PLAY_TYPE.SCORE)">
        <view
          class="center h-70rpx bg-#F3F4F5 rounded-4rpx"
          @click="openModal(PLAY_TYPE.SCORE, match)"
        >
          比分
        </view>
      </wd-badge>
      <wd-badge :is-dot="isSelected(match.matchId, PLAY_TYPE.BQC)">
        <view
          class="center h-70rpx bg-#F3F4F5 rounded-4rpx"
          @click="openModal(PLAY_TYPE.BQC, match)"
        >
          半全场
        </view>
      </wd-badge>
      <wd-badge :is-dot="isSelected(match.matchId, PLAY_TYPE.JQ)">
        <view
          class="center h-70rpx bg-#F3F4F5 rounded-4rpx"
          @click="openModal(PLAY_TYPE.JQ, match)"
        >
          进球数
        </view>
      </wd-badge>
    </view>
  </view>
  <template v-if="releaseModalType === PLAY_TYPE.SCORE">
    <score-modal
      :match="currentMatch"
      :limit="getLimit(PLAY_TYPE.SCORE)"
      :result-type="getResultType(PLAY_TYPE.SCORE)"
      ref="scoreRef"
      :category="SCHEME_CATEGORY.JZ"
    />
  </template>
  <template v-else-if="releaseModalType === PLAY_TYPE.BQC">
    <bqc-modal
      :match="currentMatch"
      :limit="getLimit(PLAY_TYPE.BQC)"
      :result-type="getResultType(PLAY_TYPE.BQC)"
      ref="bqcRef"
    />
  </template>
  <template v-else-if="releaseModalType === PLAY_TYPE.JQ">
    <jq-modal
      ref="jqRef"
      :match="currentMatch"
      :limit="getLimit(PLAY_TYPE.JQ)"
      :result-type="getResultType(PLAY_TYPE.JQ)"
      :category="SCHEME_CATEGORY.JZ"
    />
  </template>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import { IMatchInfoData } from '@/api/match'
import { PLAY_TYPE, SCHEME_CATEGORY } from '@/utils/enum'
import { useMatchSchemeStore } from '@/store/matchScheme'
import scoreModal from '../../modal/score.vue'
import bqcModal from '../../modal/bqc.vue'
import jqModal from '../../modal/jq.vue'
import { IGamePlayRecord } from '@/api/author'

const scoreRef = ref()
const bqcRef = ref()
const jqRef = ref()

const props = defineProps<{ matchList: IMatchInfoData[]; playMethod: IGamePlayRecord[] }>()

const { scheme } = useMatchSchemeStore()

const releaseModalType = ref<PLAY_TYPE>(PLAY_TYPE.SCORE)
const currentMatch = ref<IMatchInfoData>({} as IMatchInfoData)

function isSelected(id: number, t: PLAY_TYPE) {
  const m = scheme.bonus.find((b) => b.matchId === id)
  if (!m) return false
  return !!m.matchPlays.find((mp) => mp.playId === t)
}

function getLimit(t: PLAY_TYPE) {
  const pm = props.playMethod
  if (!pm) return 0
  const r = pm.find((e) => e.id === t)
  return r ? r.resultNum : 0
}

function getResultType(t: PLAY_TYPE) {
  const rt = props.playMethod.find((p) => p.id === t)
  return rt ? rt.resultType : 0
}
function openModal(r: PLAY_TYPE, m: IMatchInfoData) {
  if (r !== releaseModalType.value) {
    releaseModalType.value = r
  }
  currentMatch.value = m
  nextTick(() => {
    switch (r) {
      case PLAY_TYPE.SCORE:
        scoreRef.value.open()
        break
      case PLAY_TYPE.BQC:
        bqcRef.value.open()
        break
      case PLAY_TYPE.JQ:
        jqRef.value.open()
        break
    }
  })
}

function isMainActive(matchId: number, p: PLAY_TYPE, v: string) {
  const m = scheme.main.find((m) => m.matchId === matchId)
  if (!m) return false
  const ret = m.matchPlays.find((mp) => mp.playId === p)
  if (!ret) return false
  const result =
    p === PLAY_TYPE.HANDICAP_WIN_LOSE_DRAW ? ret.result.split(',').slice(1) : ret.result.split(',')
  return result.includes(v)
}

const emit = defineEmits<{
  (e: 'mainPlayCheck', m: IMatchInfoData, p: PLAY_TYPE, v: string): void
}>()

function handleMainCheck(m: IMatchInfoData, playId: PLAY_TYPE, v: string) {
  emit('mainPlayCheck', m, playId, v)
}
</script>

<style lang="scss" scoped>
.play-item {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #333;
  background-color: #f3f4f5;
  border-radius: 4rpx;

  .rq {
    color: #d1302e;
  }

  .odd {
    font-size: 22rpx;
    color: #999;
  }

  &.isActive {
    color: white;
    background-color: #d1302e;

    .rq {
      color: white !important;
    }

    .odd {
      color: white;
    }
  }
}
</style>
