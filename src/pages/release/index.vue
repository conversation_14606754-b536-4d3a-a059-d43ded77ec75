<route lang="json5">
{
  style: {
    navigationBarTitleText: '发布方案',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="flex flex-col bg-#fafafa" style="height: calc(100vh - 180rpx)">
    <view class="flex items-center p-20rpx text-#333 text-24rpx h-[55rpx]">
      <image
        @click="showMemu"
        src="https://sacdn.850g.com/football/static/icons/release/menu.svg"
        mode="scaleToFill"
        class="w-50rpx h-50rpx"
      />
      <text class="ml-20rpx mr-auto">{{ categoryTxt }}</text>
      <wd-icon name="close-circle-filled" size="50rpx" color="#D1302E" @click="gotoHomePage" />
    </view>
    <!-- 方案编辑 -->
    <!-- <view :style="{ height: 'calc(100% - 95rpx)' }" v-if="step === SCHEME_STEP.SCHEME"> -->
    <!-- <view v-if="step === SCHEME_STEP.SCHEME"> -->
    <template v-if="step === SCHEME_STEP.SCHEME">
      <scheme
        :category="schemeCategory"
        :match-group="matchGroup"
        @search="handleSearch"
        @next="handleContentStep"
      />
    </template>
    <template v-else-if="step === SCHEME_STEP.CONTENT">
      <content :category="schemeCategory" @privious="handlePriviousStep" :draft="draft" />
    </template>
    <template v-else-if="step === SCHEME_STEP.EXPERT_MANAGEMENT">
      <expertManagement />
    </template>
    <!-- </view> -->
  </view>
  <!-- 侧边栏弹框 -->
  <wd-popup v-model="menuVisible" position="left">
    <view class="w-335rpx h-full pt-160rpx pl-42rpx pr-33rpx box-border bg-#fafafa">
      <view class="flex items-center ml-5rpx gap-x-20rpx w-fit text-#333 text-26rpx">
        <image :src="homeIcon" mode="scaleToFill" class="w-26rpx h-26rpx" />
        <text>首页</text>
      </view>
      <view class="mt-50rpx text-#333">
        <view class="flex items-center justify-between" @click="changeExpand">
          <view class="center w-42rpx h-42rpx bg-#EA545D rounded-8rpx">
            <image
              src="https://sacdn.850g.com/football/static/release/plane.png"
              mode="scaleToFill"
              class="w-26rpx h-26rpx"
            />
          </view>
          <text class="ml-10rpx mr-auto text-26rpx">发布管理</text>
          <wd-icon name="arrow-up" v-if="expanded" />
          <wd-icon name="arrow-down" v-else />
        </view>
        <view v-if="expanded" class="flex flex-col gap-y-48rpx mt-34rpx pl-40rpx">
          <!-- 竞足 -->
          <view
            class="flex items-center gap-x-20rpx text-24rpx"
            @click="changeSchemeCategory(SCHEME_CATEGORY.JZ)"
          >
            <view
              class="center w-42rpx h-42rpx rounded-8rpx"
              :class="schemeCategory === SCHEME_CATEGORY.JZ ? 'bg-#EA545D' : 'bg-white'"
            >
              <image
                src="https://sacdn.850g.com/football/static/icons/release/ballHL.svg"
                mode="scaleToFill"
                class="w-26rpx h-26rpx"
                v-if="schemeCategory === SCHEME_CATEGORY.JZ"
              />
              <image
                src="https://sacdn.850g.com/football/static/icons/release/ball.svg"
                mode="scaleToFill"
                class="w-26rpx h-26rpx"
                v-else
              />
            </view>
            <text>竞足</text>
          </view>
          <!-- 十四场 -->
          <view
            class="flex items-center gap-x-20rpx text-24rpx"
            @click="changeSchemeCategory(SCHEME_CATEGORY.MATCH_LOTTERY)"
          >
            <view
              class="center w-42rpx h-42rpx rounded-8rpx"
              :class="
                [SCHEME_CATEGORY.MATCH_LOTTERY, SCHEME_CATEGORY.ANY_NINE].includes(schemeCategory)
                  ? 'bg-#EA545D'
                  : 'bg-white'
              "
            >
              <image
                src="https://sacdn.850g.com/football/static/icons/release/14HL.svg"
                mode="scaleToFill"
                class="w-26rpx h-26rpx"
                v-if="
                  [SCHEME_CATEGORY.MATCH_LOTTERY, SCHEME_CATEGORY.ANY_NINE].includes(schemeCategory)
                "
              />
              <image
                src="https://sacdn.850g.com/football/static/icons/release/14.svg"
                mode="scaleToFill"
                class="w-26rpx h-26rpx"
                v-else
              />
            </view>
            <text>十四场</text>
          </view>
          <!-- 北单 -->
          <view
            class="flex items-center gap-x-20rpx text-24rpx"
            @click="changeSchemeCategory(SCHEME_CATEGORY.BD)"
          >
            <view
              class="center w-42rpx h-42rpx rounded-8rpx"
              :class="schemeCategory === SCHEME_CATEGORY.BD ? 'bg-#EA545D' : 'bg-white'"
            >
              <image
                src="https://sacdn.850g.com/football/static/icons/release/ballHL.svg"
                mode="scaleToFill"
                class="w-26rpx h-26rpx"
                v-if="schemeCategory === SCHEME_CATEGORY.BD"
              />
              <image
                src="https://sacdn.850g.com/football/static/icons/release/ball.svg"
                mode="scaleToFill"
                class="w-26rpx h-26rpx"
                v-else
              />
            </view>
            <text>北单</text>
          </view>
          <!-- 自由发布 -->
          <view class="flex items-center gap-x-20rpx text-24rpx" @click="freeRelease">
            <view class="center w-42rpx h-42rpx rounded-8rpx" :class="schemeCategory === SCHEME_CATEGORY.FREESTYLE ? 'bg-#EA545D' : 'bg-white'">
              <image
                v-if="schemeCategory === SCHEME_CATEGORY.FREESTYLE"
                src="https://sacdn.850g.com/football/static/icons/release/rocketHL.svg"
                mode="scaleToFill"
                class="w-26rpx h-26rpx"
              />
              <image
              v-else
                src="https://sacdn.850g.com/football/static/icons/release/rocket.svg"
                mode="scaleToFill"
                class="w-26rpx h-26rpx"
              />
            </view>
            <text>自由发布</text>
          </view>
          <!-- 快捷发布 -->
<!--     todo:下个迭代版本完善功能     -->
<!--          <view class="flex items-center gap-x-20rpx text-24rpx" @click="templateRelease">-->
<!--            <view class="center w-42rpx h-42rpx rounded-8rpx bg-white">-->
<!--              <image-->
<!--                src="https://sacdn.850g.com/football/static/icons/release/thunder.svg"-->
<!--                mode="scaleToFill"-->
<!--                class="w-26rpx h-26rpx"-->
<!--              />-->
<!--            </view>-->
<!--            <text>快捷发布</text>-->
<!--          </view>-->
        </view>
      </view>
      <!-- 专家管理细则 -->
      <view
        @click="handleExperClick"
        class="flex items-center gap-x-20rpx w-fit mt-45rpx mb-60rpx text-#333 text-26rpx"
      >
        <view class="center w-42rpx h-42rpx bg-white">
          <wd-icon name="list" size="26rpx" />
        </view>
        <text>专家管理细则</text>
      </view>
      <!-- 我的发布 -->
<!--      <view class="flex items-center gap-x-20rpx w-fit text-#333 text-26rpx">-->
<!--        <view class="center w-42rpx h-42rpx bg-white">-->
<!--          <image-->
<!--            src="https://sacdn.850g.com/football/static/icons/release/my-release.svg"-->
<!--            mode="scaleToFill"-->
<!--            class="w-26rpx h-26rpx"-->
<!--          />-->
<!--        </view>-->
<!--        <text>我的发布</text>-->
<!--      </view>-->
    </view>
  </wd-popup>
  <!-- 快捷发布111 -->
  <template v-if="showTemp">
    <temp ref="tempRef" @close="handleTempClose" @submit="handleTempSubmit" />
  </template>
</template>

<script setup lang="ts">
import homeIcon from '@/static/tabbar/home.png'
import { DATA_TYPE, SCHEME_CATEGORY, SCHEME_STEP } from '@/utils/enum'
import scheme from './scheme.vue'
import content from './content.vue'
import expertManagement from './components/expertManagement/index.vue'
import temp from './modal/temp.vue'
import { IMatchInfoRecord, get24HoursMatchList } from '@/api/match'
import { ceil, groupBy } from 'lodash-es'
import { formatDate } from '@/utils/format'
import { WEEK_CN } from '@/utils/constant'
import dayjs from 'dayjs'
import { useMatchSchemeStore } from '@/store/matchScheme'
import { IArticleDraft, getArticleDraft } from '@/api/author'

const menuVisible = ref(true)
const expanded = ref(true)
const schemeCategory = ref(SCHEME_CATEGORY.JZ)
const dataType = ref(DATA_TYPE.JZ)
const step = ref(SCHEME_STEP.SCHEME)
const tempRef = ref()
const showTemp = ref()

const pageNo = ref(0)
const totalPage = ref(1)
const matchList = ref<IMatchInfoRecord[]>([])
const name = ref('')

/*
 * schemePlay
 * 1 14场
 * 2 任9
 * 3 竞足2串1
 * 4 竞足单关
 * 5 竞足多串1
 * 6 足球单关
 * 10 足球2串1
 * 11 足球多串1
 */

const { clearScheme } = useMatchSchemeStore()

const matchGroup = computed(() => {
  if ([SCHEME_CATEGORY.JZ, SCHEME_CATEGORY.BD].includes(schemeCategory.value)) {
    return matchList.value.reduce((prev, c) => {
      const { matchTime, issueNum } = c
      const key = formatDate(matchTime * 1000)
      let week = ''
      let _issueNum = issueNum
      if (schemeCategory.value === SCHEME_CATEGORY.JZ && issueNum) {
        const wIndex = issueNum.slice(0, 1)
        _issueNum = issueNum.slice(1)
        week = wIndex === '7' ? WEEK_CN[0] : WEEK_CN[+wIndex]
      } else {
        week = WEEK_CN[dayjs(matchTime * 1000).day()]
      }

      const _c = { ...c, week, matchTime, issueNum: _issueNum }
      return Object.keys(prev).includes(key)
        ? { ...prev, [key]: [...prev[key], _c] }
        : { ...prev, [key]: [_c] }
    }, {})
  }

  return groupBy(
    matchList.value.map((e) => ({
      ...e,
      matchTime: e.matchTime,
      week: WEEK_CN[dayjs(e.matchTime * 1000).day()],
    })),
    'issue',
  )
})

const categoryTxt = computed(() => {
  switch (schemeCategory.value) {
    case SCHEME_CATEGORY.JZ:
      return '首页/竞足'
    case SCHEME_CATEGORY.BD:
      return '首页/北单'
    case SCHEME_CATEGORY.MATCH_LOTTERY:
      return '首页/十四场'
    case SCHEME_CATEGORY.FREESTYLE:
      return '首页/自由发布'
    case SCHEME_CATEGORY.EXPERT_MANAGEMENT:
      return '首页/专家管理细则'
    default:
      return ''
  }
})

function handleContentStep() {
  step.value = SCHEME_STEP.CONTENT
}

function handlePriviousStep() {
  step.value = SCHEME_STEP.SCHEME
}

function templateRelease() {
  // 快捷发布
  // tempRef.value.open()
  showTemp.value = true
}

// 自由发布
function freeRelease() {
  clearScheme()
  step.value = SCHEME_STEP.CONTENT
  schemeCategory.value = SCHEME_CATEGORY.FREESTYLE
  menuVisible.value = false
}

function handleTempClose() {
  showTemp.value = false
}

// 快捷发布-发布
function handleTempSubmit(dataType: number) {
  showTemp.value = false
  step.value = SCHEME_STEP.CONTENT

  if (dataType === 1) { // 十四场
    schemeCategory.value = SCHEME_CATEGORY.MATCH_LOTTERY
  } else if (dataType === 2) { // 北单
    schemeCategory.value = SCHEME_CATEGORY.BD
  } else if (dataType === 3) { // 竞足
    schemeCategory.value = SCHEME_CATEGORY.JZ
  }
}

const handleExperClick = () => {
  step.value = SCHEME_STEP.EXPERT_MANAGEMENT
  menuVisible.value = false
  dataType.value = DATA_TYPE.EXPERT_MANAGEMENT
  schemeCategory.value = SCHEME_CATEGORY.EXPERT_MANAGEMENT
}

function changeExpand() {
  expanded.value = !expanded.value
}

function showMemu() {
  menuVisible.value = true
}

function gotoHomePage() {
  uni.showModal({
    title: '关闭未发布的方案吗？',
    success: (success) => {
      if (success.confirm) {
        uni.switchTab({ url: '/pages/index/index' })
      }
    },
  })
}

function changeSchemeCategory(c: SCHEME_CATEGORY) {
  if (c !== schemeCategory.value) {
    schemeCategory.value = c
    switch (c) {
      case SCHEME_CATEGORY.JZ:
        dataType.value = DATA_TYPE.JZ
        break
      case SCHEME_CATEGORY.BD:
        dataType.value = DATA_TYPE.BD
        break
      case SCHEME_CATEGORY.MATCH_LOTTERY:
        dataType.value = DATA_TYPE.FOURTY_NINE
    }
    pageNo.value = 0
    name.value = ''

    getMatchData()
    clearScheme()
    menuVisible.value = false
    step.value = SCHEME_STEP.SCHEME
  }
}

async function getMatchData(loadMore = false, showLoading = true) {
  if (step.value !== SCHEME_STEP.SCHEME) return
  try {
    if (showLoading) uni.showLoading()
    let params = name.value
      ? { dataType: dataType.value, pageNo: pageNo.value + 1, pageSize: 20, name: name.value }
      : { dataType: dataType.value, pageNo: pageNo.value + 1, pageSize: 20 }
    const { list, total } = await get24HoursMatchList(params)
    pageNo.value = pageNo.value + 1
    totalPage.value = ceil(total / 20)
    matchList.value = loadMore ? [...matchList.value, ...list] : list
  } finally {
    if (showLoading) uni.hideLoading()
  }
}

function handleSearch(key: string) {
  name.value = key
  pageNo.value = 0
  getMatchData()
}

const draft = ref<IArticleDraft | null>(null)

onReachBottom(() => {
  console.log('reach bottom', pageNo.value, totalPage.value)
  if (pageNo.value < totalPage.value) getMatchData(true)
})

onLoad(async ({ id }) => {
  try {
    uni.showLoading()
    getMatchData()
    if (id) {
      step.value = SCHEME_STEP.CONTENT
    } else {
      draft.value = await getArticleDraft()
      if (draft.value) {
        step.value = SCHEME_STEP.CONTENT
      }
    }
  } finally {
    uni.hideLoading()
  }
})
</script>
