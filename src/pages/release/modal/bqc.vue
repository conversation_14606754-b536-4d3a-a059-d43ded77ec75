<template>
  <wd-popup v-model="showModal" custom-style="width:calc(100% - 90rpx);border-radius:16rpx;">
    <view class="flex flex-col justify-between items-center pt-35rpx px-30rpx pb-40rpx">
      <view class="flex w-full">
        <view class="flex-1 text-right">
          <text class="text-black text-32rpx">{{ match.shortHomeName }}</text>
          <text class="text-#999 text-26rpx">（主）</text>
        </view>
        <text class="mx-10rpx text-32rpx text-black">VS</text>
        <view class="flex-1 text-left">
          <text class="text-black text-32rpx">{{ match.shortAwayName }}</text>
          <text class="text-#999 text-26rpx">（客）</text>
        </view>
      </view>
      <text class="mt-18rpx text-#999 text-26rpx text-center">
        竞猜主队在上半场和全场比赛（不含加时和点球）的胜平负
      </text>
      <view class="flex w-full mt-20rpx mb-30rpx">
        <view
          class="center w-38rpx bg-#6EBDBD text-20rpx text-white"
          style="writing-mode: vertical-lr"
        >
          半全场
        </view>
        <view class="flex-1 grid grid-cols-3">
          <view
            v-for="(e, i) in BQC_SCORE"
            :key="e"
            :class="checked.includes(e) ? 'bg-#D1302E' : ''"
            @click="handleCheck(e)"
            class="flex flex-col justify-center items-center h-88rpx border-1rpx border-solid border-#ddd"
          >
            <text class="text-28rpx" :class="checked.includes(e) ? 'text-white' : 'text-#333'">
              {{ e }}
            </text>
            <text
              v-if="match && match.matchPlayOdds.bqc"
              class="text-24rpx"
              :class="checked.includes(e) ? 'text-white' : 'text-#999'"
            >
              {{ match.matchPlayOdds.bqc.split(',')[i] }}
            </text>
          </view>
        </view>
      </view>
      <view class="flex gap-x-12rpx w-full">
        <wd-button :round="false" type="info" custom-style="flex:1 1 0" @click="handleClose">
          取消
        </wd-button>
        <wd-button :round="false" custom-style="flex:1 1 0" @click="handleConfirm">确认</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { IMatchInfoData } from '@/api/match'
import { useMatchSchemeStore } from '@/store/matchScheme'
import { PLAY_TYPE } from '@/utils/enum'
import { isEmpty, pull, uniq } from 'lodash-es'
import { BQC_SCORE } from '@/utils/constant'

const props = defineProps<{ match: IMatchInfoData; limit: number; resultType: number }>()
const { scheme, setBonusScheme, changeSchemePlay } = useMatchSchemeStore()
const showModal = ref(false)
const checked = ref<string[]>([])
const { match } = toRefs(props)
function open() {
  const { matchId } = props.match
  const mr = scheme.bonus.find((b) => b.matchId === matchId)
  if (!mr) {
    checked.value = []
  } else {
    const ret = mr.matchPlays.find((m) => m.playId === PLAY_TYPE.BQC)
    checked.value = ret ? ret.result.split(',') : []
  }
  showModal.value = true
}
defineExpose({ open })

function handleClose() {
  showModal.value = false
}

function handleCheck(e: string) {
  if (checked.value.includes(e)) {
    pull(checked.value, e)
  } else {
    if (checked.value.length + 1 > props.limit) {
      uni.showToast({ title: '超出可选范围', icon: 'none' })
      return
    }
    checked.value.push(e)
  }
}

function handleConfirm() {
  const {
    matchId,
    homeName,
    awayName,
    roundName,
    matchTime,
    matchPlayOdds,
    shortHomeName,
    shortAwayName,
    comp,
    shortComp,
    issueNum,
    week,
  } = props.match
  const bonus = scheme.bonus
  const resultType = props.resultType
  const type = 1
  const opinion = 0
  const m = bonus.find((b) => b.matchId === matchId)
  const c = checked.value

  if (!m) {
    setBonusScheme([
      ...bonus,
      {
        matchId,
        homeName,
        awayName,
        roundName,
        matchTime,
        matchPlayOdds,
        shortHomeName,
        shortAwayName,
        comp,
        shortComp,
        issueNum,
        week,
        matchPlays: [
          {
            type,
            playId: PLAY_TYPE.BQC,
            opinion,
            resultType,
            result: c.join(','),
          },
        ],
      },
    ])

    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length
    if (count === 1) {
      changeSchemePlay(4)
    } else if (count === 2) {
      changeSchemePlay(3)
    } else {
      changeSchemePlay(5)
    }
    handleClose()
    return
  }

  const mp = m.matchPlays.find((m) => m.playId === PLAY_TYPE.BQC)
  if (!mp) {
    setBonusScheme(
      bonus.map((m) => {
        return {
          ...m,
          matchPlays: [
            ...m.matchPlays,
            {
              type,
              playId: PLAY_TYPE.BQC,
              opinion,
              resultType,
              result: c.join(','),
            },
          ],
        }
      }),
    )
    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length
    if (count === 1) {
      changeSchemePlay(4)
    } else if (count === 2) {
      changeSchemePlay(3)
    } else {
      changeSchemePlay(5)
    }
    handleClose()
    return
  }

  if (isEmpty(c) && m.matchPlays.length === 1) {
    setBonusScheme(bonus.filter((e) => e.matchId !== matchId))
    handleClose()
    return
  }

  isEmpty(c)
    ? setBonusScheme(
        bonus.map((e) =>
          e.matchId === matchId
            ? {
                ...e,
                matchPlays: e.matchPlays.filter((m) => m.playId !== PLAY_TYPE.BQC),
              }
            : e,
        ),
      )
    : setBonusScheme(
        bonus.map((e) =>
          e.matchId === matchId
            ? {
                ...e,
                matchPlays: e.matchPlays.map((m) =>
                  m.playId === PLAY_TYPE.BQC
                    ? {
                        ...m,
                        result: c.join(','),
                      }
                    : m,
                ),
              }
            : e,
        ),
      )
  const count = uniq([
    ...scheme.main.map((s) => s.matchId),
    ...scheme.bonus.map((s) => s.matchId),
  ]).length
  if (count === 1) {
    changeSchemePlay(4)
  } else if (count === 2) {
    changeSchemePlay(3)
  } else {
    changeSchemePlay(5)
  }
  handleClose()
}
</script>
