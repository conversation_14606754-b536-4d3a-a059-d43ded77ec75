<template>
  <wd-popup v-model="showModal" custom-style="width:calc(100% - 90rpx);border-radius:16rpx;">
    <view class="flex flex-col justify-between items-center mt-26rpx mb-38rpx px-30rpx">
      <view class="flex w-full">
        <view class="flex-1 text-right">
          <text class="text-black text-32rpx">{{ match.shortHomeName }}</text>
          <text class="text-#999 text-26rpx">（主）</text>
        </view>
        <text class="mx-10rpx text-32rpx text-black">VS</text>
        <view class="flex-1 text-left">
          <text class="text-black text-32rpx">{{ match.shortAwayName }}</text>
          <text class="text-#999 text-26rpx">（客）</text>
        </view>
      </view>
      <text class="mt-15rpx text-#999 text-24rpx">（进球数）</text>
      <view class="grid grid-cols-4 w-full mt-24rpx mb-40rpx">
        <view
          v-for="(e, i) in JQ_SCORE"
          :key="e"
          @click="handleCheck(e)"
          :class="checked.includes(e) ? 'bg-#D1302E' : ''"
          class="flex flex-col justify-center items-center h-90rpx border-1rpx border-solid border-#ddd"
        >
          <text class="text-28rpx" :class="checked.includes(e) ? 'text-white' : 'text-#333'">
            {{ e }}
          </text>
          <text
            v-if="match.matchPlayOdds && match.matchPlayOdds.jq"
            class="text-24rpx"
            :class="checked.includes(e) ? 'text-white' : 'text-#999'"
          >
            {{ match.matchPlayOdds.jq.split(',')[i] }}
          </text>
        </view>
      </view>
      <view class="flex gap-x-12rpx w-full">
        <wd-button :round="false" type="info" custom-style="flex:1 1 0" @click="handleClose">
          取消
        </wd-button>
        <wd-button :round="false" custom-style="flex:1 1 0" @click="handleConfirm">确认</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { IMatchInfoData } from '@/api/match'
import { useMatchSchemeStore } from '@/store/matchScheme'
import { PLAY_TYPE, SCHEME_CATEGORY } from '@/utils/enum'
import { JQ_SCORE } from '@/utils/constant'
import { isEmpty, pull, uniq } from 'lodash-es'

const showModal = ref(false)

const props = defineProps<{
  match: IMatchInfoData
  limit: number
  resultType: number
  category: SCHEME_CATEGORY
}>()
const { scheme, setBonusScheme, changeSchemePlay } = useMatchSchemeStore()
const checked = ref<string[]>([])
function open() {
  const { matchId } = props.match
  const mr = scheme.bonus.find((b) => b.matchId === matchId)
  if (!mr) {
    checked.value = []
  } else {
    const ret = mr.matchPlays.find((m) => m.playId === PLAY_TYPE.JQ)
    checked.value = ret ? ret.result.split(',') : []
  }
  showModal.value = true
}
defineExpose({ open })

function handleClose() {
  showModal.value = false
}

function handleCheck(e: string) {
  if (checked.value.includes(e)) {
    pull(checked.value, e)
  } else {
    if (checked.value.length + 1 > props.limit) {
      uni.showToast({ title: '超出可选范围', icon: 'none' })
      return
    }
    checked.value.push(e)
  }
}

function handleConfirm() {
  const {
    matchId,
    homeName,
    awayName,
    roundName,
    matchTime,
    matchPlayOdds,
    shortHomeName,
    shortAwayName,
    comp,
    shortComp,
    issueNum,
    week,
  } = props.match
  const bonus = scheme.bonus
  const resultType = props.resultType
  const type = 1
  const opinion = 0
  const m = bonus.find((b) => b.matchId === matchId)
  const c = checked.value

  if (!m) {
    setBonusScheme([
      ...bonus,
      {
        matchId,
        homeName,
        awayName,
        roundName,
        matchTime,
        matchPlayOdds,
        shortHomeName,
        shortAwayName,
        comp,
        shortComp,
        issueNum,
        week,
        matchPlays: [
          {
            type,
            playId: PLAY_TYPE.JQ,
            opinion,
            resultType,
            result: c.join(','),
          },
        ],
      },
    ])

    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length
    if (props.category === SCHEME_CATEGORY.JZ) {
      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }
    } else {
      if (count === 1) {
        changeSchemePlay(6)
      } else if (count === 2) {
        changeSchemePlay(10)
      } else {
        changeSchemePlay(11)
      }
    }

    handleClose()
    return
  }

  const mp = m.matchPlays.find((m) => m.playId === PLAY_TYPE.JQ)
  if (!mp) {
    setBonusScheme(
      bonus.map((m) => {
        return {
          ...m,
          matchPlays: [
            ...m.matchPlays,
            {
              type,
              playId: PLAY_TYPE.JQ,
              opinion,
              resultType,
              result: c.join(','),
            },
          ],
        }
      }),
    )

    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length
    if (props.category === SCHEME_CATEGORY.JZ) {
      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }
    } else {
      if (count === 1) {
        changeSchemePlay(6)
      } else if (count === 2) {
        changeSchemePlay(10)
      } else {
        changeSchemePlay(11)
      }
    }
    handleClose()
    return
  }

  if (isEmpty(c) && m.matchPlays.length === 1) {
    setBonusScheme(bonus.filter((e) => e.matchId !== matchId))
    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length
    if (props.category === SCHEME_CATEGORY.JZ) {
      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }
    } else {
      if (count === 1) {
        changeSchemePlay(6)
      } else if (count === 2) {
        changeSchemePlay(10)
      } else {
        changeSchemePlay(11)
      }
    }
    handleClose()
    return
  }

  isEmpty(c)
    ? setBonusScheme(
        bonus.map((e) =>
          e.matchId === matchId
            ? {
                ...e,
                matchPlays: e.matchPlays.filter((m) => m.playId !== PLAY_TYPE.JQ),
              }
            : e,
        ),
      )
    : setBonusScheme(
        bonus.map((e) =>
          e.matchId === matchId
            ? {
                ...e,
                matchPlays: e.matchPlays.map((m) =>
                  m.playId === PLAY_TYPE.JQ
                    ? {
                        ...m,
                        result: c.join(','),
                      }
                    : m,
                ),
              }
            : e,
        ),
      )
  const count = uniq([
    ...scheme.main.map((s) => s.matchId),
    ...scheme.bonus.map((s) => s.matchId),
  ]).length
  if (props.category === SCHEME_CATEGORY.JZ) {
    if (count === 1) {
      changeSchemePlay(4)
    } else if (count === 2) {
      changeSchemePlay(3)
    } else {
      changeSchemePlay(5)
    }
  } else {
    if (count === 1) {
      changeSchemePlay(6)
    } else if (count === 2) {
      changeSchemePlay(10)
    } else {
      changeSchemePlay(11)
    }
  }
  handleClose()
}
</script>
