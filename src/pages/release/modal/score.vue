<template>
  <wd-popup v-model="showModal" custom-style="width:calc(100% - 90rpx);border-radius:16rpx;">
    <view class="flex flex-col justify-between items-center py-35rpx px-30rpx">
      <view class="flex w-full">
        <view class="flex-1 text-right">
          <text class="text-black text-32rpx">{{ match.shortHomeName }}</text>
          <text class="text-#999 text-26rpx">（主）</text>
        </view>
        <text class="mx-10rpx text-32rpx text-black">VS</text>
        <view class="flex-1 text-left">
          <text class="text-black text-32rpx">{{ match.shortAwayName }}</text>
          <text class="text-#999 text-26rpx">（客）</text>
        </view>
      </view>
      <view class="flex flex-col gap-y-24rpx w-full mt-30rpx mb-45rpx">
        <view class="flex f-full">
          <view
            class="center w-42rpx bg-#6EBDBD text-20rpx leading-28rpx text-white"
            style="writing-mode: vertical-lr"
          >
            主胜比分
          </view>
          <view class="flex-1 grid grid-cols-5">
            <view
              v-for="(e, i) in BF_SCORES.slice(0, 13)"
              @click="handleCheck(e)"
              :key="e"
              class="flex flex-col justify-center items-center h-80rpx last:col-span-3 border-1rpx border-solid border-#ddd"
              :class="checked.includes(e) ? 'bg-#D1302E' : ''"
            >
              <text class="text-28rpx" :class="checked.includes(e) ? 'text-white' : 'text-#333'">
                {{ e }}
              </text>
              <text
                v-if="match.matchPlayOdds && match.matchPlayOdds.bf"
                class="text-24rpx"
                :class="checked.includes(e) ? 'text-white' : 'text-#999'"
              >
                {{ match.matchPlayOdds.bf.split(',')[i] }}
              </text>
            </view>
          </view>
        </view>
        <view class="flex f-full">
          <view
            class="center w-42rpx bg-#6BB385 text-20rpx leading-28rpx text-white"
            style="writing-mode: vertical-lr"
          >
            平比分
          </view>
          <view class="flex-1 grid grid-cols-5">
            <view
              v-for="(e, i) in BF_SCORES.slice(13, 18)"
              @click="handleCheck(e)"
              :class="checked.includes(e) ? 'bg-#D1302E' : ''"
              class="flex flex-col justify-center items-center h-80rpx border-1rpx border-solid border-#ddd"
              :key="e"
            >
              <text class="text-28rpx" :class="checked.includes(e) ? 'text-white' : 'text-#333'">
                {{ e }}
              </text>
              <text
                v-if="match.matchPlayOdds && match.matchPlayOdds.bf"
                class="text-24rpx"
                :class="checked.includes(e) ? 'text-white' : 'text-#999'"
              >
                {{ match.matchPlayOdds.bf.split(',')[13 + i] }}
              </text>
            </view>
          </view>
        </view>
        <view class="flex f-full">
          <view
            class="center w-42rpx bg-#6EBDBD text-20rpx leading-28rpx text-white"
            style="writing-mode: vertical-lr"
          >
            客胜比分
          </view>
          <view class="flex-1 grid grid-cols-5">
            <view
              v-for="(e, i) in BF_SCORES.slice(18)"
              @click="handleCheck(e)"
              :key="e"
              :class="checked.includes(e) ? 'bg-#D1302E' : ''"
              class="flex flex-col justify-center items-center h-80rpx last:col-span-3 border-1rpx border-solid border-#ddd"
            >
              <text class="text-28rpx" :class="checked.includes(e) ? 'text-white' : 'text-#333'">
                {{ e }}
              </text>
              <text
                v-if="match.matchPlayOdds && match.matchPlayOdds.bf"
                class="text-24rpx"
                :class="checked.includes(e) ? 'text-white' : 'text-#999'"
              >
                {{ match.matchPlayOdds.bf.split(',')[18 + i] }}
              </text>
            </view>
          </view>
        </view>
      </view>
      <view class="flex gap-x-12rpx w-full">
        <wd-button :round="false" type="info" custom-class="btn" @click="handleClose">
          取消
        </wd-button>
        <wd-button :round="false" custom-class="btn" @click="handleConfirm">确认</wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { IMatchInfoData } from '@/api/match'
import { useMatchSchemeStore } from '@/store/matchScheme'
import { BF_SCORES } from '@/utils/constant'
import { PLAY_TYPE, SCHEME_CATEGORY } from '@/utils/enum'
import { isEmpty, pull, uniq } from 'lodash-es'

const props = defineProps<{
  match: IMatchInfoData
  limit: number
  resultType: number
  category: SCHEME_CATEGORY
}>()
const { scheme, setBonusScheme, changeSchemePlay } = useMatchSchemeStore()

const showModal = ref(false)
function open() {
  const { matchId } = props.match
  const mr = scheme.bonus.find((b) => b.matchId === matchId)
  if (!mr) {
    checked.value = []
  } else {
    const ret = mr.matchPlays.find((m) => m.playId === PLAY_TYPE.SCORE)
    checked.value = ret ? ret.result.split(',') : []
  }
  showModal.value = true
}
defineExpose({ open })

const checked = ref<string[]>([])

const { match } = toRefs(props)

function handleCheck(e: string) {
  if (checked.value.includes(e)) {
    pull(checked.value, e)
  } else {
    if (checked.value.length + 1 > props.limit) {
      uni.showToast({ title: '超出可选范围', icon: 'none' })
      return
    }
    checked.value.push(e)
  }
}

function handleClose() {
  showModal.value = false
}

function handleConfirm() {
  const {
    matchId,
    homeName,
    awayName,
    roundName,
    matchTime,
    matchPlayOdds,
    shortHomeName,
    shortAwayName,
    comp,
    shortComp,
    issueNum,
    week,
  } = props.match
  const bonus = scheme.bonus
  const resultType = props.resultType
  const type = 1
  const opinion = 0
  const m = bonus.find((b) => b.matchId === matchId)
  const c = checked.value

  if (!m) {
    setBonusScheme([
      ...bonus,
      {
        matchId,
        homeName,
        awayName,
        roundName,
        matchTime,
        matchPlayOdds,
        shortHomeName,
        shortAwayName,
        comp,
        shortComp,
        issueNum,
        week,
        matchPlays: [
          {
            type,
            playId: PLAY_TYPE.SCORE,
            opinion,
            resultType,
            result: c.join(','),
          },
        ],
      },
    ])
    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length
    if (props.category === SCHEME_CATEGORY.JZ) {
      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }
    } else {
      if (count === 1) {
        changeSchemePlay(6)
      } else if (count === 2) {
        changeSchemePlay(10)
      } else {
        changeSchemePlay(11)
      }
    }
    handleClose()
    return
  }

  const mp = m.matchPlays.find((m) => m.playId === PLAY_TYPE.SCORE)
  if (!mp) {
    setBonusScheme(
      bonus.map((m) => {
        return {
          ...m,
          matchPlays: [
            ...m.matchPlays,
            {
              type: 0,
              playId: PLAY_TYPE.SCORE,
              opinion,
              resultType,
              result: c.join(','),
            },
          ],
        }
      }),
    )
    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length
    if (props.category === SCHEME_CATEGORY.JZ) {
      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }
    } else {
      if (count === 1) {
        changeSchemePlay(6)
      } else if (count === 2) {
        changeSchemePlay(10)
      } else {
        changeSchemePlay(11)
      }
    }
    handleClose()
    return
  }

  if (isEmpty(c) && m.matchPlays.length === 1) {
    setBonusScheme(bonus.filter((e) => e.matchId !== matchId))
    const count = uniq([
      ...scheme.main.map((s) => s.matchId),
      ...scheme.bonus.map((s) => s.matchId),
    ]).length
    if (props.category === SCHEME_CATEGORY.JZ) {
      if (count === 1) {
        changeSchemePlay(4)
      } else if (count === 2) {
        changeSchemePlay(3)
      } else {
        changeSchemePlay(5)
      }
    } else {
      if (count === 1) {
        changeSchemePlay(6)
      } else if (count === 2) {
        changeSchemePlay(10)
      } else {
        changeSchemePlay(11)
      }
    }
    handleClose()
    return
  }

  isEmpty(c)
    ? setBonusScheme(
        bonus.map((e) =>
          e.matchId === matchId
            ? {
                ...e,
                matchPlays: e.matchPlays.filter((m) => m.playId !== PLAY_TYPE.SCORE),
              }
            : e,
        ),
      )
    : setBonusScheme(
        bonus.map((e) =>
          e.matchId === matchId
            ? {
                ...e,
                matchPlays: e.matchPlays.map((m) =>
                  m.playId === PLAY_TYPE.SCORE
                    ? {
                        ...m,
                        result: c.join(','),
                      }
                    : m,
                ),
              }
            : e,
        ),
      )
  const count = uniq([
    ...scheme.main.map((s) => s.matchId),
    ...scheme.bonus.map((s) => s.matchId),
  ]).length
  if (props.category === SCHEME_CATEGORY.JZ) {
    if (count === 1) {
      changeSchemePlay(4)
    } else if (count === 2) {
      changeSchemePlay(3)
    } else {
      changeSchemePlay(5)
    }
  } else {
    if (count === 1) {
      changeSchemePlay(6)
    } else if (count === 2) {
      changeSchemePlay(10)
    } else {
      changeSchemePlay(11)
    }
  }
  handleClose()
}
</script>

<style lang="scss" scoped>
.btn {
  flex: 1;
}
</style>
