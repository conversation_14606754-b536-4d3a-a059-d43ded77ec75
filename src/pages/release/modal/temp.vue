<template>
  <wd-popup
    v-model="showModal"
    custom-style="width:calc(100% - 90rpx);border-radius:16rpx;"
    @close="handleClose"
  >
    <view class="flex flex-col justify-between items-center gap-y-40rpx mt-26rpx mb-38rpx px-30rpx">
      <text>模版发布</text>
      <wd-textarea v-model="temp" placeholder="请填写评价" custom-class="textarea" clearable />
      <wd-button :round="false" @click="submit">发布</wd-button>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
import { getOrcInfo } from '@/api/article'
import { IMatchScheme } from '@/api/match'
import { WEEK_CN } from '@/utils/constant'
import { isEmpty, omit } from 'lodash-es'
import { useMatchSchemeStore } from '@/store/matchScheme'

const { setMainScheme, changeSchemePlay, scheme } = useMatchSchemeStore()

const showModal = ref(true)

const temp = ref('')
// function open() {
//   showModal.value = true
// }
// defineExpose({ open })
const emit = defineEmits<{
  (e: 'close'): void
  (e: 'submit', dataType: number): void
}>()

function handleClose() {
  emit('close')
}

async function submit() {
  try {
    uni.showLoading()
    const res = await getOrcInfo(temp.value)
    const { schemePlay, matchScheme, dataType } = res
    let ms: Omit<IMatchScheme, 'week'>[] = JSON.parse(matchScheme)
    
    if (isEmpty(ms)) {
      uni.showToast({ title: '请检查模板内容' })
      return
    }
    if ([3, 4, 5].includes(schemePlay)) {
      // 竞足
      ms = ms.map((m) => {
        let { issueNum, ...rest } = m
        const weekIndex = issueNum.substring(0, 1)
        issueNum = issueNum.substring(1)
        const week = weekIndex === '7' ? WEEK_CN[0] : WEEK_CN[+weekIndex]
        return {
          week,
          issueNum,
          ...rest,
        }
      })
    } else {
      ms = ms.map((m) =>
        omit(m, ['comp', 'shortHomeName', 'shortAwayName', 'shortComp', 'issueNum']),
      )
    }

    console.log(`🚀 ~ submit ~ matchScheme:`, matchScheme)
    console.log(`🚀 ~ submit ~ schemePlay:`, schemePlay)
    console.log(`🚀 ~ submit ~ dataType:`, dataType) // 十四场: 1 竞足: 3 北单: 2

    setMainScheme(ms)

    // 设置玩法 14场:1 任9:2
    changeSchemePlay(schemePlay)

    emit('submit', dataType)

  } catch(error) {
    await uni.showToast({
      title: error?.data?.msg || '请检查模板内容',
      icon: 'error',
    })
  } finally {
    uni.hideLoading()
  }
}
</script>

<style lang="scss" scoped>
.textarea {
  width: 100%;

  :deep(.wd-textarea__inner) {
    height: 50vh;
  }
}
</style>
