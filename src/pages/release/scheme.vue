<template>
  <view class="h-full overflow-auto box-border">
    <view class="content">
      <template v-if="category === SCHEME_CATEGORY.JZ">
        <jz :match-group="matchGroup" :play-method="gamePlay" @search="handleSearch" />
      </template>
      <template v-else-if="category === SCHEME_CATEGORY.BD">
        <bd :match-group="matchGroup" :play-method="gamePlay" @search="handleSearch" />
      </template>
      <template v-else-if="category === SCHEME_CATEGORY.MATCH_LOTTERY">
        <fourteen-nine ref="fourteenNineRef" @update-count="updateSelectedCount" />
      </template>
    </view>
    <view
      class="fixed bottom-0 flex items-center w-full h-120rpx px-20rpx bg-white box-border"
    >
      <view class="text-26rpx text-#333">
        已选择
        <text class="text-#D1302E">{{ selectedCount }}</text>
        场
      </view>
      <view class="flex gap-x-24rpx ml-auto">
        <wd-button :round="false" type="info" @click="resetScheme">重置</wd-button>
        <wd-button :round="false" :disabled="validNext" @click="handleNext">下一步</wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { SCHEME_CATEGORY } from '@/utils/enum'
import jz from './components/jz/index.vue'
import bd from './components/bd/index.vue'
import fourteenNine from './components/fourteen-nine/index.vue'
import { IMatchInfoData } from '@/api/match'
import { IGamePlayRecord, getAllPlayMethod } from '@/api/author'
import { useMatchSchemeStore } from '@/store/matchScheme'
import { isEmpty, uniq } from 'lodash-es'
import { SCHEME_TYPE } from '@/utils/enum'

const props = defineProps<{
  category: SCHEME_CATEGORY
  matchGroup: { [key: string]: IMatchInfoData[] }
}>()

const emit = defineEmits<{
  (e: 'search', key: string): void
  (e: 'next')
}>()

const { scheme } = useMatchSchemeStore()

const gamePlay = ref<IGamePlayRecord[]>([])
const fourteenNineRef = ref()

// 选中的比赛数量
const selectedCount = computed(() => {
  return uniq([...scheme.main.map((s) => s.matchId), ...scheme.bonus.map((s) => s.matchId)]).length
})

const validNext = computed(() => {
  return isEmpty(scheme.main) && isEmpty(scheme.bonus)
})

function handleNext() {
  if(useMatchSchemeStore().scheme.schemePlay === SCHEME_TYPE.MATCH_LOTTERY) {
    if(useMatchSchemeStore().scheme.main?.length !== 14) {
      uni.showToast({
        title: '请选择14场比赛',
        icon: 'none',
      })
      return
    }
  }

  if(useMatchSchemeStore().scheme.schemePlay === SCHEME_TYPE.ANY_NINE) {
    if(useMatchSchemeStore().scheme.main?.length !== 9) {
      uni.showToast({
        title: '请选择9场比赛',
        icon: 'none',
      })
      return
    }
  }
  emit('next')
}

// 更新选中数量
const updateSelectedCount = (count: number) => {
  // selectedCount.value = count
}

function handleSearch(v: string) {
  emit('search', v)
}

// 重置选择
const resetSelection = () => {
  if (props.category === SCHEME_CATEGORY.MATCH_LOTTERY && fourteenNineRef.value) {
    fourteenNineRef.value.resetSelection?.()
  }
}

const { clearScheme } = useMatchSchemeStore()

onLoad(async () => {
  try {
    uni.showLoading()
    gamePlay.value = await getAllPlayMethod()
  } finally {
    uni.hideLoading()
  }
})

function resetScheme() {
  uni.showModal({
    title: '确认取消所有选择吗？',
    success: (success) => {
      if (success.confirm) {
        if (props.category === SCHEME_CATEGORY.MATCH_LOTTERY) {
          fourteenNineRef.value.resetSelection?.()
        }
        clearScheme()
      }
    },
  })
}
</script>

<style lang="scss" scoped>
.content {
  height: calc(100% - 140rpx);
  padding: 9rpx 0;
  overflow: hidden;
  overflow-y: auto;
}
</style>
