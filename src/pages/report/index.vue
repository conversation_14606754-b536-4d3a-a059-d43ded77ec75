<route lang="json5">
{
  style: {
    navigationBarTitleText: '数据报表',
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen">
    <wd-calendar
      v-model="dates"
      allow-same-day
      class="mb-2"
      label="选择日期"
      placeholder="请选择日期"
      type="daterange"
      @confirm="changeDate"
    />
    <scroll-view :show-scrollbar="false" :style="{ 'white-space': 'nowrap' }" scroll-x="true">
      <view class="flex justify-between px-1 mb-5">
        <view :class="activeType === 1 ? 'card active mr-4' : 'card mr-4'" @click="changeData(1)">
          <view class="mb-1">交易总额</view>
          <view class="amount">{{ reprotData?.totalAmount }}</view>
        </view>
        <view :class="activeType === 10 ? 'card active mr-4' : 'card mr-4'" @click="changeData(10)">
          <view class="mb-1">新增付费用户</view>
          <view class="amount">{{ reprotData?.newPayUserNum }}</view>
        </view>
        <view :class="activeType === 7 ? 'card active mr-4' : 'card mr-4'" @click="changeData(7)">
          <view class="mb-1">套餐总额</view>
          <view class="amount">{{ reprotData?.privilegeAmount }}</view>
        </view>
        <view :class="activeType === 8 ? 'card active mr-4' : 'card mr-4'" @click="changeData(8)">
          <view class="mb-1">方案总额</view>
          <view class="amount">{{ reprotData?.orderAmount }}</view>
        </view>
        <view :class="activeType === 9 ? 'card active mr-4' : 'card mr-4'" @click="changeData(9)">
          <view class="mb-1">退款总额</view>
          <view class="amount">{{ reprotData?.refundAmount || 0 }}</view>
        </view>
        <view :class="activeType === 2 ? 'card active mr-4' : 'card mr-4'" @click="changeData(2)">
          <view class="mb-1">购买人数</view>
          <view class="amount">{{ reprotData?.totalBuyUserNum }}</view>
        </view>
        <view :class="activeType === 3 ? 'card active mr-4' : 'card mr-4'" @click="changeData(3)">
          <view class="mb-1">成交率</view>
          <view class="amount">{{ reprotData?.totalTransRate }}%</view>
        </view>
        <view :class="activeType === 4 ? 'card active mr-4' : 'card mr-4'" @click="changeData(4)">
          <view class="mb-2">新增粉丝</view>
          <view class="amount">{{ reprotData?.newUserNum }}</view>
        </view>
        <view :class="activeType === 5 ? 'card active mr-4' : 'card mr-4'" @click="changeData(5)">
          <view class="mb-2">客单价</view>
          <view class="amount">{{ reprotData?.averagePrice }}</view>
        </view>
        <view :class="activeType === 6 ? 'card active mr-4' : 'card mr-4'" @click="changeData(6)">
          <view class="mb-2">发布方案</view>
          <view class="amount">{{ reprotData?.articleNum }}</view>
        </view>
      </view>
    </scroll-view>
    <view class="charts-box">
      <qiun-data-charts :chartData="chartData" :opts="opts" type="column" />
    </view>
    <view v-if="![1, 7, 8, 9, 10].includes(activeType)" class="px-5">客户漏斗</view>
    <view v-if="![1, 7, 8, 9, 10].includes(activeType)" class="charts-box">
      <qiun-data-charts :chartData="pvChartData" :opts="pvOpts" type="funnel" />
    </view>
    <view v-if="[1, 8].includes(activeType)" class="mx-10rpx">
      <view
        class="flex px-20rpx font-500 text-24rpx pb-15rpx border-b border-b-solid border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center">方案名称</text>
        <text class="flex-1 text-center">观看人数</text>
        <text class="flex-1 text-center">购买人数</text>
        <text class="flex-1 text-center">购买金额</text>
      </view>
      <view
        v-if="[8].includes(activeType)"
        v-for="item in articleData"
        :key="item.id"
        class="flex items-center last:border-0 text-#797979 px-20rpx text-22rpx mt-15rpx pb-15rpx border-b border-b-dotted border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center line-clamp-1 break-words">{{ item.title }}</text>
        <text class="flex-1 text-center">{{ item.pvNum }}</text>
        <text class="flex-1 text-center">{{ item.buyNum }}</text>
        <text class="flex-1 text-center">{{ item.authorDivide }}</text>
      </view>
      <view
        v-else
        v-for="item in dataList"
        :key="item.id"
        class="flex items-center last:border-0 text-#797979 px-20rpx text-22rpx mt-15rpx pb-15rpx border-b border-b-dotted border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center line-clamp-1 break-words">{{ item.title }}</text>
        <text class="flex-1 text-center">{{ item.pvNum }}</text>
        <text class="flex-1 text-center">{{ item.buyNum }}</text>
        <text class="flex-1 text-center">{{ item.authorDivide }}</text>
      </view>
    </view>
    <view v-if="[7].includes(activeType)" class="mx-10rpx">
      <view
        class="flex px-20rpx font-500 text-24rpx pb-15rpx border-b border-b-solid border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center">套餐名称</text>
        <text class="flex-1 text-center">购买用户</text>
        <text class="flex-1 text-center">收益金额</text>
        <text class="flex-1 text-center">购买时间</text>
      </view>
      <view
        v-for="item in privilegeList"
        :key="item.id"
        class="flex items-center last:border-0 text-#797979 px-20rpx text-22rpx mt-15rpx pb-15rpx border-b border-b-dotted border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center line-clamp-1 break-words">{{ item.privilegeName }}</text>
        <text class="flex-1 text-center line-clamp-1 break-words">{{ item.nickname }}</text>
        <text class="flex-1 text-center">{{ item.authorDivide }}</text>
        <text class="flex-1 text-center">
          {{ formatDate(item.createTime, 'YYYY-MM-DD HH:mm') }}
        </text>
      </view>
    </view>
    <view v-if="[9].includes(activeType)" class="mx-10rpx">
      <view
        class="flex px-20rpx font-500 text-24rpx pb-15rpx border-b border-b-solid border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center">方案/套餐</text>
        <text class="flex-1 text-center">用户昵称</text>
        <text class="flex-1 text-center">退款金额</text>
        <text class="flex-1 text-center">退款时间</text>
      </view>
      <view
        v-for="item in refundList"
        :key="item.id"
        class="flex items-center last:border-0 text-#797979 px-20rpx text-22rpx mt-15rpx pb-15rpx border-b border-b-dotted border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center line-clamp-1 break-words">{{ item.name }}</text>
        <text class="flex-1 text-center line-clamp-1 break-words">{{ item.nickname }}</text>
        <text class="flex-1 text-center">{{ item.amount }}</text>
        <text class="flex-1 text-center">
          {{ formatDate(item.createTime, 'YYYY-MM-DD HH:mm') }}
        </text>
      </view>
    </view>
    <!--  新增付费用户  -->
    <view v-if="[10].includes(activeType)" class="mx-10rpx">
      <view
        class="flex px-20rpx font-500 text-24rpx pb-15rpx border-b border-b-solid border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center">用户昵称</text>
        <text class="flex-1 text-center">购买方案</text>
        <text class="flex-1 text-center">消费金额</text>
        <text class="flex-1 text-center">消费时间</text>
      </view>
      <view
        v-for="item in newUserList"
        :key="item.id"
        class="flex items-center last:border-0 text-#797979 px-20rpx text-22rpx mt-15rpx pb-15rpx border-b border-b-dotted border-b-#797979 border-b-opacity-20"
      >
        <text class="flex-1 text-center">{{ item.nickname }}</text>
        <text class="flex-1 text-center line-clamp-1 break-words">{{ item.title }}</text>
        <text class="flex-1 text-center">{{ item.amount }}</text>
        <text class="flex-1 text-center">{{ formatDate(item.createTime, 'YYYY-MM-DD') }}</text>
      </view>
    </view>
  </view>
  <back />
</template>

<script lang="ts" setup>
import {
  getSaleReportData,
  getArticleList,
  getTotalList,
  getSalePrivilegeListData,
  getNewPayUserData,
} from '@/service/userService'
import { getRefundOrderList } from '@/api/combo'
import dayjs from 'dayjs'
import back from '@/components/back/index.vue'

const params = ref({
  startDate: '',
  endDate: '',
})
const dataList = ref([])
const privilegeList = ref([])
const refundList = ref([])
const dates = ref([])
const newUserList = reactive([])
const chartData = ref()
const pvChartData = ref()
const reprotData = ref()
const articleData = ref([])

const activeType = ref(1)

const changeData = async (active) => {
  activeType.value = active
  if (activeType.value === 7) {
    // 请求特权数据
    getPrivilegeData()
  } else if (activeType.value === 9) {
    // 请求退款数据
    getRefundData()
  } else if (activeType.value === 10) {
    // 请求新增付费用户数据
    getNewPayUserDataList()
  } else if (activeType.value === 8) {
    // 请求方案数据
    getArticleData()
  }
  const days = []
  const values = []
  let name = ''
  console.info(reprotData)
  reprotData.value.data.forEach((item) => {
    days.push(dayjs(item.day).format('M.D'))
    if (activeType.value === 1) {
      values.push(Number(item.amount) + Number(item.privilegeAmount))
      name = '交易总额'
    } else if (activeType.value === 2) {
      values.push(item.buyUserNum)
      name = '购买人数'
    } else if (activeType.value === 3) {
      values.push(item.transRate)
      name = '成交率'
    } else if (activeType.value === 4) {
      values.push(item.newUserNum)
      name = '新增粉丝'
    } else if (activeType.value === 5) {
      values.push(item.averagePrice)
      name = '客单价'
    } else if (activeType.value === 6) {
      values.push(item.articleNum)
      name = '发布方案'
    } else if (activeType.value === 7) {
      values.push(item.privilegeAmount)
      name = '特权总额'
    } else if (activeType.value === 8) {
      values.push(item.amount)
      name = '方案总额'
    } else if (activeType.value === 9) {
      values.push(item.refundAmount)
      name = '退款总额'
    } else if (activeType.value === 10) {
      values.push(item.newPayUserNum)
      name = '新增付费用户'
    }
  })
  const res = {
    categories: days,
    series: [
      {
        name,
        data: values,
      },
    ],
  }
  chartData.value = JSON.parse(JSON.stringify(res))
}

// 获取新增付费用户数据
const getNewPayUserDataList = async () => {
  newUserList.length = 0
  if (dates.value.length > 1) {
    params.value.startDate = dayjs(dates.value[0]).format('YYYY-MM-DD')
    params.value.endDate = dayjs(dates.value[1]).format('YYYY-MM-DD')
  }
  const data = await getNewPayUserData(params.value)
  if (Array.isArray(data)) {
    Object.assign(newUserList, data)
  }
}

// 获取特权数据
const getPrivilegeData = async () => {
  if (dates.value.length > 1) {
    params.value.startDate = dayjs(dates.value[0]).format('YYYY-MM-DD')
    params.value.endDate = dayjs(dates.value[1]).format('YYYY-MM-DD')
  }
  const data = await getSalePrivilegeListData(params.value)
  if (Array.isArray(data)) {
    privilegeList.value = data
  }
}

const opts = ref({
  color: [
    '#1890FF',
    '#91CB74',
    '#FAC858',
    '#EE6666',
    '#73C0DE',
    '#3CA272',
    '#FC8452',
    '#9A60B4',
    '#ea7ccc',
  ],
  padding: [15, 15, 0, 5],
  // touchMoveLimit: 24,
  enableScroll: false,
  legend: {
    show: false,
  },
  xAxis: {
    disableGrid: true,
    // scrollShow: true,
    // itemCount: 4
    labelCount: 5, // 控制x轴标签显示的数量
  },
  yAxis: {
    data: [
      {
        min: 0,
      },
    ],
  },
  extra: {
    column: {
      type: 'group',
      width: 20,
      activeBgColor: '#000000',
      activeBgOpacity: 0.03,
      label: {
        show: false, // 设置为false以隐藏柱子上的数值
      },
    },
  },
  dataLabel: false,
})

const pvOpts = ref({
  color: ['#1890FF', '#91CB74', '#FAC858'],
  padding: [15, 100, 0, 0],
  enableScroll: false,
  legend: {
    show: false,
  },
  extra: {
    funnel: {
      activeOpacity: 0.3,
      activeWidth: 10,
      border: true,
      borderWidth: 2,
      borderColor: '#FFFFFF',
      fillOpacity: 1,
      labelAlign: 'right',
      type: 'triangle',
    },
  },
})

const getData = async () => {
  if (dates.value.length > 1) {
    params.value.startDate = dayjs(dates.value[0]).format('YYYY-MM-DD')
    params.value.endDate = dayjs(dates.value[1]).format('YYYY-MM-DD')
  }
  console.log(params.value, 'params.value')
  const data = await getTotalList(params.value)

  if (Array.isArray(data)) {
    dataList.value = [...dataList.value, ...data]
  }
}

const getArticleData = async () => {
  if (dates.value.length > 1) {
    params.value.startDate = dayjs(dates.value[0]).format('YYYY-MM-DD')
    params.value.endDate = dayjs(dates.value[1]).format('YYYY-MM-DD')
  }
  console.log(params.value, 'params.value')
  const data = await getArticleList(params.value)

  if (Array.isArray(data)) {
    articleData.value = [...data]
  }
}

const isRefresh = ref(false)
onPullDownRefresh(() => {
  isRefresh.value = true
  getReprotData()
})

const changeDate = () => {
  dataList.value = []
  privilegeList.value = []
  getReprotData()
}
const getReprotData = async (step = 1) => {
  if (dates.value.length > 1) {
    params.value.startDate = dayjs(dates.value[0]).format('YYYY-MM-DD')
    params.value.endDate = dayjs(dates.value[1]).format('YYYY-MM-DD')
  }
  const data = await getSaleReportData(params.value)
  reprotData.value = data
  await changeData(step)
  getData()
  const pvRes = {
    series: [
      {
        data: [
          { name: '浏览量', value: data.totalPvNum, labelText: '浏览量:' + data.totalPvNum },
          { name: '浏览人数', value: data.totalUvNum, labelText: '浏览人数:' + data.totalUvNum },
          {
            name: '购买人数',
            value: data.totalBuyUserNum,
            labelText: '购买人数:' + data.totalBuyUserNum,
          },
        ],
      },
    ],
  }
  pvChartData.value = JSON.parse(JSON.stringify(pvRes))
  if (isRefresh.value) {
    // 提示刷新成功
    uni.showToast({ title: '刷新成功', icon: 'none' })
  }
  isRefresh.value = false
  uni.stopPullDownRefresh()
}

// 计算默认近七天日期
const initDate = () => {
  const today = dayjs().valueOf()
  const sevenDaysAgo = dayjs().subtract(7, 'day').valueOf()
  dates.value[1] = today
  dates.value[0] = sevenDaysAgo
}

// 格式化日期
const formatDate = (date, type = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(date).format(type)
}

// 获取退款数据
const getRefundData = async () => {
  if (dates.value.length > 1) {
    params.value.startDate = dayjs(dates.value[0]).format('YYYY-MM-DD')
    params.value.endDate = dayjs(dates.value[1]).format('YYYY-MM-DD')
  }
  const data = await getRefundOrderList(params.value)
  if (data && typeof data === 'object' && 'list' in data) {
    refundList.value = data.list as any[]
  } else {
    refundList.value = []
  }
}

onLoad(({step}) => {
  activeType.value = step ? Number(step) : 1
  if(step){
    const today = dayjs().valueOf()
    dates.value[1] = today
    dates.value[0] = today
  }else{
    initDate()
  }
  getReprotData(activeType.value)
})

onMounted(() => {
})
</script>

<style lang="scss" scoped>
/* 隐藏右侧箭头 */
:deep(.wd-icon-arrow-right) {
  display: none !important;
}

/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
.charts-box {
  width: 100%;
  margin-bottom: 50rpx;
}

.active {
  color: #d1302e !important;
  background: rgba(209, 48, 46, 0.05) !important;
  border: 1px dashed #d1302e !important;
}

.card {
  width: 180rpx;
  padding: 30rpx 20rpx;
  font-family: 'PingFang SC';
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 20rpx;
  text-align: center;
  background: rgba(0, 0, 0, 0.02);
  border: 1px dashed rgba(0, 0, 0, 0.3);
  border-radius: 12px;

  .amount {
    margin-top: 30rpx;
    font-size: 36rpx;
    font-weight: 400;
    line-height: 20rpx;
    text-align: center;
  }
}
</style>
