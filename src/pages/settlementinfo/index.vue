<route lang="json5">
{
  style: {
    navigationBarTitleText: '提现卡号',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="overflow-hidden bg-[#f4f8fa] min-h-screen">
    <wd-form ref="form" :model="fromData" :errorType="errorType">
      <wd-cell title="提现方式" title-width="180rpx" prop="settleType" required custom-class="flex">
        <wd-radio-group
          checked-color="#D1302E"
          prop="settleType"
          shape="dot"
          center
          v-model="fromData.settleType"
          inline
        >
          <wd-radio :value="1">支付宝</wd-radio>
          <wd-radio :value="2">银行卡</wd-radio>
        </wd-radio-group>
      </wd-cell>
      <!-- <wd-input
        label="手续费"
        label-width="180rpx"
        prop="rate"
        disabled
        v-model="fromData.rate"
        :rules="[{ required: true }]"
      >
        <template #suffix>
          <text>%</text>
        </template>
</wd-input> -->
      <wd-input
        label="提现账号"
        label-width="180rpx"
        prop="account"
        clearable
        v-model="fromData.account"
        placeholder="请输入提现账号"
        :rules="getAccountRules"
      />
      <wd-input
        label="姓名"
        label-width="180rpx"
        prop="name"
        clearable
        v-model="fromData.name"
        placeholder="请输入姓名"
        :rules="[
          { required: true, message: '请填写姓名' },
          { pattern: /^(?:[\u4e00-\u9fa5·]{2,16})$/, message: '请输入正确的姓名' },
        ]"
      />
      <wd-input
        label="身份证号码"
        label-width="180rpx"
        prop="idNo"
        clearable
        v-model="fromData.idNo"
        placeholder="请输入身份证号码"
        :rules="[{ pattern: ID_CARD_REG, message: '请输入正确的身份证号码' }]"
      />
      <wd-picker
        :columns="bankList"
        :error="bankError"
        v-if="fromData.settleType === 2"
        label="开户银行"
        @confirm="fromData.bankId ? (bankError = false) : (bankError = true)"
        label-width="200rpx"
        v-model="fromData.bankId"
        placeholder="请选择开户行"
        :rules="[{ required: true, message: '请选择开户行' }]"
        required
      />
      <view class="footer">
        <wd-button size="large" custom-class="save" @click="save" block>保存设置</wd-button>
      </view>
    </wd-form>
    <back></back>
  </view>
</template>
<script lang="ts" setup>
import {
  getSettlementInfoData,
  saveSettlementInfoData,
  getBankListData,
} from '@/service/userService'
import back from '@/components/back/index.vue'
import { ID_CARD_REG } from '@/utils/regex'

const form = ref()
const errorType = ref<string>('message')
const bankList = ref([])
const bankError = ref(false)
const fromData = ref({
  settleType: 1,
  rate: 0.3,
  account: '',
  name: '',
  idNo: '',
  bankId: undefined,
})
const getData = async () => {
  const data = await getSettlementInfoData()
  fromData.value = data
}
const getBankList = async () => {
  const data = await getBankListData()
  if (Array.isArray(data)) {
    data.forEach((item) => {
      bankList.value.push({ label: item.bankName, value: item.id })
    })
  }
}

const getAccountRules = computed(() => {
  if (fromData.value.settleType === 1) {
    return [
      { required: true, message: '请填写支付宝账号' },
      {
        pattern:
          /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[1589]))\d{8}$/,
        message: '请输入正确的支付宝账号',
      },
    ]
  } else {
    return [
      { required: true, message: '请填写银行卡账号' },
      { pattern: /^[1-9]\d{18}$/, message: '请输入正确的银行卡账号' },
    ]
  }
})

const save = async () => {
  form.value.validate().then(async ({ valid }) => {
    if (fromData.value.settleType === 2 && !fromData.value.bankId) {
      bankError.value = true
    } else {
      bankError.value = false
    }
    if (!valid || bankError.value) {
      return
    }
    const data = await saveSettlementInfoData(fromData.value)
    if (data) {
      uni.showToast({ title: '保存成功' })
    }
  })
}
onMounted(() => {
  getData()
  getBankList()
})
</script>
<style lang="scss" scoped>
:deep(.wd-radio.is-dot.is-checked .wd-radio__shape) {
  background: #d1302e !important;
  border-color: #d1302e !important;
}

.footer {
  bottom: 30rpx;
  padding: 30rpx;
}

.save {
  background: #d1302e !important;
}
</style>
