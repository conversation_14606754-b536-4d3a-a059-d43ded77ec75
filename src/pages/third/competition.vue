<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="pb-120px box-border h-full" :class="touching ? 'overflow-hidden' : 'overflow-auto'">
    <view class="flex h-90rpx border-b-1rpx border-b-solid border-b-#797979 border-b-opacity-20">
      <text class="relative flex-1 flex center" :class="activeClazz(0)" @click="changeMatchType(0)">
        五大联赛
      </text>
      <text class="relative flex-1 flex center" :class="activeClazz(1)" @click="changeMatchType(1)">
        一级赛事
      </text>
    </view>
    <view class="flex items-center h-114rpx px-30rpx">
      <wd-segmented
        :options="processOptions"
        v-model:value="status"
        custom-class="sy-segmented"
        @change="handleStatusChange"
      >
        <template #label="{ option }">
          <text>{{ option.label }}</text>
        </template>
      </wd-segmented>
    </view>
    <!-- 没有赛事的显示 -->
    <template v-if="isEmpty(matchListGroup)">
      <view class="flex flex-col items-center pt-110rpx pb-64rpx">
        <image :src="emptyImg" class="w-488rpx h-266rpx" />
        <text class="mt-30rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
      </view>
    </template>
    <template v-else>
      <template v-if="status === 0">
        <!-- 进行中 -->
        <!-- <wd-picker :columns="matchFilterOptions" v-model="matchId" custom-class="sy-picker"
          @confirm="handlleMatchChange" :safe-area-inset-bottom="true" /> -->
        <sy-select v-model="matchId" :options="matchFilterOptions" @change="handlleMatchChange" />
        <view class="h-420rpx mb-42rpx">
          <iframe :src="matchVideo" class="w-full h-full border-none" />
        </view>
        <view class="mt-[-10rpx] pt-10rpx rounded-t-lg bg-white">
          <!-- tab -->
          <view
            class="flex items-center gap-x-40rpx h-80rpx pl-40rpx border-b-solid border-b-1rpx border-b-black border-b-opacity-10"
          >
            <text
              :class="tabActiveClazz(MATCH_DETAIL_TYPE.STATUS)"
              @click="changeTab(MATCH_DETAIL_TYPE.STATUS)"
            >
              赛况
            </text>
            <text
              :class="tabActiveClazz(MATCH_DETAIL_TYPE.LINEUP)"
              @click="changeTab(MATCH_DETAIL_TYPE.LINEUP)"
              v-if="!fromThird || (fromThird && matchInfo.hasFormation)"
            >
              阵容
            </text>
            <text
              :class="tabActiveClazz(MATCH_DETAIL_TYPE.SCHEME)"
              @click="changeTab(MATCH_DETAIL_TYPE.SCHEME)"
              v-if="!fromThird || (fromThird && matchInfo.hasArticle)"
            >
              方案
            </text>
          </view>
          <!-- 正文 -->
          <template v-if="tab === MATCH_DETAIL_TYPE.STATUS">
            <situations :match="matchInfo" />
          </template>
          <template
            v-if="
              matchInfo.hasFormation &&
              tab === MATCH_DETAIL_TYPE.LINEUP &&
              (!fromThird || (fromThird && matchInfo.hasFormation))
            "
          >
            <lineup :match-id="matchId" :match="matchInfo" />
          </template>
          <template
            v-if="
              matchInfo.hasArticle &&
              tab === MATCH_DETAIL_TYPE.SCHEME &&
              (!fromThird || (fromThird && matchInfo.hasArticle))
            "
          >
            <scheme :match-id="matchId" />
          </template>
        </view>
      </template>
      <template v-else>
        <!-- 即将开始 -->
        <template v-if="isEmpty(matchListGroup)">
          <view class="flex flex-col items-center pt-110rpx pb-64rpx">
            <image :src="emptyImg" class="w-488rpx h-266rpx" />
            <text class="mt-30rpx font-normal text-30rpx text-black text-opacity-30">暂无数据</text>
          </view>
        </template>
        <template v-else>
          <!-- 赛事列表 -->
          <match-list :group="matchListGroup" />
        </template>
      </template>
    </template>
    <view
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      class="fixed center w-130rpx h-130rpx rounded-full bg-#D1302E text-white text-28rpx"
      :style="{ left: position.x + 'px', top: position.y + 'px' }"
      @click.stop="gotoOfficial"
    >
      关注更多
    </view>
  </view>
</template>

<script lang="ts" setup>
import { groupBy, isEmpty } from 'lodash-es'
import { IMatchItem, getMatchDetailById, getThirdMatchList } from '@/api/match'
import matchList from '@/pages/competition/components/matchList.vue'
import situations from '@/pages/matchDetail/situations.vue'
import lineup from '@/pages/matchDetail/lineup.vue'
import scheme from '@/pages/matchDetail/scheme.vue'
import emptyImg from '@/static/images/empty.png'
import { MATCH_DETAIL_TYPE } from '@/utils/enum'
import sySelect from './components/sy-select.vue'
import { getMatchStatus, officialLog } from '@/api/partner'
import { getCurrentPath } from '@/utils/authentication'

const type = ref(1)

const processOptions = [
  { value: 0, label: '进行中' },
  { value: 1, label: '即将开始' },
]

const status = ref(0)

const position = ref({
  x: 0,
  startX: 0,
  y: 0,
  startY: 0,
})

const touching = ref(false)
const fromThird = ref(false)

const matchId = ref<null | number>(null)
const matchInfo = ref<IMatchItem>()
const matchVideo = computed(() =>
  matchId.value
    ? `https://widgets-livetracker.nami.com/zh/football?profile=3ry7uo4x1h7ixre&id=${matchId.value}`
    : '',
)

const matchs = ref<IMatchItem[]>([])
const matchListGroup = computed(() => groupBy(matchs.value, 'date'))
const matchFilterOptions = computed(() =>
  status.value === 0
    ? matchs.value.map(({ id, competitionName, homeTeamName, awayTeamName }) => ({
        label: `【${competitionName}】${homeTeamName} VS ${awayTeamName}`,
        value: id,
      }))
    : [],
)

const tab = ref<MATCH_DETAIL_TYPE>(MATCH_DETAIL_TYPE.STATUS)

const activeClazz = computed(() => {
  return (t: 0 | 1) => {
    return t === type.value
      ? "text-30rpx text-#D1302E text-opacity-90 after:absolute after:bottom-0 after:content-[''] after:w-40rpx after:h-6rpx after:bg-#D1302E rounded-8rpx"
      : 'text-28rpx text-black text-opacity-50'
  }
})

const tabActiveClazz = computed(() => {
  return (type: MATCH_DETAIL_TYPE) => {
    if (type === tab.value) {
      return "flex items-center relative h-full after:absolute after:block after:content-[''] after:w-full  after:h-6rpx after:bg-#D1302E after:bottom-0 text-30rpx text-opacity-90"
    }

    return 'text-28rpx text-black text-opacity-50'
  }
})

async function getMatchDetail(id: number) {
  const match = await getMatchDetailById(id)
  matchInfo.value = match
}

function changeTab(t: MATCH_DETAIL_TYPE) {
  if (tab.value !== t) tab.value = t
}

async function getData(t = 0, s = 0) {
  const { list } = await getThirdMatchList(t, s)
  matchs.value = list
  return list
}

async function changeMatchType(t: 0 | 1) {
  if (t === type.value) return
  type.value = t
  uni.showLoading({ title: '加载中...' })
  const d = await getData(t, status.value)
  if (isEmpty(d)) {
    matchId.value = null
    uni.hideLoading()
    return
  }
  const [{ id }] = d
  if (status.value === 0) {
    matchId.value = id
    await getMatchDetail(id)
  } else {
    matchId.value = null
  }
  uni.hideLoading()
}

async function handleStatusChange({ value }: { value: number }) {
  uni.showLoading({ title: '加载中...' })
  const d = await getData(type.value, value)
  if (isEmpty(d)) {
    matchId.value = null
    uni.hideLoading()
    return
  }

  const [{ id }] = d
  if (value === 0) {
    matchId.value = id
    await getMatchDetail(id)
  } else {
    matchId.value = null
  }
  uni.hideLoading()
}

async function handlleMatchChange({ value }: { value: number }) {
  matchId.value = value
  uni.showLoading({ title: '加载中...' })
  await getMatchDetail(value)
  uni.hideLoading()
}

async function gotoOfficial() {
  uni.showLoading()
  await officialLog(0)
  uni.hideLoading()
  uni.navigateTo({ url: '/pages/official/index' })
}

function handleTouchStart(e) {
  touching.value = true
  const touch = e.touches[0]
  position.value.startX = touch.clientX
  position.value.startY = touch.clientY
}

function handleTouchMove(e) {
  const touch = e.touches[0]
  position.value.x += touch.clientX - position.value.startX
  position.value.y += touch.clientY - position.value.startY

  position.value.startX = touch.clientX
  position.value.startY = touch.clientY
}

function handleTouchEnd() {
  touching.value = false
}

function calcPosition() {
  const { windowWidth: width, windowHeight: heigh } = uni.getSystemInfoSync()
  position.value.x = width - 80
  position.value.y = heigh - 200
}

onLoad(async () => {
  uni.showLoading({ title: '加载中...' })
  calcPosition()
  const path = getCurrentPath()
  fromThird.value = path.includes('third')
  const { fiveMatchings, fiveMatchLater, otherMatchings, otherMatchLater } = await getMatchStatus()
  if (!fiveMatchings && !fiveMatchLater && !otherMatchings && !otherMatchLater) {
    // 如果没有任何赛事则直接跳转关注公众号
    uni.navigateTo({ url: '/pages/official/index' })
    return
  }
  type.value = fiveMatchings || fiveMatchLater ? 0 : 1
  if (type.value === 0) {
    // 5大联赛
    status.value = fiveMatchings ? 0 : 1
  } else {
    status.value = otherMatchings ? 0 : 1
  }

  const d = await getData(type.value, status.value)
  // 请求五大联赛，没有数据则隐藏tab
  if (isEmpty(d)) {
    matchId.value = null
    uni.hideLoading()
    return
  }
  const [{ id }] = d
  matchId.value = id
  await getMatchDetail(id)
  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
.search {
  :deep(.wd-input) {
    padding: 10rpx;
    background: rgba(0, 0, 0, 0.02);
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    border-radius: 12rpx;
  }
}

.sy-fab {
  position: fixed;
  top: 70% !important;
  right: 100rpx;
}
</style>
