<template>
  <view class="sy-select">
    <template v-if="modelValue">
      <view class="sy-select__content" @click="showPopover">
        <text class="flex-1 truncate">{{ display }}</text>
        <wd-icon name="arrow-down" size="28rpx" color="rgba(0,0,0,0.2)" />
      </view>
    </template>
    <template v-else>
      <text class="sy-select__content text-black text-opacity-50">{{ placeholder }}</text>
    </template>
    <view class="sy-select-popover" ref="popoverRef" v-show="isShow">
      <view class="absolute top-5px right-20rpx" @click="hidePopover">
        <wd-icon name="close" size="28rpx" />
      </view>
      <template v-for="e in options" :key="e[fieldValue]">
        <text v-if="modelValue === e[fieldValue]" class="sy-select-option truncate text-#D1302E">
          {{ e[fieldLabel] }}
        </text>
        <text v-else class="sy-select-option truncate" @click="handleChange(e)">
          {{ e[fieldLabel] }}
        </text>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'

const props = withDefaults(
  defineProps<{
    options?: any[]
    fieldLabel?: string
    fieldValue?: string
    placeholder?: string
    modelValue: any
  }>(),
  {
    options: () => [],
    fieldLabel: 'label',
    fieldValue: 'value',
    placeholder: '请选择',
  },
)

const emit = defineEmits<{
  (e: 'change', data: { value: any; option: any }): void
  (e: 'update:modelValue', value: any): void
}>()

const isShow = ref(false)

const popoverRef = ref()

const display = computed(() => {
  const fieldValue = props.fieldValue
  const fieldLabel = props.fieldLabel
  const modelValue = props.modelValue

  const selected = props.options.find((e) => e[fieldValue] === modelValue)
  return selected ? selected[fieldLabel] : modelValue
})

function showPopover() {
  isShow.value = true
}

function hidePopover() {
  isShow.value = false
}

onClickOutside(popoverRef, hidePopover)

function handleChange(e: any) {
  const value = e[props.fieldValue]
  emit('update:modelValue', value)
  emit('change', { value, option: e })
  isShow.value = false
}
</script>

<style lang="scss" scoped>
.sy-select {
  position: relative;
  background-color: var(--sy-select-bg-color, rgba(0, 0, 0, 0.02));

  &__content {
    display: flex;
    column-gap: 20rpx;
    align-items: center;
    justify-content: space-between;
    height: var(--sy-select-height, 70rpx);
    padding: 0 var(--sy-select-padding, 30rpx);
    font-size: 28rpx;
  }

  &-popover {
    position: absolute;
    bottom: 0;
    left: 10rpx;
    z-index: 10;
    display: flex;
    flex-direction: column;
    width: calc(100% - 60rpx);
    max-height: 600rpx;
    padding: 40rpx 20rpx 30rpx;
    overflow-y: auto;
    background-color: white;
    border-radius: 12rpx;
    box-shadow:
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05);
    transition-duration: 300ms;
    transform: translateY(calc(100% + var(--sy-select-popover-gap, 10rpx)));
  }

  &-option {
    flex: none;
    height: var(--sy-select-option-height, 50rpx);
  }
}
</style>
