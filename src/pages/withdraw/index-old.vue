<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '提现',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="container">
    <view class="top">
      <view class="flex items-center p-l-50rpx h-120rpx border-y-1 border-y-solid border-y-black">
        <image
          style="width: 80rpx; height: 80rpx; margin-right: 20rpx"
          :src="config.payType == 1 ? '/static/images/zfb.svg' : '/static/images/bank.svg'"
        />
        {{ config.payTypeName + '(' + config.payAccount + ')' }}
      </view>
      <view class="p-x-20rpx p-t-50rpx">
        提现金额
        <br />
        <input
          type="digit"
          v-model="formData.amount"
          placeholder="提现金额"
          maxlength="8"
          @blur="handleBlur"
          style="padding: 10rpx; margin-top: 10rpx; border: 1px solid #ccc; border-radius: 10rpx"
          disabled
        />
        <view
          class="flex justify-between tooltip p-y-10rpx border-b-1 border-b-solid border-b-black"
        >
          <view class="left">
            <!-- 保留两位小数，向下取整 -->
            可提现金额￥{{
              ((config.canWithdraw * (100 - config.commission)) / 100).toFixed(2)
            }}(最低可提现￥{{ config.min }})
          </view>
          <view class="right text-black" @click="withdrawAll">全部提现</view>
        </view>
        <!-- <view
          class="tooltip p-y-10rpx border-b-1 border-b-solid border-b-black"
          style="color: black"
        >
          作者佣金￥{{ getCommission }}({{ 100 - config.commission }}%)
        </view> -->
        <view class="tooltip">
          为优化提现流程、防范潜在风险，提现到账时间预计1~3个工作日，感谢您的使用
        </view>
      </view>
    </view>
    <view class="bottom">
      <wd-button custom-class="max-width-buttom" @click="handleSubmit">提现</wd-button>
    </view>
    <back />
  </view>
</template>
<script setup lang="ts">
import { getWithdrawConfig, AppWithdrawConfigRespVO, submitWithdraw } from '@/api/withdraw'
import back from '@/components/back/index.vue'

const config = ref<AppWithdrawConfigRespVO>({} as AppWithdrawConfigRespVO)

const formData = ref({
  amount: undefined,
})

const withdrawAll = () => {
  formData.value.amount = config.value.canWithdraw
}

const getCommission = computed(() => {
  let amount = formData.value.amount
  if (!amount) {
    amount = 0
  }
  return ((amount * config.value.commission) / 100).toFixed(2)
})

const handleSubmit = async () => {
  if (!formData.value.amount) {
    uni.showToast({
      title: '请输入提现金额',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (formData.value.amount <= 0) {
    uni.showToast({
      title: '提现金额必须为正数',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (formData.value.amount > config.value.canWithdraw) {
    uni.showToast({
      title: '提现金额不能大于可提现金额',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (formData.value.amount < config.value.min) {
    uni.showToast({
      title: `提现金额不能小于${config.value.min}元`,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (formData.value.amount > config.value.max) {
    uni.showToast({
      title: `提现金额不能大于${config.value.max}元`,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  console.log('提现' + formData.value.amount)

  await submitWithdraw(formData.value)

  uni.showToast({
    title: '提交成功',
    icon: 'none',
    duration: 2000,
  })
  // 跳转到提现记录页面

  getConfig()
}

const handleBlur = () => {
  const pattern = /^(0|([1-9][0-9]*))(\.[\d]{1,2})?$/
  if (formData.value.amount) {
    const nval = formData.value.amount.toString()
    if (!pattern.test(nval)) {
      formData.value.amount = undefined
    }
  }
}

const getConfig = async () => {
  const res = await getWithdrawConfig()
  config.value = res
}

// watch(
//   () => formData.value.amount,
//   (nval, oval) => {
//     const pattern = /^(0|([1-9][0-9]*))\.?([\d]{1,2})?$/
//     console.log('nval1', nval)
//     if (nval) {
//       if (!pattern.test(nval.toString())) {
//         console.log('nval3', nval)
//         if (oval == 0) {
//           formData.value.amount = 0
//         } else {
//           formData.value.amount = Number(oval)
//         }
//         console.log('value', formData.value.amount)
//       } else {
//         // 判断第一位是否为0第二位是否为.
//         if (
//           nval.toString().length >= 2 &&
//           nval.toString().charAt(0) == '0' &&
//           nval.toString().charAt(1) != '.'
//         ) {
//           formData.value.amount = 0
//         } else {
//           formData.value.amount = Number(nval)
//         }
//       }
//     }
//   },
//   { deep: true },
// )

onMounted(() => {
  getConfig()
})
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100vh;
}

.tooltip {
  font-size: 26rpx;
  color: #999;
}

:deep() {
  .max-width-buttom {
    width: 90vw;
    margin: 0 5vw;
  }
}
</style>
