<route lang="json5">
{
  style: {
    navigationBarTitleText: '提现信息',
    enablePullDownRefresh: false,
    navigationBarBackgroundColor: '#FFF',
  },
}
</route>

<template>
  <view class="container bg-gray-50 min-h-screen">
    <!-- 银行卡信息卡片 -->
    <view class="bank-card bg-white mx-40rpx mt-20rpx rounded-32rpx shadow-sm p-64rpx">
      <!-- 银行卡头部 -->
      <view class="flex items-center justify-between mb-60rpx">
        <view class="flex items-center">
          <image 
            class="w-120rpx h-120rpx mr-32rpx rounded-full"
            src="/static/icons/bank-card.png"
            mode="aspectFit"
          />
          <text class="text-56rpx font-semibold text-gray-800">招商银行（***************4666）</text>
        </view>
        <text class="text-48rpx text-gray-400">审核通过</text>
      </view>
      
      <!-- 分割线 -->
      <view class="border-b-1 border-gray-100 mb-64rpx"></view>
      
      <!-- 金额信息 -->
      <view class="flex justify-between mb-82rpx">
        <view class="flex flex-col items-center">
          <text class="text-52rpx text-gray-400 mb-24rpx">总收益(元)</text>
          <text class="text-60rpx font-semibold text-gray-800">10080.00</text>
        </view>
        <view class="flex flex-col items-center">
          <text class="text-52rpx text-gray-400 mb-24rpx">可提现(元)</text>
          <text class="text-60rpx font-semibold text-gray-800">10080.00</text>
        </view>
        <view class="flex flex-col items-center">
          <text class="text-52rpx text-gray-400 mb-24rpx">已提现(元)</text>
          <text class="text-60rpx font-semibold text-gray-800">10080.00</text>
        </view>
      </view>
      
      <!-- 提现按钮 -->
      <button class="withdraw-btn bg-red-500 text-white w-full py-36rpx rounded-80rpx text-64rpx font-semibold">
        立即提现
      </button>
    </view>
    
    <!-- 提现记录 -->
    <view class="record-section mt-78rpx">
      <!-- 标题和筛选 -->
      <view class="flex items-end justify-between mx-40rpx mb-32rpx">
        <text class="text-60rpx font-semibold text-gray-800">提现记录</text>
        <view class="flex items-center space-x-28rpx">
          <text class="text-52rpx text-gray-800 font-medium">全部</text>
          <text class="text-52rpx text-gray-400">本月</text>
          <text class="text-52rpx text-gray-400">本周</text>
        </view>
      </view>
      
      <!-- 选中指示器 -->
      <view class="indicator bg-red-500 w-100rpx h-12rpx rounded-6rpx ml-40rpx mb-38rpx"></view>
      
      <!-- 记录列表 -->
      <view class="record-list bg-white mx-40rpx rounded-32rpx shadow-sm p-46rpx">
        <!-- 提示信息 -->
        <text class="notice-text text-48rpx text-gray-400 mb-60rpx block">
          为保障资金安全，到账时间预计1~3个工作日，感谢使用！
        </text>
        
        <!-- 记录项 1 - 处理中 -->
        <view class="record-item flex items-center justify-between py-40rpx">
          <view class="flex items-center">
            <image 
              class="w-120rpx h-120rpx mr-32rpx rounded-full"
              src="/static/icons/bank-card.png"
              mode="aspectFit"
            />
            <view class="flex flex-col">
              <text class="text-52rpx text-gray-800 mb-8rpx">招商银行（***************4666）</text>
              <text class="text-48rpx text-gray-400">2025-06-28 15:30:10</text>
            </view>
          </view>
          <view class="flex flex-col items-end">
            <text class="text-60rpx font-semibold text-gray-800 mb-18rpx">12800.00</text>
            <view class="status-tag bg-red-50 text-red-500 px-26rpx py-10rpx rounded-10rpx">
              <text class="text-44rpx">处理中</text>
            </view>
          </view>
        </view>
        
        <!-- 分割线 -->
        <view class="border-b-1 border-gray-100"></view>
        
        <!-- 记录项 2 - 成功 -->
        <view class="record-item flex items-center justify-between py-40rpx">
          <view class="flex items-center">
            <image 
              class="w-120rpx h-120rpx mr-32rpx rounded-full"
              src="/static/icons/bank-card.png"
              mode="aspectFit"
            />
            <view class="flex flex-col">
              <text class="text-52rpx text-gray-800 mb-8rpx">招商银行（***************4666）</text>
              <text class="text-48rpx text-gray-400">2025-06-28 15:30:10</text>
            </view>
          </view>
          <view class="flex flex-col items-end">
            <text class="text-60rpx font-semibold text-gray-800 mb-18rpx">12800.00</text>
            <view class="status-tag bg-green-50 text-green-500 px-26rpx py-10rpx rounded-10rpx">
              <text class="text-44rpx">成功</text>
            </view>
          </view>
        </view>
        
        <!-- 分割线 -->
        <view class="border-b-1 border-gray-100"></view>
        
        <!-- 记录项 3 - 失败 -->
        <view class="record-item flex items-center justify-between py-40rpx">
          <view class="flex items-center">
            <image 
              class="w-120rpx h-120rpx mr-32rpx rounded-full"
              src="/static/icons/bank-card.png"
              mode="aspectFit"
            />
            <view class="flex flex-col">
              <text class="text-52rpx text-gray-800 mb-8rpx">招商银行（***************4666）</text>
              <text class="text-48rpx text-gray-400">2025-06-28 15:30:10</text>
            </view>
          </view>
          <view class="flex flex-col items-end">
            <text class="text-60rpx font-semibold text-gray-800 mb-18rpx">12800.00</text>
            <view class="status-tag bg-orange-50 text-orange-500 px-26rpx py-10rpx rounded-10rpx">
              <text class="text-44rpx">失败</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
// import { getWithdrawConfig, AppWithdrawConfigRespVO, submitWithdraw } from '@/api/withdraw'

// 响应式数据
const withdrawData = ref({
  totalIncome: '10080.00',
  availableAmount: '10080.00',
  withdrawnAmount: '10080.00',
  bankName: '招商银行',
  bankAccount: '***************4666',
  isVerified: true
})

const withdrawRecords = ref([
  {
    id: 1,
    bankName: '招商银行',
    bankAccount: '***************4666',
    amount: '12800.00',
    status: 'processing', // processing, success, failed
    createTime: '2025-06-28 15:30:10'
  },
  {
    id: 2,
    bankName: '招商银行',
    bankAccount: '***************4666',
    amount: '12800.00',
    status: 'success',
    createTime: '2025-06-28 15:30:10'
  },
  {
    id: 3,
    bankName: '招商银行',
    bankAccount: '***************4666',
    amount: '12800.00',
    status: 'failed',
    createTime: '2025-06-28 15:30:10'
  }
])

const activeTab = ref('all') // all, month, week

// 方法
const handleWithdraw = () => {
  console.log('发起提现')
  // TODO: 实现提现逻辑
}

const changeTab = (tab: string) => {
  activeTab.value = tab
  // TODO: 根据选中的tab筛选记录
}

const getStatusText = (status: string) => {
  const statusMap = {
    processing: '处理中',
    success: '成功',
    failed: '失败'
  }
  return statusMap[status] || status
}

const getStatusColor = (status: string) => {
  const colorMap = {
    processing: 'bg-red-50 text-red-500',
    success: 'bg-green-50 text-green-500',
    failed: 'bg-orange-50 text-orange-500'
  }
  return colorMap[status] || 'bg-gray-50 text-gray-500'
}
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40rpx;
}

.bank-card {
  box-shadow: 0 4rpx 8rpx 0 rgba(231, 231, 231, 0.5);
}

.withdraw-btn {
  border: none;
  outline: none;
  
  &:active {
    opacity: 0.8;
  }
}

.record-list {
  box-shadow: 0 4rpx 8rpx 0 rgba(231, 231, 231, 0.5);
}

.record-item:last-child {
  border-bottom: none;
}

.status-tag {
  font-size: 44rpx;
  border-radius: 10rpx;
}

.notice-text {
  line-height: 1.4;
}

// 自定义颜色类（如果unocss没有包含这些颜色）
.bg-red-50 {
  background-color: rgba(255, 232, 232, 1);
}

.text-red-500 {
  color: rgba(210, 48, 46, 1);
}

.bg-green-50 {
  background-color: rgba(232, 250, 237, 1);
}

.text-green-500 {
  color: rgba(0, 184, 0, 1);
}

.bg-orange-50 {
  background-color: rgba(255, 242, 226, 1);
}

.text-orange-500 {
  color: rgba(237, 159, 59, 1);
}

.text-gray-400 {
  color: rgba(153, 153, 153, 1);
}

.text-gray-800 {
  color: rgba(51, 51, 51, 1);
}
</style>
