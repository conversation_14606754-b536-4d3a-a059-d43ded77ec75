<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '提现记录',
  },
}
</route>
<template>
  <view class="h-100vh flex flex-col bg-[#f7d6c8]">
    <view class="up p-x-20rpx p-t-20rpx z-[10]">
      <view class="position-relative box-border h-300rpx color-white">
        <image class="position-absolute z-[-1]" src="/static/images/income_bg.png" style="width: 100%; height: 100%" />
        <view class="top flex justify-center h-50% border-b-1 border-b-solid border-b-white position-relative">
          <view class="position-absolute bottom-0 text-center text-22rpx">
            目前仅支持提取 7 天前累计的余额
          </view>
          <view class="left w-250rpx"></view>
          <view class="middle flex flex-col justify-center items-center">
            <view class="text-26rpx">可提现(元)</view>
            <view class="text-36rpx m-t-15rpx">{{ withdrawData.canWithdraw }}</view>
          </view>
          <view class="right flex justify-end w-250rpx">
            <view v-if="!isPartner"
              class="m-t-20rpx p-x-10rpx p-y-3rpx border-1 border-solid border-white text-26rpx h-40rpx rounded-10rpx"
              @click="goToWithdraw">
              提现
            </view>
          </view>
        </view>
        <view class="bottom flex justify-around text-26rpx h-50%">
          <view class="flex flex-col justify-center items-center h-100%">
            <view>总收益(元)</view>
            <view class="m-t-10rpx text-30rpx">{{ withdrawData.totalIncome }}</view>
          </view>
          <view class="flex flex-col justify-center items-center h-100%">
            <view>已打款(元)</view>
            <view class="m-t-10rpx text-30rpx">{{ withdrawData.withdraw }}</view>
          </view>
          <view class="flex flex-col justify-center items-center h-100%">
            <view>待审核(元)</view>
            <view class="m-t-10rpx text-30rpx">{{ withdrawData.waitingWithdraw }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="flex-1 flex flex-col down bg-white" style="border-radius: 20rpx 20rpx 0 0">
      <view class="p-x-30rpx p-y-30rpx flex justify-between items-center">
        <view class="font-bold text-28rpx">提现记录</view>
        <view class="flex">
          <view class="select-item" :select="params.type === 0" @click="changeSelectType(0)">
            全部
          </view>
          <view class="select-item" :select="params.type === 1" @click="changeSelectType(1)">
            本月
          </view>
          <view class="select-item" :select="params.type === 2" @click="changeSelectType(2)">
            本周
          </view>
        </view>
      </view>
      <view class="flex-1 m-t-20rpx border-t-1 border-t-solid border-t-[#cbcbcb]">
        <scroll-view :show-scrollbar="false" scroll-y>
          <view class="item" v-for="item in dataList" :key="item.id">
            <view class="middle">
              <view class="title">
                {{ statusName(item.status) }}
              </view>
              <view class="time">{{ formatDataTime(item.createTime) }}</view>
            </view>
            <view class="bottom">
              <view class="price" :style="statusStyle(item.status)">{{ item.amount }}</view>
            </view>
          </view>
        </scroll-view>
      </view>
      <wd-loadmore custom-class="loadmore" :state="loadmoreState" @reload="loadmore" v-if="dataList.length < total" />
    </view>
    <back />
  </view>
</template>
<script lang="ts" setup>
import { formatDate, formatDataTime } from '@/utils/format'
import { getWithdrawLog, canWithdraw, getWithdrawInfo } from '@/api/withdraw'
import back from '@/components/back/index.vue'
import { getPartnerInfo, getPartnerWithdrawLog } from '@/api/partner'
const params = ref({
  type: 0,
  pageNo: 1,
  pageSize: 10,
})

const withdrawData = ref({
  canWithdraw: 0,
  totalIncome: 0,
  withdraw: 0,
  waitingWithdraw: 0,
})

const goToWithdraw = async () => {
  const isCan = await canWithdraw()
  if (!isCan) {
    uni.showModal({
      title: '提现失败',
      content: '您还没有填写提现信息，请先填写提现信息',
      showCancel: false,
      confirmText: '确定',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: '/pages/settlementinfo/index',
          })
        }
      },
    })
  } else {
    uni.navigateTo({
      url: '/pages/withdraw/index',
    })
  }
}

const getWithdrawInfoData = async () => {
  const data = await getWithdrawInfo()
  withdrawData.value = data
}

const changeSelectType = (type) => {
  params.value.type = type
  params.value.pageNo = 1
  dataList.value = []
  getList()
}

const statusStyle = (status) => {
  switch (status) {
    case 0:
      return 'color: #e2b565;'
    case 1:
      return 'color: #67c23a;'
    case 2:
      return 'color: #f56c6c;'
  }
}

const loadmoreState = ref('finished')
const isPartner = ref(false)

const statusName = (status) => {
  switch (status) {
    case 0:
      return '待审核'
    case 1:
      return '提现成功'
    case 2:
      return '提现被拒'
    case 3:
      return '待打款'
  }
}

const dataList = ref([])
const total = ref(0)

const getList = async () => {
  if (isPartner.value) {
    const data = await getPartnerWithdrawLog(params.value)
    dataList.value = dataList.value.concat(data.list)
  } else {
    const data = await getWithdrawLog(params.value)
    dataList.value = dataList.value.concat(data.list)
    total.value = data.total
  }
}

const loadmore = () => {
  setTimeout(() => {
    params.value.pageNo = params.value.pageNo + 1
    loadmoreState.value = 'loading'
    getList()
  }, 200)
}

onReachBottom(() => {
  if (dataList.value.length < total.value) {
    loadmore()
  } else if (dataList.value.length >= total.value) {
    uni.showToast({
      icon: 'none',
      title: '没有更多数据了',
    })
  }
})

onPullDownRefresh(async () => {
  await getWithdrawInfoData()
  uni.showToast({ title: '刷新成功', icon: 'none' })
  uni.stopPullDownRefresh()
})

// onMounted(() => {
//   getList()
//   getWithdrawInfoData()
// })

onLoad(async ({ role }: { role: string }) => {
  if (role === 'partner') {
    isPartner.value = true

    const [withdrawInfo] = await Promise.all([getPartnerInfo(), getList()])
    const { totalAmount, canWithdrawAmount, withdrawedAmount, withdrawingAmount } = withdrawInfo

    withdrawData.value = {
      canWithdraw: canWithdrawAmount, // 可提现
      totalIncome: totalAmount, // 总收益
      withdraw: withdrawedAmount, // 已打款
      waitingWithdraw: withdrawingAmount, // 待审核
    }
  } else {
    Promise.all([getList(), getWithdrawInfoData()])
  }
})
</script>
<style lang="scss" scoped>
.item {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 150rpx;
  margin: 0 30rpx;
  border-bottom: 1px solid #cbcbcb;

  .middle {
    .title {
      font-size: 28rpx;
    }

    .time {
      padding-top: 10rpx;
      font-size: 26rpx;
      color: #999;
    }
  }
}

.select-item {
  padding: 3rpx 10rpx 3rpx 10rpx;
  font-size: 26rpx;
  color: #333;
  border-radius: 10rpx;

  &[select='true'] {
    color: white;
    background-color: #d1302e;
  }
}
</style>
