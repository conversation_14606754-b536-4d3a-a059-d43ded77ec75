<route lang="json5">
{
  style: {
    navigationBarTitleText: '提现',
    enablePullDownRefresh: false,
  },
}
</route>

<template>
  <view class="flex flex-col h-full">
    <wd-gap bg-color="#F4F8FA" height="20rpx" />
    <wd-picker v-model="settileId" :columns="payOptions" use-default-slot>
      <template #default>
        <view
          class="flex justify-between items-center h-100rpx px-30rpx border-b-solid border-b-1rpx border-b-black border-b-opacity-10"
        >
          <template v-if="selectedPayItem">
            <image :src="selectedPayItem.icon" class="w-64rpx h-64rpx" />
            <text class="ml-10rpx mr-auto text-30rpx text-black text-opacity-90">
              {{ selectedPayItem.label }}
            </text>
            <wd-icon name="arrow-right" class="w-40rpx h-40rpx" color="#999999" />
          </template>
        </view>
      </template>
    </wd-picker>
    <view
      class="flex flex-col gap-y-30rpx mx-30rpx pt-30rpx pb-20rpx border-b-solid border-b-1rpx border-b-black border-b-opacity-10"
    >
      <text class="text-30rpx text-black text-opacity-90">提现金额</text>
      <wd-input-number
        :precision="2"
        :min="payInfo.min"
        input-width="100%"
        custom-class="withdraw-input"
        v-model="amount"
      />
    </view>
    <view class="flex justify-between mt-20rpx mb-30rpx px-30rpx">
      <view class="flex flex-col gap-y-10rpx text-28rpx">
        <text class="text-black text-opacity-50">{{ promotionTxt }}</text>
        <text class="text-black text-opacity-90">{{ ratioTxt }}</text>
      </view>
      <text class="mt-10rpx text-#D1302E underline underline-offset-2" @click="withdrawAll">
        全部提现
      </text>
    </view>
    <wd-button block class="withdraw-btn" @click="submit">提现</wd-button>
    <view
      class="flex-1 flex flex-col gap-y-10rpx mt-30rpx p-30rpx bg-#F4F8FA text-28rpx text-black text-opacity-50"
    >
      <text>温馨提示：</text>
      <text>预计到账时间1-3个工作日</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getPartnerWithdrawConfig, partnerWithdraw } from '@/api/partner'
import { Decimal } from 'decimal.js'

// 1 支付宝 2 银行
const settileId = ref(0)

const payInfo = ref({ min: 0, canWithdraw: 0 })

const payOptions = ref<{ value: number; label: string; icon: string; type: number }[]>([])

const selectedPayItem = computed(
  () => payOptions.value.find(({ value }) => value === settileId.value) ?? null,
)

const promotionTxt = computed(
  () => `可提现金额￥${payInfo.value.canWithdraw}（最低可提现￥${payInfo.value.min}）`,
)

/* 费率 */
const ratio = ref(0)

const amount = ref(0)

const ratioTxt = computed(
  () =>
    `手续费￥${amount.value ? Decimal(amount.value).mul(ratio.value).div(100) : 0}（费率${ratio.value}%）`,
)

function withdrawAll() {
  amount.value = payInfo.value.canWithdraw
}

async function init() {
  const config = await getPartnerWithdrawConfig()
  const { canWithdraw, min, commission, payInfos } = config
  payInfo.value = { min, canWithdraw }
  ratio.value = commission

  let po = []
  payInfos.forEach(({ payType, payTypeName, payAccount, settileId: sId }) => {
    if (payType === 2) {
      settileId.value = sId
      po = [
        ...po,
        {
          type: payType,
          value: sId,
          label: `${payTypeName}（${payAccount}）`,
          icon: '/static/images/bank.svg',
        },
      ]
    } else {
      po = [
        ...po,
        {
          type: payType,
          value: sId,
          label: `${payTypeName}（${payAccount}）`,
          icon: '/static/images/zfb.svg',
        },
      ]
    }
  })
  payOptions.value = po
}

async function submit() {
  uni.showLoading()
  await partnerWithdraw(amount.value, settileId.value)
  await init()
  uni.hideLoading()
}

onLoad(() => {
  uni.showLoading()
  init()
  uni.hideLoading()
})
</script>

<style lang="scss" scoped>
.withdraw-input {
  :deep(.wd-input-number__action) {
    display: none;
  }

  &::before {
    font-size: 36rpx;
    color: rgba($color: #000000, $alpha: 0.5);
    content: '￥';
  }

  :deep(.wd-input-number__input) {
    padding: 0 20rpx;
  }

  :deep(.uni-input-input) {
    font-size: 30rpx;
    text-align: left;
  }

  // :deep(.wd-input-number__inner) {
  //   position: unset;
  // }
}

.withdraw-btn {
  width: calc(100% - 60rpx);
  height: 100rpx !important;
  margin: 0 auto;
  border-radius: 12rpx !important;
}
</style>
