<template>
  <view class="flex flex-col gap-y-30rpx">
    <view class="flex flex-col gap-y-20rpx" v-for="e in list">
      <view class="flex justify-between items-center">
        <text class="text-28rpx text-black text-opacity-90">{{ zhanjiTxt(e) }}</text>
        <view class="flex items-center gap-x-12rpx text-#D1302E" @click="show(e)">
          <wd-icon name="download1" size="30rpx" />
          <text class="text-26rpx">生成战绩</text>
        </view>
      </view>
      <!-- 进度条 -->
      <template v-if="e.hitRate === 0">
        <view class="h-8rpx bg-#FF5050 bg-opacity-30" />
      </template>
      <template v-else-if="e.hitRate === 100">
        <view class="h-8rpx bg-#FF5050" />
      </template>
      <template v-else>
        <view class="flex h-8rpx">
          <view class="h-full bg-#FF5050 size-34%" :class="`size-${e.hitRate}%`" />
          <view class="h-full bg-#FF5050 bg-opacity-30" :class="`size-${100 - e.hitRate}%`" />
        </view>
      </template>
    </view>
    <!-- 弹窗(ActionSheet) -->
    <wd-action-sheet v-model="showAction">
      <view class="pb-30rpx">
        <view class="relative center h-100rpx text-30rpx text-black text-opacity-90">
          选择标签
          <view class="absolute right-30rpx w-30rpx h-30rpx" @click="closeAction">
            <wd-icon name="close" size="30rpx" color="rgba(0,0,0,0.5)" />
          </view>
        </view>
        <view class="px-30rpx">
          <wd-radio-group v-model="type" shape="dot">
            <wd-radio value="0">不选择</wd-radio>
            <wd-radio value="红单王">{{ `${record.keyword}红单王` }}</wd-radio>
            <wd-radio value="收割机">{{ `${record.keyword}收割机` }}</wd-radio>
            <wd-radio value="狙击手">{{ `${record.keyword}狙击手` }}</wd-radio>
            <wd-radio value="之王">{{ `${record.keyword}之王` }}</wd-radio>
          </wd-radio-group>
          <view class="flex gap-x-20rpx mt-40rpx">
            <wd-button
              block
              size="large"
              custom-class="cancel-btn"
              type="info"
              round
              @click="handleCancel"
            >
              取消
            </wd-button>
            <wd-button block size="large" custom-class="confirm-btn" round @click="generate">
              确认
            </wd-button>
          </view>
        </view>
      </view>
    </wd-action-sheet>
  </view>
</template>
<script setup lang="ts">
import { IZhanjiItem } from '@/api/zhanji'
import { generateZhanji } from '@/api/zhanji'

defineProps<{ list: IZhanjiItem[] }>()

const type = ref<string>('')

const record = ref<IZhanjiItem | null>()

const zhanjiTxt = computed(() => {
  return ({ keyword, hitRate }: IZhanjiItem) => {
    return `${keyword}准确率${hitRate}%`
  }
})

const showAction = ref(false)

function show(e: IZhanjiItem) {
  showAction.value = true
  record.value = e
}

function closeAction() {
  showAction.value = false
}

function handleCancel() {
  closeAction()
}

async function generate() {
  try {
    const t = type.value
    if (!t) {
      uni.showToast({ title: '请选择类型' })
      return
    }
    uni.showLoading()
    const id = await generateZhanji({ ...record.value, alias: type.value })
    uni.navigateTo({ url: `/pages/zhanji/index?id=${id}` })
  } finally {
    uni.hideLoading()
  }
}
</script>

<style lang="scss" scoped>
.cancel-btn,
.confirm-btn {
  flex: 1;
}
</style>
