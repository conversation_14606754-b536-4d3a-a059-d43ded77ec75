<route lang="json5">
{
  style: {
    navigationBarTitleText: '战绩生成',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="pt-20rpx">
    <view class="px-30rpx">
      <wd-datetime-picker
        v-model="date"
        type="date"
        custom-class="sy-date-picker"
        :max-date="Date.now()"
        :min-date="minDate"
        @confirm="handleDateConfirm"
      />
      <wd-segmented
        :options="timeOptions"
        v-model:value="timeValue"
        :custom-class="segmentClazz"
        @change="handleSegmentTabChange"
      >
        <template #label="{ option }">
          <view>{{ option.label }}</view>
        </template>
      </wd-segmented>
      <view class="flex items-center gap-x-10rpx py-30rpx">
        <image :src="accuracyIcon" mode="scaleToFill" class="w-40rpx h-40rpx" />
        <text class="text-32rpx text-black text-opacity-90">准确率排行前十</text>
      </view>
    </view>
    <!-- 前十排行 -->
    <view class="pb-40rpx px-30rpx border-bottom">
      <zhanji-list :list="topTen" />
    </view>
    <!-- 条件筛选 -->
    <view class="flex flex-wrap gap-y-20rpx gap-x-10rpx py-30rpx px-30rpx">
      <wd-select-picker
        custom-class="sy-picker"
        placeholder="请选择国家"
        clearable
        :columns="countryOptions"
        value-key="id"
        label-key="name"
        filterable
        v-model="formData.countryId"
        type="radio"
      />
      <sy-select
        v-model="formData.competitionId"
        clearable
        placeholder="请选择联赛"
        :options="competitionOptions"
        label-key="shortName"
        value-key="id"
      />
      <wd-picker
        placeholder="请选择方案类型"
        custom-class="sy-picker"
        clearable
        :columns="schemeOptions"
        v-model="formData.schemePlay"
        value-key="id"
        label-key="name"
      />
      <wd-picker
        placeholder="请选择玩法类型"
        custom-class="sy-picker"
        clearable
        :columns="playOptions"
        v-model="formData.playId"
        label-key="name"
        value-key="id"
      />
      <wd-picker
        placeholder="请选择时间类型"
        custom-class="sy-picker"
        :columns="timeTypeOptions"
        clearable
        v-model="formData.timeType"
      />
      <wd-picker
        placeholder="请选择冷门高倍类型"
        custom-class="sy-picker"
        :columns="oddsTypeOptions"
        clearable
        v-model="formData.oddsType"
      />
    </view>
    <!-- 定制战绩 -->
    <view class="pt-30rpx px-30rpx pb-80rpx" v-if="!isEmpty(customRanking)">
      <zhanji-list :list="customRanking" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/format'
import accuracyIcon from '@/static/images/accuracy.svg'
import {
  IZhanjiItem,
  IZhanjiRankingParams,
  getCompetitionList,
  getCountryList,
  getPlayMethods,
  getSchemePlayList,
  getZhanjiRanking,
} from '@/api/zhanji'
import zhanjiList from './components/zhanjiList.vue'
import { isEmpty, isNil, pickBy } from 'lodash-es'
import sySelect from '@/components/sySelect/index.vue'
import { IGamePlayItem, IScheme } from '@/api/author'
import dayjs from 'dayjs'

const date = ref<number[]>([])
// const todayStr = ref(formatDate(Date.now()))

const isSegmentedDisabled = ref(false)

const segmentClazz = computed(() =>
  isSegmentedDisabled.value ? 'segmented-disabled' : 'sy-segmented',
)

const timeOptions = ref([
  { label: '今日', value: 0 },
  { label: '本周', value: 1 },
  { label: '本月', value: 2 },
])

const timeValue = ref(0)

const topTenList = ref<{
  today: IZhanjiItem[]
  currentWeek: IZhanjiItem[]
  currentMonth: IZhanjiItem[]
}>({
  today: [],
  currentWeek: [],
  currentMonth: [],
})

const topTen = computed(() => {
  if (timeValue.value === 0) return topTenList.value.today
  return timeValue.value === 1 ? topTenList.value.currentWeek : topTenList.value.currentMonth
})

const customRanking = ref<IZhanjiItem[]>([])

const countryOptions = ref<{ id: number; name: string }[]>([])
const competitionOptions = ref<{ id: number; name: string }[]>([])
const schemeOptions = ref<IScheme[]>([])
const playOptions = ref<Omit<IGamePlayItem, 'value'>[]>([])
const timeTypeOptions = ref([
  { value: 0, label: '早场' },
  { value: 1, label: '晚场' },
])
const oddsTypeOptions = ref([
  { value: 0, label: '3X' },
  { value: 1, label: '5X' },
  { value: 2, label: '10X' },
])

const formData = ref<{
  countryId: string | number
  competitionId: string | number
  schemePlay: string | number
  playId: string | number
  timeType: string | number
  oddsType: string | number
}>({
  countryId: '',
  competitionId: '',
  schemePlay: '',
  playId: '',
  timeType: '',
  oddsType: '',
})

const minDate = computed(() => dayjs().startOf('year').valueOf())
async function getZhanjiRankingData(dateType = 0, date?: { startDate: string; endDate: string }) {
  let rankingList: IZhanjiItem[] = []
  if (!date) {
    rankingList = await getZhanjiRanking(dateType)
  } else {
    rankingList = await getZhanjiRanking(dateType, date)
  }
  if (dateType === 0) topTenList.value.today = rankingList
  else if (dateType === 1) topTenList.value.currentWeek = rankingList
  else if (dateType === 2) topTenList.value.currentMonth = rankingList
}

async function getCustomZhanjiData(params: Partial<IZhanjiRankingParams>, dateType = 0) {
  customRanking.value = await getZhanjiRanking(dateType, params)
}

async function handleDateConfirm({ value }: { value: [number, number] }) {
  try {
    uni.showLoading()
    const [s, e] = value
    const startDate = formatDate(s)
    const endDate = formatDate(e)
    isSegmentedDisabled.value = true
    // 第一个参数传任何值都不影响后面的查询结果
    await getZhanjiRankingData(timeValue.value, { startDate, endDate })
    // if (dateStr === todayStr.value) {
    //   // 获取当天战绩
    //   timeValue.value = 0
    //   isSegmentedDisabled.value = false
    //   await getZhanjiRankingData(0)
    // } else {
    //   isSegmentedDisabled.value = true
    //   const d = formatDate(value)
    //   await getZhanjiRankingData(timeValue.value, { startDate: d, endDate: d })
    // }
  } finally {
    uni.hideLoading()
  }
}

async function handleSegmentTabChange({ value }: { value: number }) {
  isSegmentedDisabled.value = false
  date.value = []
  // if (value === 0) {
  //   date.value = Date.now()
  // } else {
  //   date.value = 0
  // }

  try {
    uni.showLoading()
    if (isEmpty(topTen.value)) {
      await getZhanjiRankingData(value)
    } else if (timeValue.value === 0) {
      await getZhanjiRankingData(value)
    }
  } finally {
    uni.hideLoading()
  }
}

const unwatch = watch(
  () => formData.value.schemePlay,
  async (v, o) => {
    if (v !== o) {
      if (v) {
        const { play } = schemeOptions.value.find(({ id }) => v === id)
        playOptions.value = await getPlayMethods(play)
      } else {
        playOptions.value = []
      }
    }
  },
)

const unwatchFormData = watch(
  [formData, date],
  async ([v]) => {
    let d = pickBy(v, (e) => !isNil(e) && e !== '')
    if (isEmpty(d)) {
      customRanking.value = []
    } else {
      try {
        uni.showLoading()
        // date数组为空说明选择的是日期类型，否则是选择的时间段
        const t = isEmpty(date.value) ? timeValue.value : null
        if (t) {
          await getCustomZhanjiData(d, t)
        } else {
          const [s, e] = date.value
          const startDate = formatDate(s)
          const endDate = formatDate(e)
          await getCustomZhanjiData({ ...d, startDate, endDate })
        }
      } finally {
        uni.hideLoading()
      }
    }
  },
  { deep: true },
)

onLoad(async () => {
  getZhanjiRankingData(timeValue.value)
  const [c, cp, s] = await Promise.all([
    getCountryList(),
    getCompetitionList(),
    getSchemePlayList(),
  ])
  countryOptions.value = c
  competitionOptions.value = cp
  schemeOptions.value = s
})

onUnload(() => {
  unwatch()
  unwatchFormData()
})
</script>

<style lang="scss" scoped>
.sy-date-picker {
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(121, 121, 121, 0.1);
  border-radius: 12rpx;
}

.segmented-disabled {
  --wot-segmented-item-acitve-bg: #eeeeee;

  :deep(.wd-segmented__item.is-active) {
    font-weight: 400 !important;
  }
}

.sy-picker {
  width: calc(50% - 10rpx);
  border: 1px solid rgba(121, 121, 121, 0.1);
  border-radius: 14rpx;
}
</style>
