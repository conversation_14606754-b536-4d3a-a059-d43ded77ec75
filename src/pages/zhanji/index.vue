<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的战绩',
    enablePullDownRefresh: false,
  },
}
</route>
<template>
  <view class="zhanji" id="zhanji">
    <template v-if="record">
      <!-- 头 -->
      <view class="zhanji-header">{{ title }}</view>
      <!-- 排行榜 -->
      <view class="zhanji-ranking">
        <view
          class="flex flex-col items-center mb-20rpx text-30rpx leading-40rpx pb-30rpx border-b-2rpx border-b-solid border-b-#E6AE78 border-b-2rpx border-b-solid border-b-#E6AE78"
        >
          <text>{{ time }}</text>
          <text>{{ caption }}</text>
          <text class="mt-5rpx text-#DD4040">{{ `红单率${record.hitRate}%` }}</text>
        </view>
        <view
          class="flex items-center h-70rpx rounded-12rpx border-1rpx border-solid border-#623F08 text-26rpx text-#623F08"
        >
          <text class="center w-60rpx">序号</text>
          <text class="center w-160rpx">日期</text>
          <text class="flex-1 text-center">赛事分析</text>
          <text class="center w-60rpx">SP值</text>
          <text class="center w-120rpx">百元可中</text>
          <text class="center w-80rpx">红黑</text>
        </view>
        <view
          v-if="!isEmpty(record.dataList)"
          class="flex flex-col gap-y-20rpx mt-20rpx text-white text-22rpx"
        >
          <template
            v-for="(
              { articleId, isWin, createTime, title, sp, income, winName }, index
            ) in record.dataList"
            :key="articleId"
          >
            <!-- 命中 -->
            <view v-if="isWin" class="flex items-center h-60rpx rounded-12rpx bg-#DD4040">
              <text class="center w-60rpx">{{ index }}</text>
              <text class="center w-160rpx">{{ datetime(createTime) }}</text>
              <text class="flex-1 px-10rpx truncate text-center">{{ title }}</text>
              <text class="center w-60rpx">{{ sp }}</text>
              <text class="center w-120rpx">{{ income }}</text>
              <text class="center w-80rpx">{{ winName }}</text>
            </view>
            <!-- 未命中 -->
            <view v-else class="flex items-center h-60rpx rounded-12rpx bg-#593434 bg-opacity-50">
              <text class="center w-60rpx">{{ index }}</text>
              <text class="center w-160rpx">{{ datetime(createTime) }}</text>
              <text class="flex-1 px-10rpx truncate text-center">{{ title }}</text>
              <text class="center w-60rpx">{{ sp }}</text>
              <text class="center w-120rpx">{{ income }}</text>
              <text class="center w-80rpx">{{ winName }}</text>
            </view>
          </template>
        </view>
      </view>
    </template>
    <wd-fab :expandable="false" :draggable="true" position="right-bottom" class="mt-[-150rpx]">
      <template #trigger>
        <view class="share" @click="handleShare" />
      </template>
    </wd-fab>
  </view>
</template>
<script setup lang="ts">
import { IZhanjiItem, getZhanjiDetail, uploadZhanjiImg } from '@/api/zhanji'
import { DATE_TYPE, WEEK_CN } from '@/utils/constant'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash-es'
import html2canvas from 'html2canvas'

const record = ref<IZhanjiItem>()
const base64Data = ref('')
const id = ref('')

const title = computed(() => {
  if (!record.value) return ''
  const { authorName, keyword, alias } = record.value
  const caption = alias === '0' ? '' : alias
  return `${authorName} ${keyword}${caption}`
})

const time = computed(() => {
  if (!record.value) return ''
  const {
    accomplishmentReqVO: { startDate, endDate, dateType },
  } = record.value
  const dateTypeStr = dateType > 2 || dateType < 0 ? '' : DATE_TYPE[dateType]
  return startDate ? `${startDate} ~ ${endDate}` : dateTypeStr
})

const caption = computed(() => {
  if (!record.value) return ''
  const { dataList, keyword } = record.value
  return `共推${dataList.length}场${keyword}`
})

const datetime = computed(() => {
  return (t: number) => {
    const dt = dayjs(t)
    const time = dt.format('HH.mm')
    const week = WEEK_CN[dt.day()]
    return `${time} ${week}`
  }
})

async function createZhanjiImg() {
  return new Promise((resolve) => {
    setTimeout(async () => {
      const zhanjiDom = document.getElementById('zhanji')
      const canvas = await html2canvas(zhanjiDom, {
        scale: window.devicePixelRatio || 2,
        useCORS: true, // 处理跨域问题
        allowTaint: true,
        logging: false,
        backgroundColor: null,
      })
      base64Data.value = canvas.toDataURL('image/png')
      resolve(true)
    }, 600)
  })
}

function handleShare() {
  uni.setClipboardData({
    data: window.location.href,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    },
  })
}

onMounted(async () => {
  try {
    uni.showLoading({ title: '战绩生成中' })
    record.value = await getZhanjiDetail(id.value)
    const { showPic } = record.value
    if (!showPic) {
      await createZhanjiImg()
      await uploadZhanjiImg({ id: id.value, picUrl: base64Data.value })
      await getZhanjiDetail(id.value)
    }
  } finally {
    uni.hideLoading()
  }
})

onLoad(async ({ id: i }) => {
  if (!i) return
  id.value = i
})
</script>

<style lang="scss" scoped>
.zhanji {
  box-sizing: border-box;
  height: 100%;
  padding-top: 60rpx;
  background-image: url('https://sacdn.850g.com/football/static/zhanji_bg.jpg');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &-header {
    display: flex;
    column-gap: 10rpx;
    align-items: center;
    justify-content: center;
    height: 68rpx;
    font-size: 34rpx;
    color: #fff;

    &::before {
      display: block;
      width: 32rpx;
      height: 100%;
      content: '';
      background-image: url('/static/images/wheat-l.svg');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    &::after {
      display: block;
      width: 32rpx;
      height: 100%;
      content: '';
      background-image: url('/static/images/wheat-r.svg');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  &-ranking {
    box-sizing: border-box;
    width: 724rpx;
    height: 1216rpx;
    padding: 70rpx 45rpx 0;
    margin: 60rpx auto 0;
    background-image: url('https://sacdn.850g.com/football/static/zhanji-ranking_bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
</style>

<style lang="scss" scoped>
.share {
  width: 100rpx;
  height: 100rpx;
  background-image: url('https://sacdn.850g.com/football/static/share.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
</style>
