import { http } from '@/utils/http'
/*
 *修改昵称
 */
export const updateNickName = (data) => {
  return http.post<any>('/app-api/member/user/updateNickName', data)
}

/*
 *修改头像
 */
export const updateAvatar = (data) => {
  return http.post<any>('/app-api/member/user/updateAvatar', data)
}

/*
 * 获取银行列表
 */
export const getBankListData = () => {
  return http.get<any>('/app-api/member/settlement-info/bank-list')
}

/*
 * 获取结算账户信息
 */
export const getSettlementInfoData = () => {
  return http.get<any>('/app-api/member/settlement-info/get')
}

/*
 * 保存结算账户信息
 */
export const saveSettlementInfoData = (data) => {
  return http.post<any>('/app-api/member/settlement-info/save', data)
}

/*
 * 获取鱼币明细数据
 */
export const getGoldLogsData = (params: any) => {
  return http.get<any>('/app-api/member/gold-log/page', params)
}

export const getConsumerInfo = () => {
  return http.get<any>('/app-api/member/gold-log/getMemberConsumerInfo')
}

/*
 * 获取收益明细数据
 */
export const getBalanceLogsData = (params: any) => {
  return http.get<any>('/app-api/author/getIncomeList', params)
}

/*
 * 获取订单收益明细数据
 */
export const getIncomeOrderLogsData = (params: any) => {
  return http.get<any>('/app-api/author/getIncomeOrderList', params)
}

export const getIncomeInfo = () => {
  return http.get<any>('/app-api/author/getIncomeCount')
}

/**
 * 获取用户信息
 */
export const getUserInfo: () => Promise<IUserInfo> = () => {
  return http.get<any>('/app-api/member/user/get')
}

export function updateAttention(isAttention: number, consume: number) {
  return http.post('/app-api/member/user/update-attention', { isAttention, consume })
}

export function updateAccomplishment(accomplishment: number) {
  return http.post('/app-api/member/user/update-accomplishment', { accomplishment })
}

/**
 * 获取关注列表
 */
export const getAttentionList = (authorName: string) => {
  return http.get<any>('/app-api/member/attention/getList', { authorName })
}
/**
 * 取消关注
 */
export const cancelAttention = (authorId: number) => {
  return http.get<any>('/app-api/member/attention/cancel', { authorId })
}
/**
 * 关注
 */
export const addAttention = (authorId: number) => {
  return http.get<any>('/app-api/member/attention/add', { authorId })
}
/**
 * 获取粉丝列表
 */
export const getFansList = (params: any) => {
  return http.get<any>('/app-api/member/attention/getFansList', params)
}

export const getFansCountInfo = () => {
  return http.get<any>('/app-api/member/attention/getFansCountInfo')
}

/*
 * 获取足迹列表
 */
export const getFootprintList = (params: any) => {
  return http.get<any>('/app-api/member/footprint', params)
}

/*
 * 获取我的方案列表
 */
export const getMyArticlyList = (params: any) => {
  return http.get<any>('/app-api/author/article-page', params)
}

export const getArticleList = (params: any) => {
  return http.get<any>('/app-api/author/article-list', params)
}

export const getTotalList = (params: any) => {
  return http.get<any>('/app-api/author/total-list', params)
}

/*
 * 设置方案红黑
 */
export const updateWin = (data) => {
  return http.post<any>('/app-api/author/updateWin', data)
}

// 执行收款或退款
export const winExecute = (data) => {
  return http.post<any>('/app-api/author/winExecute', data)
}

/*
 * 获取已购方案列表
 */
export const getMyBuyArticlyList = (params: any) => {
  return http.get<any>('/app-api/scheme-order/getSchemeOrderList', params)
}

export const getUserByArticlyList = (params: any) => {
  return http.get<any>('/app-api/scheme-order/getSchemeOrders', params)
}

/*
 * 获取已购方案作者列表
 */
export const getMyBuyArticlyAuthorList = () => {
  return http.get<any>('/app-api/scheme-order/getSchemeOrderAuthorList')
}

/*
 * 获取销售报表数据
 */
export const getSaleReportData = (params: any) => {
  return http.get<any>('/app-api/report/sale-data', params)
}

/*
 * 销售数据特权单独接口
 */

export const getSalePrivilegeListData = (params: any) => {
  return http.get<any>('/app-api/author/getPrivilegeDetail', params)
}
/**
 * 请求支付
 */
export const requestPayment = (params: any) => {
  return http.post<any>('/app-api/gold-order/submit', params)
}

/**
 * 请求小程序支付
 */
export const requestMiniProgramPayment = (params: any) => {
  return http.post<any>('/app-api/gold-order/mini-program/submit', params)
}

/**
 *
 * @param params 获取鱼币订单列表
 * @returns
 */
export const getMyOrderList = (params: any) => {
  return http.get<any>('/app-api/gold-order/getUserPage', params)
}

/**
 *
 * @param params 获取战绩列表
 * @returns
 */
export const getMyScoreList = (params: any) => {
  return http.get<any>('/app-api/author/getMyScore', params)
}

/**
 *
 * @param params 获取主页二维码
 * @returns
 */
export const getAuthorQrcode = () => {
  return http.get<any>('/app-api/author/getAuthorQrcode')
}

/**
 *
 * @param params 获取APPID
 * @returns
 */
export const getAppid = () => {
  return http.get<any>('/app-api/wechat/account/getAppid')
}

/**
 *
 * @param params 获取公众二维码
 * @returns
 */
export const getAppQrcode = (
  type: number,
  authorId?: number,
  articleId?: number,
  shareType: number = 0,
) => {
  return http.get<any>('/app-api/wechat/account/getAppQrcode', {
    type,
    authorId,
    articleId,
    shareType,
  })
}

/**
 *
 * @param params 获取包时特权设置
 * @returns
 */
export const getPrivilegeSet = (type) => {
  return http.get<any>('/app-api/author/getPrivilegeSet?type=' + type)
}

/**
 *
 * @param params 保存包时特权设置
 * @returns
 */
export const savePrivilegeSet = (data) => {
  return http.post<any>('/app-api/author/savePrivilegeSet', data)
}
export const delPrivilegeSet = (id) => {
  return http.get<any>('/app-api/author/delPrivilegeSet?id=' + id)
}

export const getWxPayDetail = (orderNo) => {
  return http.get<any>('/app-api/wxPay/detail', { orderNo })
}

export const requestGiftRetio = () => {
  return http.get<any>('/app-api/gold-order/getGiftRatio')
}

export const getArticleUserInfos = (data) => {
  return http.post<any>('/app-api/author/getArticleUserInfos', data)
}

export const getBuyArticleDetail = (articleId) => {
  return http.get<any>('/app-api/author/getBuyArticleDetail', { articleId })
}

export const pushPrivilegeNum = (userId, num) => {
  return http.get<any>('/app-api/author/pushPrivilegeNum', { userId, num })
}

/*
 * 销售数据特权单独接口
 */

/* 拉黑(取消拉黑)粉丝 */
export function toggleBlackList(userId: number, status: number) {
  return http.get('/app-api/member/attention/addBlackList', { userId, status })
}
export const getMatchTypes = () => {
  return http.get<any>('/app-api/author/getPrivilegeMatchs')
}

export const activatePrivilege = (id) => {
  return http.get<any>('/app-api/author/activatePrivilege', { id })
}

export const getMyBuyPrivilegeAuthorList = () => {
  return http.get<any>('/app-api/author/getPrivilegeAuthorList')
}

export const getMemberPrivilegeLogs = (userId, privilegeId) => {
  return http.get<any>('/app-api/author/getMemberPrivilegeLogs', { userId, privilegeId })
}

export const getNewPayUserData = (params: any) => {
  return http.get<any>('/app-api/report/new-pay-user-detail', params)
}
