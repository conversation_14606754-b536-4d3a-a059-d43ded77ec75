import { IMatchScheme } from '@/api/match'
import { defineStore } from 'pinia'

export const useMatchSchemeStore = defineStore(
  'matchScheme',
  () => {
    const scheme = ref<{
      main: IMatchScheme[]
      bonus: IMatchScheme[]
      schemePlay: null | number
      instalments: string,
      article: {}
    }>({
      main: [],
      bonus: [],
      schemePlay: null,
      instalments: '',
      article:{}
    })

    function setMainScheme(m: IMatchScheme[]) {
      scheme.value.main = m
    }

    function setBonusScheme(b: IMatchScheme[]) {
      scheme.value.bonus = b
    }

    function clearScheme() {
      scheme.value.main = []
      scheme.value.bonus = []
      scheme.value.schemePlay = null
      scheme.value.instalments = ''
      scheme.value.article = {}
    }

    function changeSchemePlay(v: number | null) {
      scheme.value.schemePlay = v
    }

    return {
      scheme,
      setMainScheme,
      setBonusScheme,
      clearScheme,
      changeSchemePlay,
    }
  },
  {
    persist: true,
  },
)
