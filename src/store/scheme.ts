import { IMatchSchemItem } from '@/api/article'
import { defineStore } from 'pinia'

export const useSchemeStore = defineStore(
  'scheme',
  () => {
    const scheme = ref<(IMatchSchemItem & { dataType: number })[]>([])

    const setScheme = (ss: (IMatchSchemItem & { dataType: number })[]) => {
      scheme.value = ss
    }

    const clearScheme = () => {
      scheme.value = []
    }

    return {
      scheme,
      setScheme,
      clearScheme,
    }
  },
  {
    persist: true,
  },
)
