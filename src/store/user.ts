import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getUserInfo } from '@/service/userService'

const initState: any = { nickname: '', avatar: '' }

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...initState })

    const setUserInfo = (val: IUserInfo) => {
      if (val != null) {
        const token = userInfo.value.token
        const refreshToken = userInfo.value.refreshToken
        const isAuthor = !!userInfo.value.isAuthor
        userInfo.value = { ...val, isAuthor, parentAccessToken: userInfo.value.parentAccessToken }
        setToken(token, refreshToken, null)
      }
    }

    const setToken = (token, refreshToken, parentAccessToken) => {
      userInfo.value.token = token
      userInfo.value.refreshToken = refreshToken
      if (parentAccessToken) {
        userInfo.value.parentAccessToken = parentAccessToken
      }
    }
    const changeRole = (isAuthor: boolean) => {
      userInfo.value.isAuthor = isAuthor
    }

    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }
    // 一般没有reset需求，不需要的可以删除
    const reset = () => {
      userInfo.value = { ...initState }
    }

    // 获取并更新最新的用户信息
    const getUserInfo = async () => {
      try {
        const userData = await getUserInfo()
        if (userData) {
          setUserInfo(userData)
          return userData
        }
        return userInfo.value
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return userInfo.value
      }
    }

    const isLogined = computed(() => !!userInfo.value.token)

    const isAuthor = computed(() => !!userInfo.value.isAuthor)

    const matchCategories = computed(() =>
      userInfo.value.competitionIds ? userInfo.value.competitionIds.split(',').map(Number) : [],
    )

    return {
      userInfo,
      setToken,
      setUserInfo,
      clearUserInfo,
      isLogined,
      isAuthor,
      matchCategories,
      changeRole,
      reset,
      getUserInfo,
    }
  },
  {
    persist: true,
  },
)
