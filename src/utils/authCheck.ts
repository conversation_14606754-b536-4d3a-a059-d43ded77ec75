// const authCheckPath = ['/pages/detail/index']
const authCheckPath = []
const salt = 'Yfm##v7H^6(~sQ7!zTc!@259lc9'
import { SHA256 } from './sha256'
import { useAuthStore } from '@/store/auth'

enum AuthType {
  SHARE = 'share',
  NORMAL = 'normal',
  AUTH = 'auth',
}

const shareTimeOut = 1000 * 60 * 10 // 10分钟

export const generateSign = (path, ts, op) => {
  const str = `${path}${ts}${op}${salt}`
  return SHA256(str)
}

export const authCheck = async (query) => {
  const pathname = location.pathname
  if (authCheckPath.includes(pathname)) {
    console.log('当前页面需要校验权限', pathname)

    // 已经验证过的就无需校验了
    if (useAuthStore().auth == 1) {
      return
    }
    console.log('请求参数', query)
    const checkResult = await check(query, pathname)
    const op = query.op
    if (checkResult) {
      // 校验成功
      switch (op) {
        case AuthType.SHARE:
          break
        case AuthType.NORMAL:
          break
        case AuthType.AUTH:
          useAuthStore().setAuth(1)
          break
        default:
          go404()
      }
    } else {
      go404()
    }
  }
}

const go404 = () => {
  uni.redirectTo({
    url: '/pages/error/404',
  })
}

const check = async (query, pathname) => {
  const ts = query.ts
  const op = query.op
  const sign = query.sign

  const str = `${pathname}${ts}${op}${salt}`

  const checkSign = SHA256(str)
  if (checkSign === sign) {
    console.log('校验通过')

    if (new Date().getTime() - ts > shareTimeOut && op === AuthType.SHARE) {
      console.log('分享链接已过期')
      return false
    }

    return true
  } else {
    console.log('校验失败')
    return false
  }
}

export const urlFilter = (url) => {
  let path

  if (url.includes('?')) {
    const currentPath = url.split('?')[0]
    path = currentPath
  } else {
    path = url
  }

  if (authCheckPath.includes(path)) {
    if (url.includes('?')) {
      // 有参数再进行参数判断

      const query = url.split('?')[1]
      const queryArr = query.split('&')
      // 查询其中是否有 ts op sign
      const ts = queryArr.find((item) => item.split('=')[0] === 'ts')
      const op = queryArr.find((item) => item.split('=')[0] === 'op')
      const sign = queryArr.find((item) => item.split('=')[0] === 'sign')

      if (ts && op && sign) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  } else {
    return true
  }
}

export const urlCheckAction = (url) => {
  let path

  if (url.includes('?')) {
    const currentPath = url.split('?')[0]
    path = currentPath
  } else {
    path = url
  }

  let queryList = []

  // 判断有没有参数
  if (url.includes('?')) {
    const query = url.split('?')[1]
    const queryArr = query.split('&')

    for (let i = 0; i < queryArr.length; i++) {
      const item = queryArr[i].split('=')
      if (item[0] === 'ts' || item[0] === 'op' || item[0] === 'sign') {
        continue
      } else {
        queryList.push(queryArr[i])
      }
    }
  }

  const ts = new Date().getTime()
  const op = AuthType.NORMAL
  const sign = SHA256(`${path}${ts}${op}${salt}`)

  let newUrl = `${path}?ts=${ts}&op=${op}&sign=${sign}`

  if (queryList.length > 0) {
    newUrl += '&' + queryList.join('&')
  }

  return newUrl
}
