import { useUserStore } from '@/store'
import qs from 'qs'
import { isEmpty } from 'lodash-es'
import { getNeedLoginPages, needLoginPages } from './index'

export function isLogined() {
  const userStore = useUserStore()
  return userStore.isLogined
}

export function getQueryObject() {
  const [_, queryString] = window.location.href.split('?')
  return queryString ? qs.parse(queryString) : null
}
export function getQueryVal(key: string) {
  const queryObject = getQueryObject()
  return isEmpty(queryObject) ? null : queryObject[key]
}

function isNeedLoginPage() {
  const allNeedLoginPages: string[] = import.meta.env.DEV ? getNeedLoginPages() : needLoginPages()
  const currentPath = getCurrentPath()
  return allNeedLoginPages.includes(currentPath)
}

/**
 * 获取当前页面地址
 * @param querystring 是否带查询参数
 * @returns 当前页面路径
 */
export function getCurrentPath(querystring = false) {
  const pages = getCurrentPages()
  const page = pages[pages.length - 1]

  const route = page.route

  if (querystring) {
    // @ts-ignore
    const options = page.options
    const queryStr: null | string = isEmpty(options) ? '' : qs.stringify(options)
    return `/${route}?${queryStr}`
  }

  return `/${route}`
}
