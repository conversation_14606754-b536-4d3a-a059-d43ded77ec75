import { LIVE_INFO_TYPE } from './enum'

export const MATCH_TABS = ['关注', '足球']

export const WEEK_CN = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

export const MATCH_STATUS_TXT = [
  '比赛异常',
  '未开赛',
  '上半场',
  '中场',
  '下半场',
  '加时赛',
  '加时赛(弃用)',
  '点球决战',
  '完场',
  '推迟',
  '中断',
  '腰斩',
  '取消',
  '待定',
]

export const DEFAULT_PAGE_SIZE = 10

/* 比赛详情页刷新间隔30s */
export const MATCH_DETAIL_INTERVAL = 30000

export const DEFAULT_MATCH_CATEGORIES = [
  { id: -1, name: '全部' },
  { id: 0, name: '竞足' },
  { id: 46, name: '欧冠' },
  { id: 82, name: '英超' },
  { id: 120, name: '西甲' },
  { id: 129, name: '德甲' },
  { id: 108, name: '意甲' },
  { id: 142, name: '法甲' },
]

export const MATCH_LIVE_ICONS = [
  { id: LIVE_INFO_TYPE.GOAL, url: 'https://sacdn.850g.com/football/static/icons/match/goal.svg' },
  {
    id: LIVE_INFO_TYPE.CORNER_KICK,
    url: 'https://sacdn.850g.com/football/static/icons/match/corner-kick.svg',
  },
  {
    id: LIVE_INFO_TYPE.YELLOW_CARD,
    url: 'https://sacdn.850g.com/football/static/icons/match/yellow-card.svg',
  },
  {
    id: LIVE_INFO_TYPE.RED_CARD,
    url: 'https://sacdn.850g.com/football/static/icons/match/red-card.svg',
  },
  {
    id: LIVE_INFO_TYPE.OFFSIDE,
    url: 'https://sacdn.850g.com/football/static/icons/match/offside.svg',
  },
  {
    id: LIVE_INFO_TYPE.FREE_KICK,
    url: 'https://sacdn.850g.com/football/static/icons/match/free-kick.svg',
  },
  {
    id: LIVE_INFO_TYPE.GOAL_KICK,
    url: 'https://sacdn.850g.com/football/static/icons/match/goal-kick.svg',
  },
  {
    id: LIVE_INFO_TYPE.PENALTY_KICK,
    url: 'https://sacdn.850g.com/football/static/icons/match/penalty-kick.svg',
  },
  {
    id: LIVE_INFO_TYPE.SUBSTITUTION,
    url: 'https://sacdn.850g.com/football/static/icons/match/substitution.svg',
  },
  {
    id: LIVE_INFO_TYPE.TWO_YELLOWS_TURN_RED,
    url: 'https://sacdn.850g.com/football/static/icons/match/two-yellow.svg',
  },
  {
    id: LIVE_INFO_TYPE.MISSED_PENALTY_KICK,
    url: 'https://sacdn.850g.com/football/static/icons/match/missed-penalty.svg',
  }, // 点球未进
  {
    id: LIVE_INFO_TYPE.MISSED_PENALTY,
    url: 'https://sacdn.850g.com/football/static/icons/match/missed-penalty.svg',
  }, // 点球未进(点球大战)
  {
    id: LIVE_INFO_TYPE.OWN_GOAL,
    url: 'https://sacdn.850g.com/football/static/icons/match/own-goal.svg',
  },
  {
    id: LIVE_INFO_TYPE.ASSIST,
    url: 'https://sacdn.850g.com/football/static/icons/match/assist.svg',
  },
  {
    id: LIVE_INFO_TYPE.INJURY_TIME,
    url: 'https://sacdn.850g.com/football/static/icons/match/injury.svg',
  },
  {
    id: LIVE_INFO_TYPE.SHOT_ON_TARGET,
    url: 'https://sacdn.850g.com/football/static/icons/match/shot-on-target.svg',
  },
  {
    id: LIVE_INFO_TYPE.SHOT_OFF_TARGET,
    url: 'https://sacdn.850g.com/football/static/icons/match/shot-off-target.svg',
  },
  {
    id: LIVE_INFO_TYPE.ATTACK,
    url: 'https://sacdn.850g.com/football/static/icons/match/attack.svg',
  },
  {
    id: LIVE_INFO_TYPE.DANGEROUS_ATTACK,
    url: 'https://sacdn.850g.com/football/static/icons/match/dangerous-attack.svg',
  },
  {
    id: LIVE_INFO_TYPE.POSSESSION_RATE,
    url: 'https://sacdn.850g.com/football/static/icons/match/possession-rate.svg',
  },
  { id: LIVE_INFO_TYPE.VAR, url: 'https://sacdn.850g.com/football/static/icons/match/var.svg' },
]

export const INCIDENT_TYPE = [
  '未知',
  '犯规',
  '个人犯规',
  '侵犯对手(罚牌事件)/受伤换人(换人事件)',
  '战术犯规(罚牌事件)/战术换人(换人事件)',
  '进攻犯规',
  '无球犯规',
  '持续犯规',
  '持续侵犯',
  '暴力行为',
  '危险动作',
  '手球犯规',
  '严重犯规',
  '故意犯规(防守球员为最后一名防守人时)',
  '阻挡进球机会',
  '拖延时间',
  '视频回看裁定',
  '判罚取消',
  '争论',
  '对判罚表达异议',
  '犯规和攻击言语',
  '过度庆祝',
  '没有回退到要求的距离',
  '打架',
  '辅助判罚',
  '替补席',
  '赛后行为',
  '其他原因',
  '未被允许进入场地',
  '进入比赛场地',
  '离开比赛赛场',
  '非体育道德行为',
  '非主观意愿的恶意犯规',
  '假摔',
  '干预var复审',
  '进入裁判评审区',
  '吐口水(向球员或裁判)',
  '病毒',
]

export const PREFERENTIAL = [
  { label: '都不选', value: 0 },
  { label: '可补单', value: 1 },
  { label: '不中即退', value: 2 },
]

export const BF_SCORES = [
  '1:0',
  '2:0',
  '2:1',
  '3:0',
  '3:1',
  '3:2',
  '4:0',
  '4:1',
  '4:2',
  '5:0',
  '5:1',
  '5:2',
  '胜其他',
  '0:0',
  '1:1',
  '2:2',
  '3:3',
  '平其他',
  '0:1',
  '0:2',
  '1:2',
  '0:3',
  '1:3',
  '2:3',
  '0:4',
  '1:4',
  '2:4',
  '0:5',
  '1:5',
  '2:5',
  '负其他',
]

export const BQC_SCORE = ['胜胜', '胜平', '胜负', '平胜', '平平', '平负', '负胜', '负平', '负负']

export const JQ_SCORE = ['0', '1', '2', '3', '4', '5', '6', '+7']

export const ARTICLE_PRICE_TEMPLATE = ['0', '88', '128', '168', '208']

export const SCHEME_TYPE_TXT = [
  '',
  '14场',
  '任9',
  '二串一',
  '竞足单关',
  '多串一',
  '单关',
  '单关(野味)',
]

export const PLAY_TYPE_MAP = [
  '',
  '',
  '让球胜平负',
  '胜平负',
  '比分',
  '半全场',
  '总进球数',
  '让球',
  '大小球',
]

export const GAME_PLAY_TYPE_TXT = ['胜平负', '比分', '让球胜平负', '半全场', '进球']

export const HOME_ARTICLE_CATEGORY_TXT = [
  '单关推荐',
  '2串1专区',
  '临场推荐',
  '首单不中退',
  '免费专区',
]

/* 验证码获取倒计时(60秒) */
export const VERIFY_CODE_CUTDOWN = 60

export const DATE_TYPE = ['今日', '本周', '本月']
