export enum PAY_TYPE {
  CURRENCY = 0,
  ALIPAY = 1,
  WECHAT = 2,
  PRIVILEGE_NUMBER = 3,
  PRIVILEGE_TIME = 4,
  PRIVILEGE_REPAIR = 5,
  PRIVILEGE_PACKAGE = 7,
}

export enum ORDER_STATUS {
  PENDING = 0,
  SUCCESS = 1,
  FAIL = 2,
}

export enum ARTICLE_TYPE {
  MY_FOCUSED,
  HOT,
}

export enum ARTICLE_SALE_TYPE {
  ON_SALE,
  PACKAGE,
}

export enum ARTICLE_FILTER_TYPE {
  LATEST,
  HISTORY,
}

export enum WIN_TYPE {
  NO_END = 0, // 未结束
  RED = 1, // 红
  BLACK = 2, // 黑
  FLOW = 3, // 走水
  TWO_IN_ONE = 4, // 二中一
  THREE_IN_TWO = 5, // 三中二
  FOUR_IN_THREE = 6, // 四中三
  KILLED = 7, // 被绝杀
  CUSTOM = 8, // 自定义
}

export enum PRIVILEGE_TYPE {
  TIME,
  NUMBER,
}

export const enum JUDGE_TYPE {
  JUDGED = 1,
  UNJUDGED = 0,
}

export enum MODAL_TYPE {
  PUSH,
  PUBLISH,
}

/* 比赛状态 */
export enum MATCH_STATUS {
  EXCEPTION = 0, // 比赛异常
  NOT_STARTED, // 未开赛
  FIRST_HALF, // 上半场
  HALF_TIME, // 中场
  SECOND_HALF, // 下半场
  EXTRA_TIME, // 加时赛
  EXTRA_TIME_DEPRECATED, // 加时赛(弃用)
  PENALTY_SHOOTOUT, // 点球决战
  FULL_TIME, // 完场
  DELAYED, // 推迟
  INTERRUPTED, // 中断
  ABANDONED, // 腰斩
  CANCELLED, // 取消
  PENDING, // 待定
}

/* 赛事详情tab */
export enum MATCH_DETAIL_TYPE {
  STATUS = 'status',
  LINEUP = 'lineup',
  SCHEME = 'plan',
}

export enum LIVE_INFO_TYPE {
  GOAL = 1, // 进球
  CORNER_KICK = 2, // 角球corner kick
  YELLOW_CARD = 3, // 黄牌
  RED_CARD = 4, // 红牌
  OFFSIDE = 5, // 越位
  FREE_KICK = 6, // 任意球
  GOAL_KICK = 7, // 球门球
  PENALTY_KICK = 8, // 点球
  SUBSTITUTION = 9, // 换人
  KICK_OFF = 10, // 比赛开始
  HALF_TIME = 11, // 中场
  END_OF_MATCH = 12, // 结束
  HALF_TIME_SCORE = 13, // 半场比分Two yellows turn red
  TWO_YELLOWS_TURN_RED = 15, // 两黄变红
  MISSED_PENALTY_KICK = 16, // 点球未进
  OWN_GOAL = 17, // 乌龙球
  ASSIST = 18, // 助攻
  INJURY_TIME = 19, // 伤停补时
  SHOT_ON_TARGET = 21, // 射正
  SHOT_OFF_TARGET = 22, // 射偏
  ATTACK = 23, // 进攻
  DANGEROUS_ATTACK = 24, // 危险进攻
  POSSESSION_RATE = 25, // 控球率
  END_OF_EXTRA = 26, // 加时赛结束
  END_OF_PENALTY_SHOOT = 27, // 点球大战结束
  VAR = 28, // VAR(视频助理裁判)
  PENALTY = 29, // 点球(点球大战)
  MISSED_PENALTY = 30, // 点球未进(点球大战)
}

export enum PLAY_TYPE {
  HANDICAP_WIN_LOSE_DRAW = 2, // 让球胜平负
  WIN_LOSE_DRAW = 3, // 胜平负
  SCORE = 4, // 比分
  BQC = 5, // 半全
  JQ = 6, // 总进球数
  RQ = 7, // 让球
  DXQ = 8, // 大小球
}

/* 组合玩法类型 */
export enum GAME_PLAY_TYPE {
  WIN_LOSE_DRAW = 1, // 胜负平
  SCORE = 2, // 比分
  HANDICAP_WIN_LOSE_DRAW = 3, // 让球胜负平
  BQC = 4, // 半全场
  JQ = 5, // 进球
}

/* 比赛结果 */
export enum MATCH_RESULT {
  LOSE = 0,
  DRAW = 1,
  WIN = 3,
}

/* 玩法类型 */
export enum SCHEME_TYPE {
  MATCH_LOTTERY = 1, // 14场
  ANY_NINE, // 任9
  TEAM_PARLAY, // 二串一
  SINGLE, // 竞足单关
  MULTI_TEAM_PARLAY, // 多串一
  SINGLE_GAME_BET, // 单关
  TWO_SELECTIONS_PARLAY = 10, // 足球2串1 two selections parlay
  MULTIPLE_SELECTIONS_PARLAY = 11, // 足球多串1 multiple selections parlay
}

/* 文章类型 */
export enum HOME_ARTICLE_TYPE {
  MATCH_TIME = 0,
  HOT,
  ACCURACY,
  NEW,
}

/* 关注作家类型 */
export enum FOCUSED_AUTHOR_TYPE {
  ALL = 0, // 全部
  FOCUSED, // 已关注
  HOT, // 热门作者
  HIGH_HIT, // 高命中
}

export enum HOME_PAGE_MODE {
  SEARCH,
  AUTHOR,
  ARTICLE,
  MAIN,
}

export enum HOME_ARTICLE_PLAY {
  SINGLE_GAME_BET = 0, // 单关
  TEAM_PARLAY, // 2串1
  ON_SITE_RECOMMENDATION, // 临场推荐
  REFUND, // 首单不中即退
  FREE, // 免费
}

export enum SCHEME_CATEGORY {
  JZ, // 竟足
  MATCH_LOTTERY, // 14场/任九
  BD, // 北单
  ANY_NINE,
  EXPERT_MANAGEMENT, // 专家管理
  FREESTYLE, // 自由发布
}

export enum SCHEME_STEP {
  SCHEME,
  CONTENT,
  EXPERT_MANAGEMENT
}

export enum DATA_TYPE {
  FOURTY_NINE = 1,
  BD = 2,
  JZ = 3,
  EXPERT_MANAGEMENT, // 专家管理
}
