import dayjs, { ConfigType } from 'dayjs'

import { WEEK_CN } from './constant'
// import { CHINESE_CHARACTER } from './regex'

export const DEFAULT_DATE_FORMATER = 'YYYY-MM-DD'
export const DEFAULT_DATETIME = 'YYYY-MM-DD HH:mm:ss'
export const DATE_TIME_FORMATER_CN = 'M月D日 HH:mm'
export const DEFAULT_DATETIME_FORMATTER = 'YYYY-MM-DD HH:mm'
export function format(datetime: ConfigType, template: string) {
  return dayjs(datetime).format(template)
}

export function formatDate(date: ConfigType) {
  return format(date, DEFAULT_DATE_FORMATER)
}

export function timeToTimestamp(time: string) {
  const dateStr = formatDate(new Date())
  return dayjs(`${dateStr} ${time}`).valueOf()
}

export function formatDataTime(dt: ConfigType) {
  return format(dt, DEFAULT_DATETIME)
}

export function formatWeek(date: ConfigType) {
  return WEEK_CN[dayjs(date).day()]
}

export function formatDateTime(datetime: ConfigType) {
  return format(datetime, DEFAULT_DATETIME_FORMATTER)
}
