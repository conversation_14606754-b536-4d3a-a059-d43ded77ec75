import { CustomRequestOptions } from '@/interceptors/request'
import { useUserStore } from '@/store'
import { getWXH5LoginCode } from './wxh5Login'
import { getCurrentPath } from './authentication'
import { getMiniLoginCode } from './miniLogin'
import { getLastPage } from './index'

export const http = <T>(options: CustomRequestOptions) => {
  const userStore = useUserStore()
  // 1. 返回 Promise 对象
  return new Promise<T>((resolve, reject) => {
    uni.request({
      ...options,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res: any) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          if (res.data.code !== 0) {
            if (res.data.code === 401) {
              userStore.clearUserInfo()
              const platform = uni.getSystemInfoSync().uniPlatform
              const localUrl = getLastPage() as string
              let route = localUrl.route
              if (platform === 'mp-weixin') {
                // 小程序环境
                if (!route.startsWith('/')) {
                  route = '/' + route
                }
                getMiniLoginCode(route)
              } else {
                const currentPath = getCurrentPath(true)
                uni.showModal({
                  title: '提示',
                  content: '请先登录',
                  success: async (res) => {
                    if (res.confirm) {
                      const url = `${window.location.origin}${currentPath}`
                      getWXH5LoginCode(url)
                    } else {
                      console.log('login cancel', route)
                      if (route === 'pages/article/relaese/index') {
                        uni.switchTab({ url: '/pages/index/index' })
                      }
                    }
                  },
                })
              }
            } else {
              uni.showToast({
                icon: 'none',
                title: res.data.msg || '请求错误',
              })
              reject(res)
              return
            }
          }

          // 2.1 提取核心数据 res.data
          resolve(res.data.data as T)
        } else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          userStore.clearUserInfo()
          // uni.navigateTo({ url: '/pages/login/login' })
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: (res.data as IResData<T>).msg || '请求错误',
            })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
          duration: 3000,
        })
        reject(err)
      },
    })
  })
}

/**
 * GET 请求
 * @param url 后台地址
 * @param query 请求query参数
 * @returns
 */
export const httpGet = <T>(url: string, query?: Record<string, any>) => {
  return http<T>({
    url,
    query,
    method: 'GET',
  })
}

/**
 * POST 请求
 * @param url 后台地址
 * @param data 请求body参数
 * @param query 请求query参数，post请求也支持query，很多微信接口都需要
 * @returns
 */
export const httpPost = <T>(
  url: string,
  data?: Record<string, any>,
  query?: Record<string, any>,
) => {
  return http<T>({
    url,
    query,
    data,
    method: 'POST',
  })
}

http.get = httpGet
http.post = httpPost
