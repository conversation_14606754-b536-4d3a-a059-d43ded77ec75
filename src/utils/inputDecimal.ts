import type { Directive, App } from 'vue'

/**
 * 输入框文本输入限制
 */
export const keepTwoDecimal: Directive = {
  mounted(el, binding) {
    el.addEventListener(
      'input',
      (e: any) => {
        try {
          let value = e.target.value
          // 1. 当第一位为0时，只能输入小数点【第二位必须是小数点】
          if (value.charAt(0) === '0' && value.length > 1 && value.charAt(1) !== '.') {
            value = '0'
          } else if (value.length === 1 && value.charAt(0) === '.') {
            // 如果第一位输入的是小数点，则直接置空
            value = ''
          } else {
            // 当input的type不为text时，输入小数时，光标会前移，此处需要手动将光标后移(虽然此处的代码抛出了异常，但是解决了光标前移的问题)
            if (e.data === '.') {
              e.target.selectionStart = value.length + 1
              e.target.selectionEnd = value.length + 1
            }
            value = value.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
            value = value.replace(/\.{2,}/g, '.') // 只保留第一个. 清除多余的
            value = value.match(/^\d*(\.?\d{0,2})/g)![0] || '' // 保留2位小数
          }
          e.target.value = value
        } catch (e) {
          throw new Error('输入时发生异常！')
        }
      },
      true,
    ) // 由于uni-app中渲染原生的input时，增加了dom解构，此处无法直接获取input标签的输入事件，故设置为true，添加向下捕获事件
  },
}

export function setupInputDirective(app: App) {
  app.directive('input-decimal', keepTwoDecimal)
}
