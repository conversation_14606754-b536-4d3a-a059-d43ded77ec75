import { miniLogin } from '@/service/login'
import { useUserStore } from '@/store'

export const getMiniLoginCode = async (route) => {
  const userStore = useUserStore()
  uni.login({
    provider: 'weixin',
    success: async (loginRes) => {
      if (loginRes.code) {
        // 获取到code后
        const loginResult = await miniLogin(loginRes.code)
        console.log('登录结果', loginResult)

        userStore.setToken(
          loginResult.accessToken.accessToken,
          loginResult.accessToken.refreshToken,
        )
        uni.switchTab({
          url: route,
        })
      } else {
        uni.showToast({ title: '获取用户登录态失败！' + loginRes.errMsg, icon: 'none' })
      }
    },
  })
}
