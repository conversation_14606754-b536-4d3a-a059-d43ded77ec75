export const FLOAT_REG = /^-?\d+(\.\d+)?$/
export const NONE_NEGATIVE_FLOAT_WITH_BLANK = /^(0|[1-9])[0-9]*(\.[0-9]*)?$|^$/

export const NONE_NEGATIVE_FLOAT_WITH_BLANK_VALIDATION = {
  pattern: NONE_NEGATIVE_FLOAT_WITH_BLANK,
  message: '请输入有效数字',
}

/* 中文字符 */
export const CHINESE_CHARACTER = /[\u4e00-\u9fa5]+/g

/* 中文姓名 */
export const CHINESE_NAME = /^(?:[\u4e00-\u9fa5·]{2,16})$/

/* 身份证 */
export const ID_CARD_REG =
  /(^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}$)/

/* 手机 */

export const PHONE_REG = /^1[3-9]\d{9}$/

/* 银行卡号 */
export const BANK_CARD_REG = /^[1-9]\d{9,29}$/
