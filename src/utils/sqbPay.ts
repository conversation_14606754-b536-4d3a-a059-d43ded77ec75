const cleanParams = [
  'sn',
  'terminal_sn',
  'client_sn',
  'total_amount',
  'subject',
  'operator',
  'is_success',
  'status',
  'error_code',
  'error_message',
  'standard_error_code',
]

export const cleanUrl = (url: string) => {
  const [baseUrl, params] = url.split('?')
  const paramsList = params?.split('&')
  const cleanParamsList = []
  paramsList?.forEach((param) => {
    const [key, value] = param.split('=')
    if (!cleanParams.includes(key)) {
      cleanParamsList.push(param)
    }
  })

  return `${baseUrl}${cleanParamsList.length > 0 ? '?' : ''}${cleanParamsList.join('&')}`
}
