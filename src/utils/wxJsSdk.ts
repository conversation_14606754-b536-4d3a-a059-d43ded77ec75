import { getWxJsConfig } from '@/api/wx'
import wx from 'weixin-js-sdk'

// 分享配置接口
export interface ShareConfig {
  title: string
  desc?: string
  link: string
  imgUrl: string
  success?: () => void
  cancel?: () => void
}

/**
 * 初始化微信JSSDK并隐藏右上角菜单
 * @param url 当前页面URL，不包含#及其后面部分
 * @param hideMenu 是否隐藏右上角菜单，默认为true
 * @param shareConfig 自定义分享配置，如果提供则会设置自定义分享内容
 */
export function initWxJsSdk(url?: string, hideMenu = true, shareConfig?: ShareConfig) {
  try {
    // 获取当前页面URL,如果有#号需要去掉#及后面的内容
    const currentUrl = url || window.location.href.split('#')[0]

    // 从后端获取签名
    getWxJsConfig(currentUrl)
      .then((res) => {
        wx.config({
          debug: false, // 开启调试模式，方便在手机上查看问题
          appId: res.appId, // 公众号的唯一标识
          timestamp: res.timestamp, // 生成签名的时间戳
          nonceStr: res.nonceStr, // 生成签名的随机串
          signature: res.signature, // 签名
          jsApiList: [
            'updateAppMessageShareData', // 分享给朋友
            'updateTimelineShareData', // 分享到朋友圈
            'onMenuShareTimeline', // 旧版分享到朋友圈
            'onMenuShareAppMessage', // 旧版分享给朋友
            'hideAllNonBaseMenuItem', // 隐藏所有非基础按钮
            'hideMenuItems', // 隐藏某些按钮
            'showMenuItems', // 显示某些按钮
          ],
        })

        wx.ready(() => {
          wx.hideOptionMenu()
        })

        wx.error((res) => {
          console.error('微信JS-SDK配置失败:', res)
        })
      })
      .catch((err) => {
        console.error('获取微信配置失败:', err)
      })
  } catch (error) {
    console.error('初始化微信JSSDK失败:', error)
  }
}
