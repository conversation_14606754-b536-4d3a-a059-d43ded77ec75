export function onBridgeReady(wxJsapiParams, successCallback, errorCallback) {
  WeixinJSBridge.invoke('getBrandWCPayRequest', wxJsapiParams, function (res) {
    // console.log(res.err_msg)
    // uni.showToast({
    //   title: JSON.stringify(res),
    //   icon: 'none',
    // })
    if (res.err_msg === 'get_brand_wcpay_request:ok') {
      console.log('微信支付成功')
      // 支付成功的处理逻辑
      uni.showToast({
        title: '微信支付成功',
        icon: 'none',
      })
      successCallback && successCallback()
    } else if (res.err_msg == 'get_brand_wcpay_request:cancel') {
      console.log('取消支付')
      // 支付失败的处理逻辑
      uni.showToast({
        title: '取消支付',
        icon: 'none',
      })
    } else {
      console.log('支付失败')
      uni.showToast({
        title: '支付失败',
        icon: 'none',
      })
      errorCallback && errorCallback()
    }
  })
}

export function miniProgramPay(sn, merchantCode) {
  const config = {
    appId: import.meta.env.VITE_WX_MINIPROGRAM_APPID,
    path: `pages/halfPayment/index?sn=${sn}&merchantCode=${merchantCode}`,
    success: () => {
      console.log('小程序跳转成功')
    },
    fail: (err) => {
      console.log('小程序跳转失败', err)
    },
  }
  console.log('小程序跳转地址', config.path)
  uni.navigateToMiniProgram(config)
}
