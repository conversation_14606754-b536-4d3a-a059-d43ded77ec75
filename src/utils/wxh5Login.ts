import { getAppid } from '@/service/userService'
export const getWXH5LoginCode = async (redirectUrl: string) => {
  if (redirectUrl === null || redirectUrl === '') {
    redirectUrl = window.location.href
  }

  if (redirectUrl.indexOf('?') > -1) {
    const params = redirectUrl.split('?')[1]
    // 去除参数
    redirectUrl = redirectUrl.split('?')[0]

    const paramArr = params.split('&')
    const paramNeed = []
    for (let i = 0; i < paramArr.length; i++) {
      const param = paramArr[i]
      const key = param.split('=')[0]
      // const value = param.split('=')[1]
      if (key === 'code') {
        continue
      } else {
        paramNeed.push(param)
      }
    }

    if (paramNeed.length > 0) {
      redirectUrl += '?' + paramNeed.join('&')
    }
  }

  const appid = await getAppid()
  console.log(appid)

  const redirectUri = encodeURIComponent(redirectUrl)
  const scope = 'snsapi_userinfo'
  const state = 'STATE'
  console.log(appid, redirectUri, scope, state)
  // 构造授权跳转链接
  const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`

  // 跳转到授权链接
  window.location.href = authUrl
}
